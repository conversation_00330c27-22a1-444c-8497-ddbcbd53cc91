# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.buildlog/
.history
.svn/
build/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/
android/
# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/
finance.code-workspace

# Web related
lib/generated_plugin_registrant.dart

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json
flap_products

# Exceptions to above rules.
!/packages/flutter_tools/test/data/dart_dependencies_test/**/.packages

/android
/ios

.vscode/
lib/flap_dsl_box/
.vscode/settings.json
ios/Runner/GeneratedPluginRegistrant.m
ios/Flutter/Generated.xcconfig
ios/Flutter/flutter_export_environment.sh
ios/Runner/GeneratedPluginRegistrant.h
