# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

财务对账 FFW (waimai_e_fe_flutter_finance) 是美团外卖商家端的财务对账系统，支持多种运行模式：
- **AOT 模式**: 本地开发标准Flutter运行
- **Flap 模式**: 线上混合模式运行
- **FlutterWeb 模式**: Web端运行

## 常用命令

### 开发环境启动
```bash
# 安装依赖
flutter pub get

# Web端开发 (端口9999)
flutter run -d web-server --web-port=9999 --web-renderer=html

# 构建Web版本
flutter build web --release --source-maps --web-renderer html

# 生成Flap代理文件
flutter pub run build_runner build --build-filter='package:waimai_e_fe_flutter_finance/src/common_proxy/**' --verbose
```

### 代码检查和格式化
```bash
# 代码分析
flutter analyze

# 代码格式化
flutter format lib/

# 设置Git钩子 (项目根目录执行)
sh shell/githooks.sh
```

## 项目架构

### 核心文件结构
- `lib/main.dart`: Web版本入口，包含完整的Material App配置
- `lib/main_flap.dart`: Flap模式入口，可通过`useFlap`变量切换调试模式
- `lib/main.route.dart`: 路由配置，使用deferred loading懒加载页面
- `config.json`: 项目配置文件，包含代理设置和API端点

### 关键目录说明
- `lib/src/pages/`: 所有页面组件，包含home、账户信息、订单查询等
- `lib/src/services/api/`: API服务层，按功能模块分类
- `lib/src/services/model/`: 数据模型定义
- `lib/src/components/`: 可复用UI组件
- `lib/src/tools/`: 工具函数，包含请求、日期处理、平台工具等
- `lib/flap_run_proxys/`: Flap代理文件，用于桥接不同Flutter包

### 路由系统
项目使用基于字符串的路由系统，所有路由以`/finance/`为前缀：
- `/finance/home`: 首页
- `/finance/orderQuery`: 订单查询
- `/finance/balanceRecharge`: 余额充值
- `/finance/balanceWithdraw`: 余额提现

路由采用deferred loading机制，支持代码分割和懒加载。

### 网络请求架构
- 基于`wef_network`包进行网络请求
- `lib/src/tools/requests.dart`提供统一的API调用方法
- 支持GET/POST请求，包含通用参数处理
- 区分PC端和移动端的参数获取逻辑

### 多平台适配
- 通过`PlatformTool.isWeb`判断平台类型
- Web端移除路由动画优化性能
- 支持最大宽度限制(1400px)和居中布局
- PC端和移动端使用不同的参数获取策略

### 代码质量控制
- 配置了严格的`analysis_options.yaml`规则
- Git提交前自动执行`flutter analyze`和`flutter format`
- 排除了代理文件目录的静态分析

## 重要注意事项

1. **多模式验证**: 任何代码变更都需要在AOT、Flap、FlutterWeb三种模式下验证
2. **分支策略**: 以master分支为主，所有需求合并前必须验证所有运行模式
3. **相对路径**: lib根目录下引入工程文件只能用相对路径，不能用package方式
4. **Flap调试**: 修改代码后使用cmd + 8重新生成Flap Bundle
5. **Web构建**: 使用`--web-renderer html`参数确保兼容性

## 部署信息

- **Talos发布项**: https://talos.sankuai.com/#/project/8936
- **测试环境**: Web版test环境只能以qa分支发布
- **环境配置**: 根据prod/stage/test环境使用不同的config.json配置