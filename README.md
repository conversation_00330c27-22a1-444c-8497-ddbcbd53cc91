# 财务对账 FFW

## 1. 项目概述

### 项目名称
财务对账 FFW (waimai_e_fe_flutter_finance)

### 项目简介
财务对账项目是原 waimai_mfe_alchemist 重构而来，包含了财务对账 H5 端所有的功能，目前已完成 PC充值，提现，订单查询，订单统计四个页面的复用改造，后续会逐步技术栈统一。

### 主要功能
- PC充值
- 提现
- 订单查询
- 订单统计
- 账户明细
- 余额流水
- 交易详情

### 技术栈说明
- 本地开发以 Flutter AOT 模式运行
- 线上以 Flutter Flap 模式运行
- Web 端是以 Flutter Web 模式运行
- 主要依赖：
  - Flutter SDK (>=2.7.0 <3.0.0)
  - flap: ^0.16.459
  - waimai_e_native_business: ^0.0.34
  - roo_flutter: ^1.5.71
  - mt_flutter_route: ^4.6.18
  - waimai_e_common_utils: ^6.27.0

### 访问地址
#### APP端
- `prod` [线上地址]()

#### PC端
- `prod` [线上地址](https://waimaie.meituan.com/#https://waimaieapp.meituan.com/finance/newfe#/finance/xx)
- `stage` [st 地址](https://e.waimai.st.sankuai.com/?ignoreSetRouterProxy=true#https://proxy.waimai.st.sankuai.com/finance/newfe#/finance/xx)
- `test` [test 地址](http://e.b.waimai.test.sankuai.com/?ignoreSetRouterProxy=true#http://e.platform.proxy.b.waimai.test.sankuai.com/finance/newfe#/finance/xx)

## 2. 快速开始

### 环境要求
- Flutter SDK (>=2.7.0 <3.0.0)
- 商家端App（模拟器版本）：http://hpx.sankuai.com/task/application/16959/10000/873656/artifact

### 安装步骤
1. 安装依赖
```
flutter pub get
```

### 配置说明
项目配置在 `config.json` 文件中

### 运行示例
#### Flap 启动服务
1. 下载模拟器版本的商家端App：http://hpx.sankuai.com/task/application/16959/10000/873656/artifact
2. 左侧边栏以 attach finance 运行 RUN AND DEBUG
3. 有修改触发键盘的 cmd + 8 键，重新生成 Flap Bundle，重复第二步骤看到更新

#### FlutterWeb 启动服务
1. 控制台运行：
```
flutter run -d web-server --web-port=9999 --web-renderer=html
```
2. 浏览器打开：localhost:9999 即可看到页面

## 3. 文档结构

### 目录结构说明
```
waimai_e_fe_flutter_finance
├─ .gitignore
├─ .metadata
├─ README.md
├─ analysis_options.yaml
├─ build.sh
├─ config.json
├─ images/
├─ ios/
├─ lib/
│  ├─ flap_run_proxys/
│  ├─ generated_plugin_registrant.dart
│  ├─ main.dart
│  ├─ main.route.dart
│  ├─ main_flap.dart
│  ├─ run_project_proxy.dart
│  ├─ src/
│  │  ├─ common/
│  │  ├─ components/
│  │  ├─ pages/
│  │  │  ├─ accountDetails/
│  │  │  ├─ accountInfo/
│  │  │  ├─ balanceFlow/
│  │  │  ├─ balanceRecharge/
│  │  │  ├─ balanceWithdraw/
│  │  │  ├─ home/
│  │  │  ├─ orderDetail/
│  │  │  ├─ orderList/
│  │  │  ├─ orderQuery/
│  │  │  └─ ...
│  │  ├─ services/
│  │  │  ├─ api/
│  │  │  ├─ enum/
│  │  │  └─ model/
│  │  └─ tools/
│  └─ waimai_e_fe_flutter_finance.dart
├─ proxy._generator.sh
├─ pubspec.lock
├─ pubspec.yaml
├─ shell/
└─ web/
```

### 核心模块说明
- **lib/main.dart**: 应用入口文件
- **lib/src/pages/**: 包含所有页面组件
- **lib/src/services/**: 包含API服务、枚举和数据模型
- **lib/src/components/**: 包含可复用组件
- **lib/src/tools/**: 包含工具函数和第三方库

### API文档链接
财务对账项目wiki：https://km.sankuai.com/page/*********

### 架构设计文档
- https://km.sankuai.com/page/*********
- https://km.sankuai.com/page/1311640895

## 4. 开发指南

### 开发环境搭建
1. 安装Flutter SDK (>=2.7.0 <3.0.0)
2. 克隆项目代码
3. 运行 `flutter pub get` 安装依赖
4. 按照"快速开始"部分的说明运行项目

### 代码规范
和客户端版本管理不同，项目中以 master 分支为主，所有的需求上线之后都会合并到 master 分支中，同样新需求也需要从该分支 check 出来。

### 提交规范
鉴于这种分支模式，不管是 feature、bugfix 需求，在合并 master 之前，都需要验证 AOT 模式、Flap 模式、FlutterWeb 模式都需要进行验证，防止合到 master 之后，其他需要 check 的代码在某种环境下运行出错。

### 测试规范
在提交代码前，确保在以下环境中进行测试：
- AOT 模式
- Flap 模式
- FlutterWeb 模式

## 5. 部署说明

### 部署环境要求
确保部署环境支持Flutter应用运行

### 部署步骤
#### PC 
Talos 发布项：https://talos.sankuai.com/#/project/8936

项目发布同样分为 Flap2.0 和 Web 端，针对不同的需求进行不同发布项设置。

注：其中 Web 版 test 环境只能以 qa 分支发布，其他分支发布也不会生效。

#### APP
Talos 发布项：https://talos.sankuai.com/#/project/8936

灰度发布文档：https://km.sankuai.com/page/1297467418

### 配置说明
根据不同环境（prod、stage、test）配置相应的部署参数

### 监控说明
请参考项目wiki获取监控相关信息

## 6. 其他说明
如有其他问题，请参考项目wiki或联系项目负责人。