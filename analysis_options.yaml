linter:
  rules:
    - avoid_returning_null_for_void
    - avoid_returning_null_for_future
    - avoid_returning_this
    - curly_braces_in_flow_control_structures
    - avoid_empty_else
    - avoid_slow_async_io
    - camel_case_types
    - non_constant_identifier_names
    # - constant_identifier_names
    - directives_ordering
    - unnecessary_new
    - prefer_final_fields
    - cancel_subscriptions
    - avoid_null_checks_in_equality_operators
    # - sort_constructors_first

analyzer:
  errors:
    invalid_assignment: warning
    dead_code: warning
    deprecated_member_use: warning
    avoid_returning_null: error
    avoid_returning_null_for_future: error
    curly_braces_in_flow_control_structures: error
    avoid_slow_async_io: error
    camel_case_types: error
    non_constant_identifier_names: warning
    directives_ordering: warning
    # constant_identifier_names: error
    unnecessary_new: error
    cancel_subscriptions: error
    null_aware_before_operator: warning
    unused_import: warning
    must_call_super: error
    prefer_final_fields: warning
    must_be_immutable: warning
    unused_local_variable: warning
    # sort_constructors_first: warning
    null_aware_in_logical_operator: warning
    missing_required_param: warning
  exclude:
    - lib/flap_proxys/**.dart
    - lib/flap_run_proxys/
    - lib/src/tools/third_part/date_format/date_format_base.dart