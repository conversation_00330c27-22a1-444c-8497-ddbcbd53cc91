import 'package:file/file.dart';
import 'package:file/local.dart';

const FileSystem fs = LocalFileSystem();
void main(List<String> args) {
  RegExp reg =
      RegExp(r'(http://(p0|p1).meituan.net).*?(.(png|jpg))', multiLine: true);
  List<ImageModel> imageList = [];
  fs
      .directory('${PathSDK.currentPath}/lib')
      .listSync(recursive: true)
      .forEach((item) {
    if (item.basename.endsWith('.dart')) {
      final String content = fs.file(item.absolute.path).readAsStringSync();
      if (content.contains(reg)) {
        reg.allMatches(content).forEach((ele) {
          if (!imageList.any((element) => element.url == ele.group(0))) {
            imageList.add(ImageModel(url: ele.group(0), files: item));
          }
        });
      }
    }
  });
  generatMDFile(imageList);
}

void generatMDFile(List<ImageModel> imageList) {
  String writeStr = '';
  imageList.forEach((ImageModel item) {
    writeStr += '------\n';
    writeStr +=
        '<img style="background: rgba(0,0,0,.05)" src="${item.url}" width = "80" height = "80" alt="${item.files.basename}" align=center /> \n';
    writeStr += '\n';
    writeStr +=
        '### 文件使用路径: ${item.files.absolute.path.substring(item.files.absolute.path.indexOf("/lib"))}\n';
    writeStr += '##### 图片线上地址: ${item.url} \n';
    writeStr += '------\n';
    writeStr += '\n';
  });
  fs.file('${PathSDK.currentPath}/image.md').writeAsStringSync(writeStr);
}

class PathSDK {
  static String currentPath = fs.currentDirectory.absolute.path;
}

class ImageModel {
  ImageModel({String this.url, FileSystemEntity this.files});
  String url;
  FileSystemEntity files;
}
