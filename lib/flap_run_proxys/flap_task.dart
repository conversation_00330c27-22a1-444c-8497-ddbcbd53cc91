import 'package:file/file.dart';
import 'package:file/local.dart';
import 'package:flap/flap.dart';
import 'package:flap/src/proxy/meta_classes/state.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_lx/auto_statistics/statistics_mixin.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';

import 'proxy_packages.dart';

/// pwd 地址 + flap_dsl_box
const String flapDSLBoxPath = '';

void startFlap(int useFlap) {
  if (useFlap == 1) {
    return;
  }
  WidgetsFlutterBinding.ensureInitialized();
  List<String> bundleList = searchBundleToInstall();
  FlapSDK.init(
      flapConfig: FlapEnvironmentConfig.getInstance(bundles: bundleList),
      logHandler: _handleLog);
  // 添加Flap Business Proxy
  _registerFlapProxy();
  BusinessStateMixinProxy.registerMixin();
}

void _registerFlapProxy() {
  // ProxyTypeRegister.instance
  //     .registerProxyPackage(FlapProxyPackageWaimaiEFeFlutterUserComment());

  proxyPacakges.forEach(ProxyTypeRegister.instance.registerProxyPackage);
}

List<String> searchBundleToInstall() {
  const FileSystem fs = LocalFileSystem();
  List<String> bundleList = [];

  fs.directory(flapDSLBoxPath).listSync().forEach((element) {
    bundleList.add(element.path
        .replaceFirst(RegExp(r'.*?(flap_dsl_box)'), 'flap_dsl_box'));
  });
  return bundleList;
}

void _handleLog(String log) {
  if (log != null && log.isNotEmpty) {
    if (kDebugMode) {
      // debugPrint(log);
    }
  }
}

class FlapEnvironmentConfig extends FlapConfig {
  FlapEnvironmentConfig._internal({List<String> bundles}) {
    this.mockBundleList = bundles ?? [];
  }
  factory FlapEnvironmentConfig() {
    return getInstance();
  }
  static FlapEnvironmentConfig _instance;
  static FlapEnvironmentConfig getInstance({List<String> bundles}) {
    if (_instance == null) {
      _instance = FlapEnvironmentConfig._internal(bundles: bundles);
    }
    return _instance;
  }

  List<String> mockBundleList = [];

  String get moduleName => 'waimai_e_fe_flutter_finance';
  String get moduleVersion => '5.23.0';

  /// 配置是否强制读取远程 Bundle，用于调试
  bool debugForceRemoteBundle = true;

  /// 默认mock地址
  String mockKey = '';
  String mockFilePath = '';

  /// 预下载集合
  /// flapBundles结构{'flapId':'mine', 'moduleName':'waimai_e_flutter'}
  List<Map> preDownloadFlapBundles = [];

  /// 手机等级 (ios和Android默认都是中端)
  int phoneModelLevel = 1;

  /// 预加载集合
  List<Map<String, String>> preLoadParamsList = [];
  // 应用上报区分
  String appId = '15';
}

class BusinessStateMixinProxy {
  static final Map<List<String>, FactoryDelegate> _mixinRegistry = {
    ['StatisticsMixin']: (metaClass, pa, na) => FlapStateWithStatisticsMixin(),
    ['RouteLifecycleStateMixin']: (metaClass, pa, na) =>
        FlapStateWithRouteLifecycleStateMixin(),
    ['RouteLifecycleStateMixin', 'StatisticsMixin']: (metaClass, pa, na) =>
        FlapStateWithRouteLifecycleStateMixinStatisticsMixin(),
    ['SingleTickerProviderStateMixin', 'StatisticsMixin']:
        (metaClass, pa, na) =>
            FlapStateWithSingleTickerProviderStateMixinStatisticsMixin(),
    ['TickerProviderStateMixin', 'StatisticsMixin']: (metaClass, pa, na) =>
        FlapStateWithTickerProviderStateMixinStatisticsMixin(),
    ['WidgetsBindingObserver', 'StatisticsMixin']: (metaClass, pa, na) =>
        FlapStateWithStatisticsMixinWidgetsBindingObserver(),
    [
      'AutomaticKeepAliveClientMixin',
      'RouteLifecycleStateMixin'
    ]: (metaClass, pa, na) =>
        FlapStateWithAutomaticKeepAliveClientMixinRouteLifecycleStateMixin(),
    [
      'AutomaticKeepAliveClientMixin',
      'RouteLifecycleStateMixin',
      'TickerProviderStateMixin'
    ]: (metaClass, pa, na) =>
        FlapStateWithAutomaticMixinRouteLifecycleSingleTickerMixin(),
    [
      'SingleTickerProviderStateMixin',
      'RouteLifecycleStateMixin'
    ]: (metaClass, pa, na) =>
        FlapStateWithRouteLifecycleStateMixinSingleTickerProviderStateMixin(),
    ['TickerProviderStateMixin', 'RouteLifecycleStateMixin']:
        (metaClass, pa, na) =>
            FlapStateWithRouteLifecycleStateMixinTickerProviderStateMixin(),
    ['WidgetsBindingObserver', 'RouteLifecycleStateMixin']:
        (metaClass, pa, na) =>
            FlapStateWithRouteLifecycleStateMixinWidgetsBindingObserver(),
    ["RouteAware", "RouteLifecycleStateMixin"]: (metaClass, pa, na) =>
        FlapStateWithRouteLifecycleStateMixinRouteAware(),
  };

  static void registerMixin() {
    _mixinRegistry.forEach((key, value) {
      FlapInstanceFactory.instance.registStateMixinFacotryDelegate(key, value);
    });
  }
}

class FlapStateWithStatisticsMixin extends FlapState with StatisticsMixin {
  @override
  String get cid {
    String tmp = this.getCustomMember('cid');
    return tmp;
  }

  @override
  String get channel {
    String val = super.channel;
    if (this.hasCustomMember('channel')) {
      val = this.getCustomMember('channel');
    }
    return val;
  }

  @override
  Map<String, dynamic> get val {
    Map<String, dynamic> res = super.val;
    if (this.hasCustomMember('val')) {
      res = Map<String, dynamic>.from(this.getCustomMember('val'));
    }
    return res;
  }

  @override
  bool get autoReport {
    bool res = super.autoReport;
    if (this.hasCustomMember('autoReport')) {
      res = this.getCustomMember('autoReport');
    }
    return res;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    Widget widget = this.callCustomMethod('build', [context]);
    return widget;
  }
}

class FlapStateWithRouteLifecycleStateMixin extends FlapState
    with RouteLifecycleStateMixin {
  @override
  void didAppear() {
    if (this.hasCustomMethod('didAppear')) {
      this.callCustomMethod('didAppear', null);
    }
  }

  @override
  void didDisappear() {
    if (this.hasCustomMethod('didDisappear')) {
      this.callCustomMethod('didDisappear', null);
    }
  }
}

class FlapStateWithRouteLifecycleStateMixinStatisticsMixin extends FlapState
    with RouteLifecycleStateMixin, StatisticsMixin {
  @override
  String get cid {
    String tmp = this.getCustomMember('cid');
    return tmp;
  }

  @override
  String get channel {
    String val = super.channel;
    if (this.hasCustomMember('channel')) {
      val = this.getCustomMember('channel');
    }
    return val;
  }

  @override
  Map<String, dynamic> get val {
    Map<String, dynamic> res = super.val;
    if (this.hasCustomMember('val')) {
      res = Map<String, dynamic>.from(this.getCustomMember('val'));
    }
    return res;
  }

  @override
  bool get autoReport {
    bool res = super.autoReport;
    if (this.hasCustomMember('autoReport')) {
      res = this.getCustomMember('autoReport');
    }
    return res;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    Widget widget = this.callCustomMethod('build', [context]);
    return widget;
  }

  @override
  void didAppear() {
    if (this.hasCustomMethod('didAppear')) {
      this.callCustomMethod('didAppear', null);
    }
  }

  @override
  void didDisappear() {
    if (this.hasCustomMethod('didDisappear')) {
      this.callCustomMethod('didDisappear', null);
    }
  }
}

class FlapStateWithSingleTickerProviderStateMixinStatisticsMixin
    extends FlapState with SingleTickerProviderStateMixin, StatisticsMixin {
  @override
  String get cid {
    String tmp = this.getCustomMember('cid');
    return tmp;
  }

  @override
  String get channel {
    String val = super.channel;
    if (this.hasCustomMember('channel')) {
      val = this.getCustomMember('channel');
    }
    return val;
  }

  @override
  Map<String, dynamic> get val {
    Map<String, dynamic> res = super.val;
    if (this.hasCustomMember('val')) {
      res = Map<String, dynamic>.from(this.getCustomMember('val'));
    }
    return res;
  }

  @override
  bool get autoReport {
    bool res = super.autoReport;
    if (this.hasCustomMember('autoReport')) {
      res = this.getCustomMember('autoReport');
    }
    return res;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    Widget widget = this.callCustomMethod('build', [context]);
    return widget;
  }
}

class FlapStateWithTickerProviderStateMixinStatisticsMixin extends FlapState
    with TickerProviderStateMixin, StatisticsMixin {
  @override
  String get cid {
    String tmp = this.getCustomMember('cid');
    return tmp;
  }

  @override
  String get channel {
    String val = super.channel;
    if (this.hasCustomMember('channel')) {
      val = this.getCustomMember('channel');
    }
    return val;
  }

  @override
  Map<String, dynamic> get val {
    Map<String, dynamic> res = super.val;
    if (this.hasCustomMember('val')) {
      res = Map<String, dynamic>.from(this.getCustomMember('val'));
    }
    return res;
  }

  @override
  bool get autoReport {
    bool res = super.autoReport;
    if (this.hasCustomMember('autoReport')) {
      res = this.getCustomMember('autoReport');
    }
    return res;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    Widget widget = this.callCustomMethod('build', [context]);
    return widget;
  }
}

class FlapStateWithStatisticsMixinWidgetsBindingObserver extends FlapState
    with StatisticsMixin, WidgetsBindingObserver {
  @override
  String get cid {
    String tmp = this.getCustomMember('cid');
    return tmp;
  }

  @override
  String get channel {
    String val = super.channel;
    if (this.hasCustomMember('channel')) {
      val = this.getCustomMember('channel');
    }
    return val;
  }

  @override
  Map<String, dynamic> get val {
    Map<String, dynamic> res = super.val;
    if (this.hasCustomMember('val')) {
      res = Map<String, dynamic>.from(this.getCustomMember('val'));
    }
    return res;
  }

  @override
  bool get autoReport {
    bool res = super.autoReport;
    if (this.hasCustomMember('autoReport')) {
      res = this.getCustomMember('autoReport');
    }
    return res;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    Widget widget = this.callCustomMethod('build', [context]);
    return widget;
  }

  @override
  void didChangeAccessibilityFeatures() {
    if (hasCustomMethod('didChangeAccessibilityFeatures')) {
      this.callCustomMethod('didChangeAccessibilityFeatures', null);
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (hasCustomMethod('didChangeAppLifecycleState')) {
      this.callCustomMethod('didChangeAppLifecycleState', [state]);
    }
  }

  @override
  void didChangeLocales(List<Locale> locale) {
    if (hasCustomMethod('didChangeLocales')) {
      this.callCustomMethod('didChangeLocales', [locale]);
    }
  }

  @override
  void didChangeMetrics() {
    if (hasCustomMethod('didChangeMetrics')) {
      this.callCustomMethod('didChangeMetrics', null);
    }
  }

  @override
  void didChangePlatformBrightness() {
    if (hasCustomMethod('didChangePlatformBrightness')) {
      this.callCustomMethod('didChangePlatformBrightness', null);
    }
  }

  @override
  void didChangeTextScaleFactor() {
    if (hasCustomMethod('didChangeTextScaleFactor')) {
      this.callCustomMethod('didChangeTextScaleFactor', null);
    }
  }

  @override
  void didHaveMemoryPressure() {
    if (hasCustomMethod('didHaveMemoryPressure')) {
      this.callCustomMethod('didHaveMemoryPressure', null);
    }
  }
}

class FlapStateWithAutomaticKeepAliveClientMixinRouteLifecycleStateMixin
    extends FlapState
    with AutomaticKeepAliveClientMixin, RouteLifecycleStateMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return this.callCustomMethod('build', [context]);
  }

  @override
  void didAppear() {
    if (this.hasCustomMethod('didAppear')) {
      this.callCustomMethod('didAppear', null);
    }
  }

  @override
  void didDisappear() {
    if (this.hasCustomMethod('didDisappear')) {
      this.callCustomMethod('didDisappear', null);
    }
  }
}

class FlapStateWithRouteLifecycleStateMixinSingleTickerProviderStateMixin
    extends FlapState
    with RouteLifecycleStateMixin, SingleTickerProviderStateMixin {
  @override
  void didAppear() {
    if (this.hasCustomMethod('didAppear')) {
      this.callCustomMethod('didAppear', null);
    }
  }

  @override
  void didDisappear() {
    if (this.hasCustomMethod('didDisappear')) {
      this.callCustomMethod('didDisappear', null);
    }
  }
}

class FlapStateWithAutomaticMixinRouteLifecycleSingleTickerMixin
    extends FlapState
    with
        RouteLifecycleStateMixin,
        TickerProviderStateMixin,
        AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    super.build(context);
    return this.callCustomMethod('build', [context]);
  }

  @override
  void didAppear() {
    if (this.hasCustomMethod('didAppear')) {
      this.callCustomMethod('didAppear', null);
    }
  }

  @override
  void didDisappear() {
    if (this.hasCustomMethod('didDisappear')) {
      this.callCustomMethod('didDisappear', null);
    }
  }

  @override
  bool get wantKeepAlive => true;
}

class FlapStateWithRouteLifecycleStateMixinTickerProviderStateMixin
    extends FlapState with RouteLifecycleStateMixin, TickerProviderStateMixin {
  @override
  void didAppear() {
    if (this.hasCustomMethod('didAppear')) {
      this.callCustomMethod('didAppear', null);
    }
  }

  @override
  void didDisappear() {
    if (this.hasCustomMethod('didDisappear')) {
      this.callCustomMethod('didDisappear', null);
    }
  }
}

class FlapStateWithRouteLifecycleStateMixinWidgetsBindingObserver
    extends FlapState with RouteLifecycleStateMixin, WidgetsBindingObserver {
  @override
  void didAppear() {
    if (this.hasCustomMethod('didAppear')) {
      this.callCustomMethod('didAppear', null);
    }
  }

  @override
  void didDisappear() {
    if (this.hasCustomMethod('didDisappear')) {
      this.callCustomMethod('didDisappear', null);
    }
  }

  @override
  void didChangeAccessibilityFeatures() {
    if (hasCustomMethod('didChangeAccessibilityFeatures')) {
      this.callCustomMethod('didChangeAccessibilityFeatures', null);
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (hasCustomMethod('didChangeAppLifecycleState')) {
      this.callCustomMethod('didChangeAppLifecycleState', [state]);
    }
  }

  @override
  void didChangeLocales(List<Locale> locale) {
    if (hasCustomMethod('didChangeLocales')) {
      this.callCustomMethod('didChangeLocales', [locale]);
    }
  }

  @override
  void didChangeMetrics() {
    if (hasCustomMethod('didChangeMetrics')) {
      this.callCustomMethod('didChangeMetrics', null);
    }
  }

  @override
  void didChangePlatformBrightness() {
    if (hasCustomMethod('didChangePlatformBrightness')) {
      this.callCustomMethod('didChangePlatformBrightness', null);
    }
  }

  @override
  void didChangeTextScaleFactor() {
    if (hasCustomMethod('didChangeTextScaleFactor')) {
      this.callCustomMethod('didChangeTextScaleFactor', null);
    }
  }

  @override
  void didHaveMemoryPressure() {
    if (hasCustomMethod('didHaveMemoryPressure')) {
      this.callCustomMethod('didHaveMemoryPressure', null);
    }
  }
}

class FlapStateWithRouteLifecycleStateMixinRouteAware extends FlapState
    with RouteAware, RouteLifecycleStateMixin {
  @override
  void didAppear() {
    if (this.hasCustomMethod('didAppear')) {
      this.callCustomMethod('didAppear', null);
    }
  }

  @override
  void didDisappear() {
    if (this.hasCustomMethod('didDisappear')) {
      this.callCustomMethod('didDisappear', null);
    }
  }

  @override
  void didPushNext() {
    if (this.hasCustomMethod('didPushNext')) {
      this.callCustomMethod('didPushNext', null);
    }
  }

  @override
  void didPop() {
    if (this.hasCustomMethod('didPop')) {
      this.callCustomMethod('didPop', null);
    }
  }

  @override
  void didPush() {
    if (this.hasCustomMethod('didPush')) {
      this.callCustomMethod('didPush', null);
    }
  }

  @override
  void didPopNext() {
    if (this.hasCustomMethod('didPopNext')) {
      this.callCustomMethod('didPopNext', null);
    }
  }
}
