import 'package:flutter/gestures.dart';
import 'package:flutter/painting.dart';
import 'package:flutter/widgets.dart';

import 'package:flap_proxy/flap_proxy.dart';
import 'package:flutter_easyrefresh/src/behavior/scroll_behavior.dart';
import 'package:flutter_easyrefresh/src/refresher.dart';

class FlapProxyEasyRefresh extends BaseFlapProxy<EasyRefresh> {
  @override
  String getProxyName() {
    return "EasyRefresh";
  }

  @override
  Map<String, Function> constructors() {
    return {
      "": (List pa, Map na) => EasyRefresh(
          behavior: getNa(na, 'behavior',
              defaultValue: const EmptyOverScrollScrollBehavior()),
          bottomBouncing: getNa(na, 'bottomBouncing', defaultValue: true),
          child: na['child'],
          controller: na['controller'],
          emptyWidget: na['emptyWidget'],
          enableControlFinishLoad:
              getNa(na, 'enableControlFinishLoad', defaultValue: false),
          enableControlFinishRefresh:
              getNa(na, 'enableControlFinishRefresh', defaultValue: false),
          firstRefresh: getNa(na, 'firstRefresh', defaultValue: false),
          firstRefreshWidget: na['firstRefreshWidget'],
          footer: na['footer'],
          header: na['header'],
          headerIndex: getNa(na, 'headerIndex', defaultValue: 0),
          key: na['key'],
          onLoad: na['onLoad'] == null ? null : () => na['onLoad'](),
          onRefresh: na['onRefresh'] == null ? null : () => na['onRefresh'](),
          scrollController: na['scrollController'],
          taskIndependence: getNa(na, 'taskIndependence', defaultValue: false),
          topBouncing: getNa(na, 'topBouncing', defaultValue: true)),
      "builder": (List pa, Map na) => EasyRefresh.builder(
          behavior: getNa(na, 'behavior',
              defaultValue: const EmptyOverScrollScrollBehavior()),
          bottomBouncing: getNa(na, 'bottomBouncing', defaultValue: true),
          builder: na['builder'] == null
              ? null
              : (context, physics, header, footer) =>
                  na['builder'](context, physics, header, footer),
          controller: na['controller'],
          enableControlFinishLoad:
              getNa(na, 'enableControlFinishLoad', defaultValue: false),
          enableControlFinishRefresh:
              getNa(na, 'enableControlFinishRefresh', defaultValue: false),
          firstRefresh: getNa(na, 'firstRefresh', defaultValue: false),
          footer: na['footer'],
          header: na['header'],
          key: na['key'],
          onLoad: na['onLoad'] == null ? null : () => na['onLoad'](),
          onRefresh: na['onRefresh'] == null ? null : () => na['onRefresh'](),
          scrollController: na['scrollController'],
          taskIndependence: getNa(na, 'taskIndependence', defaultValue: false),
          topBouncing: getNa(na, 'topBouncing', defaultValue: true)),
      "custom": (List pa, Map na) => EasyRefresh.custom(
          anchor: getNa(na, 'anchor', defaultValue: 0.0)?.toDouble(),
          behavior: getNa(na, 'behavior',
              defaultValue: const EmptyOverScrollScrollBehavior()),
          bottomBouncing: getNa(na, 'bottomBouncing', defaultValue: true),
          cacheExtent: na['cacheExtent']?.toDouble(),
          center: na['center'],
          controller: na['controller'],
          dragStartBehavior: getNa(na, 'dragStartBehavior',
              defaultValue: DragStartBehavior.start),
          emptyWidget: na['emptyWidget'],
          enableControlFinishLoad:
              getNa(na, 'enableControlFinishLoad', defaultValue: false),
          enableControlFinishRefresh:
              getNa(na, 'enableControlFinishRefresh', defaultValue: false),
          firstRefresh: getNa(na, 'firstRefresh', defaultValue: false),
          firstRefreshWidget: na['firstRefreshWidget'],
          footer: na['footer'],
          header: na['header'],
          headerIndex: getNa(na, 'headerIndex', defaultValue: 0),
          key: na['key'],
          listKey: na['listKey'],
          onLoad: na['onLoad'] == null ? null : () => na['onLoad'](),
          onRefresh: na['onRefresh'] == null ? null : () => na['onRefresh'](),
          primary: na['primary'],
          reverse: getNa(na, 'reverse', defaultValue: false),
          scrollController: na['scrollController'],
          scrollDirection:
              getNa(na, 'scrollDirection', defaultValue: Axis.vertical),
          semanticChildCount: na['semanticChildCount'],
          shrinkWrap: getNa(na, 'shrinkWrap', defaultValue: false),
          slivers: na['slivers']?.cast<Widget>(),
          taskIndependence: getNa(na, 'taskIndependence', defaultValue: false),
          topBouncing: getNa(na, 'topBouncing', defaultValue: true))
    };
  }

  @override
  Map<String, Function> methods() {
    return {
      "createState": (EasyRefresh instance, List pa, Map na) =>
          instance.createState()
    };
  }

  @override
  Map<String, Function> getters() {
    return {
      "anchor": (EasyRefresh instance) => instance.anchor,
      "behavior": (EasyRefresh instance) => instance.behavior,
      "bottomBouncing": (EasyRefresh instance) => instance.bottomBouncing,
      "builder": (EasyRefresh instance) => instance.builder,
      "cacheExtent": (EasyRefresh instance) => instance.cacheExtent,
      "center": (EasyRefresh instance) => instance.center,
      "child": (EasyRefresh instance) => instance.child,
      "controller": (EasyRefresh instance) => instance.controller,
      "dragStartBehavior": (EasyRefresh instance) => instance.dragStartBehavior,
      "emptyWidget": (EasyRefresh instance) => instance.emptyWidget,
      "enableControlFinishLoad": (EasyRefresh instance) =>
          instance.enableControlFinishLoad,
      "enableControlFinishRefresh": (EasyRefresh instance) =>
          instance.enableControlFinishRefresh,
      "firstRefresh": (EasyRefresh instance) => instance.firstRefresh,
      "firstRefreshWidget": (EasyRefresh instance) =>
          instance.firstRefreshWidget,
      "footer": (EasyRefresh instance) => instance.footer,
      "header": (EasyRefresh instance) => instance.header,
      "headerIndex": (EasyRefresh instance) => instance.headerIndex,
      "listKey": (EasyRefresh instance) => instance.listKey,
      "onLoad": (EasyRefresh instance) => instance.onLoad,
      "onRefresh": (EasyRefresh instance) => instance.onRefresh,
      "primary": (EasyRefresh instance) => instance.primary,
      "reverse": (EasyRefresh instance) => instance.reverse,
      "scrollController": (EasyRefresh instance) => instance.scrollController,
      "scrollDirection": (EasyRefresh instance) => instance.scrollDirection,
      "semanticChildCount": (EasyRefresh instance) =>
          instance.semanticChildCount,
      "shrinkWrap": (EasyRefresh instance) => instance.shrinkWrap,
      "slivers": (EasyRefresh instance) => instance.slivers,
      "taskIndependence": (EasyRefresh instance) => instance.taskIndependence,
      "topBouncing": (EasyRefresh instance) => instance.topBouncing
    };
  }

  @override
  Map<String, Function> staticGetters() {
    return {"callOverExtent": () => EasyRefresh.callOverExtent};
  }

  @override
  Map<String, Function> staticSetters() {
    return {
      "callOverExtent": (value) => EasyRefresh.callOverExtent = value,
      "defaultFooter": (value) => EasyRefresh.defaultFooter = value,
      "defaultHeader": (value) => EasyRefresh.defaultHeader = value
    };
  }
}
