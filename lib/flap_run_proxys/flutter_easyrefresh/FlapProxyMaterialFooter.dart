import 'dart:core';

import 'package:flap_proxy/flap_proxy.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/material_footer.dart';

class FlapProxyMaterialFooter extends BaseFlapProxy<MaterialFooter> {
  @override
  String getProxyName() {
    return "MaterialFooter";
  }

  @override
  Map<String, Function> constructors() {
    return {
      "": (List pa, Map na) => MaterialFooter(
          backgroundColor: na['backgroundColor'],
          completeDuration: getNa(na, 'completeDuration',
              defaultValue: const Duration(seconds: 1)),
          enableHapticFeedback:
              getNa(na, 'enableHapticFeedback', defaultValue: false),
          enableInfiniteLoad:
              getNa(na, 'enableInfiniteLoad', defaultValue: true),
          key: na['key'],
          overScroll: getNa(na, 'overScroll', defaultValue: false),
          valueColor: AnimationTypeConvert.wrap<Color>(na['valueColor']))
    };
  }

  @override
  Map<String, Function> methods() {
    return {
      "contentBuilder": (MaterialFooter instance, List pa, Map na) =>
          instance.contentBuilder(
              pa[0],
              pa[1],
              pa[2]?.toDouble(),
              pa[3]?.toDouble(),
              pa[4]?.toDouble(),
              pa[5],
              pa[6],
              pa[7],
              pa[8],
              pa[9],
              pa[10])
    };
  }

  @override
  Map<String, Function> getters() {
    return {
      "backgroundColor": (MaterialFooter instance) => instance.backgroundColor,
      "key": (MaterialFooter instance) => instance.key,
      "linkNotifier": (MaterialFooter instance) => instance.linkNotifier,
      "valueColor": (MaterialFooter instance) => instance.valueColor
    };
  }
}
