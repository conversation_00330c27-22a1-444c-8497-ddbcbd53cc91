import 'package:flap_proxy/flap_proxy.dart';
import 'package:flutter_lx/channel/statistics_lx.dart';

class FlapProxyFlutterLx extends BaseFlapProxy<FlutterLx> {
  @override
  String getProxyName() {
    return "FlutterLx";
  }

  @override
  Map<String, Function> constructors() {
    return {"": (List pa, Map na) => FlutterLx()};
  }

  @override
  Map<String, Function> staticMethods() {
    return {
      "clickEvent": (List pa, Map na) => FlutterLx.clickEvent(
          pa[0], pa[1], pa[2], pa[3], getPa(pa, 4)?.cast<String, dynamic>()),
      "moduleEdit": (List pa, Map na) => FlutterLx.moduleEdit(
          pa[0], pa[1], pa[2],
          channel: na['channel'],
          context: na['context'],
          val: na['val']?.cast<String, dynamic>()),
      "moudleClick": (List pa, Map na) => FlutterLx.moudleClick(
          pa[0], pa[1], pa[2],
          channel: na['channel'],
          context: na['context'],
          val: na['val']?.cast<String, dynamic>()),
      "moudleView": (List pa, Map na) => FlutterLx.moudleView(
          pa[0], pa[1], pa[2],
          channel: na['channel'],
          context: na['context'],
          val: na['val']?.cast<String, dynamic>()),
      "pageDidDisappear": (List pa, Map na) => FlutterLx.pageDidDisappear(
          pa[0], pa[1],
          channel: na['channel'],
          context: na['context'],
          val: na['val']?.cast<String, dynamic>()),
      "pageDisappear": (List pa, Map na) => FlutterLx.pageDisappear(
          pa[0], pa[1], pa[2], getPa(pa, 3)?.cast<String, dynamic>()),
      "pageTrack": (List pa, Map na) => FlutterLx.pageTrack(
          pa[0], pa[1], pa[2], getPa(pa, 3)?.cast<String, dynamic>()),
      "pageView": (List pa, Map na) => FlutterLx.pageView(pa[0], pa[1],
          channel: na['channel'],
          context: na['context'],
          val: na['val']?.cast<String, dynamic>()),
      "setBizChannel": (List pa, Map na) => FlutterLx.setBizChannel(pa[0]),
      "setExposureFactor": (List pa, Map na) =>
          FlutterLx.setExposureFactor(pa[0]?.toDouble()),
      "setExposureShortestTime": (List pa, Map na) =>
          FlutterLx.setExposureShortestTime(pa[0]),
      "setupDefaultConfig": (List pa, Map na) => FlutterLx.setupDefaultConfig(
          pa[0], getPa(pa, 1) == null ? null : () => getPa(pa, 1)()),
      "viewTrack": (List pa, Map na) => FlutterLx.viewTrack(
          pa[0], pa[1], pa[2], pa[3], getPa(pa, 4)?.cast<String, dynamic>())
    };
  }

  @override
  Map<String, Function> staticGetters() {
    return {
      "defaultChannel": () => FlutterLx.defaultChannel,
      "defaultValBuilder": () => FlutterLx.defaultValBuilder,
      "exposureFactor": () => FlutterLx.exposureFactor,
      "exposureShortestTime": () => FlutterLx.exposureShortestTime
    };
  }

  @override
  Map<String, Function> staticSetters() {
    return {
      "defaultChannel": (value) => FlutterLx.defaultChannel = value,
      "defaultValBuilder": (value) => FlutterLx.defaultValBuilder = value,
      "exposureFactor": (value) => FlutterLx.exposureFactor = value,
      "exposureShortestTime": (value) => FlutterLx.exposureShortestTime = value
    };
  }
}
