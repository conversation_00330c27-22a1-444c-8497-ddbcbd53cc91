import 'dart:core';

import 'package:flap_proxy/flap_proxy.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';

class FlapProxyKNBCore extends BaseFlapProxy<KNBCore> {
  @override
  String getProxyName() {
    return "KNBCore";
  }

  @override
  Map<String, Function> constructors() {
    return {"": (List pa, Map na) => KNBCore()};
  }

  @override
  Map<String, Function> methods() {
    return {
      "autoLock": (KNBCore instance, List pa, Map na) =>
          instance.autoLock(disable: na['disable']),
      "capture": (KNBCore instance, List pa, Map na) => instance.capture(
          area: getNa(na, 'area', defaultValue: 'screen'),
          returnType: getNa(na, 'returnType', defaultValue: 'localId')),
      "chooseImage": (KNBCore instance, List pa, Map na) =>
          instance.chooseImage(
              count: na['count'],
              ensureUpright: na['ensureUpright'],
              height: na['height'],
              maxHeight: na['maxHeight'],
              maxWidth: na['maxWidth'],
              quality: na['quality'],
              returnType: na['returnType'],
              source: na['source'],
              type: na['type'],
              width: na['width']),
      "clearStorage": (KNBCore instance, List pa, Map na) =>
          instance.clearStorage(key: na['key']),
      "getAppInfo": (KNBCore instance, List pa, Map na) =>
          instance.getAppInfo(),
      "getCity": (KNBCore instance, List pa, Map na) => instance.getCity(),
      "getContactList": (KNBCore instance, List pa, Map na) =>
          instance.getContactList(
              index: getNa(na, 'index', defaultValue: 0),
              limit: getNa(na, 'limit', defaultValue: 0)),
      "getDeviceInfo": (KNBCore instance, List pa, Map na) =>
          instance.getDeviceInfo(),
      "getFingerprint": (KNBCore instance, List pa, Map na) =>
          instance.getFingerprint(),
      "getImageInfo": (KNBCore instance, List pa, Map na) =>
          instance.getImageInfo(image: na['image']),
      "getLocation": (KNBCore instance, List pa, Map na) =>
          instance.getLocation(
              cache: getNa(na, 'cache', defaultValue: true),
              mode: na['mode'],
              timeout: getNa(na, 'timeout', defaultValue: 6000),
              type: na['type']),
      "getMediaFrame": (KNBCore instance, List pa, Map na) =>
          instance.getMediaFrame(
              index: getNa(na, 'index', defaultValue: 1), media: na['media']),
      "getNetworkType": (KNBCore instance, List pa, Map na) =>
          instance.getNetworkType(),
      "getStorage": (KNBCore instance, List pa, Map na) =>
          instance.getStorage(key: na['key']),
      "getUserInfo": (KNBCore instance, List pa, Map na) =>
          instance.getUserInfo(),
      "getWifiInfo": (KNBCore instance, List pa, Map na) =>
          instance.getWifiInfo(),
      "installApp": (KNBCore instance, List pa, Map na) =>
          instance.installApp(target: na['target']),
      "isInstalledApp": (KNBCore instance, List pa, Map na) =>
          instance.isInstalledApp(package: na['package'], scheme: na['scheme']),
      "jumpPage": (KNBCore instance, List pa, Map na) =>
          instance.jumpPage(query: na['query'], url: na['url']),
      "lxlog": (KNBCore instance, List pa, Map na) =>
          instance.lxlog(data: na['data']),
      "noSuchMethod": (KNBCore instance, List pa, Map na) =>
          instance.noSuchMethod(pa[0]),
      "openMiniProgram": (KNBCore instance, List pa, Map na) =>
          instance.openMiniProgram(
              miniProgramId: na['miniProgramId'], path: na['path']),
      "openPage": (KNBCore instance, List pa, Map na) =>
          instance.openPage(query: na['query'], url: na['url']),
      "pickCity": (KNBCore instance, List pa, Map na) => instance.pickCity(
          needResult: getNa(na, 'needResult', defaultValue: 0),
          type: getNa(na, 'type', defaultValue: 0)),
      "pickContact": (KNBCore instance, List pa, Map na) =>
          instance.pickContact(),
      "previewImage": (KNBCore instance, List pa, Map na) =>
          instance.previewImage(
              current: na['current'], urls: na['urls']?.cast<String>()),
      "requestPermission": (KNBCore instance, List pa, Map na) =>
          instance.requestPermission(
              forceJump: getNa(na, 'forceJump', defaultValue: true),
              readonly: getNa(na, 'readonly', defaultValue: false),
              type: getNa(na, 'type', defaultValue: 'notification')),
      "scanQRCode": (KNBCore instance, List pa, Map na) =>
          instance.scanQRCode(needResult: na['needResult']),
      "sendLog": (KNBCore instance, List pa, Map na) =>
          instance.sendLog(text: na['text'], type: na['type']),
      "sendSMS": (KNBCore instance, List pa, Map na) => instance.sendSMS(
          content: na['content'], recipients: na['recipients']),
      "setStorage": (KNBCore instance, List pa, Map na) => instance.setStorage(
          key: na['key'], level: na['level'], value: na['value']),
      "setupEvent": (KNBCore instance, List pa, Map na) => instance.setupEvent(
          alarm: na['alarm'],
          duration: na['duration'],
          start: na['start'],
          title: na['title']),
      "share": (KNBCore instance, List pa, Map na) => instance.share(
          channel: na['channel']?.cast<int>(),
          desc: na['desc'],
          image: na['image'],
          title: na['title'],
          url: na['url']),
      "shareImage": (KNBCore instance, List pa, Map na) => instance.shareImage(
          channel: na['channel'],
          image: na['image'],
          picQuality: na['picQuality'],
          thumbPic: na['thumbPic']),
      "shareMiniProgram": (KNBCore instance, List pa, Map na) =>
          instance.shareMiniProgram(
              content: na['content'],
              image: na['image'],
              miniProgramId: na['miniProgramId'],
              path: na['path'],
              title: na['title'],
              url: na['url']),
      "stopLocating": (KNBCore instance, List pa, Map na) =>
          instance.stopLocating(),
      "toast": (KNBCore instance, List pa, Map na) => instance.toast(pa[0]),
      "uploadImage": (KNBCore instance, List pa, Map na) =>
          instance.uploadImage(
              bucket: na['bucket'],
              clientId: na['clientId'],
              localIds: na['localIds']?.cast<String>(),
              signatureURL: na['signatureURL']),
      "uploadMedia": (KNBCore instance, List pa, Map na) =>
          instance.uploadMedia(
              channel: na['channel'],
              options: na['options'],
              videoId: na['videoId']),
      "use": (KNBCore instance, List pa, Map na) =>
          instance.use(pa[0], pa[1]?.cast<String, dynamic>()),
      "vibrate": (KNBCore instance, List pa, Map na) =>
          instance.vibrate(duration: na['duration'])
    };
  }

  @override
  Map<String, Function> staticMethods() {
    return {
      "getSymbolName": (List pa, Map na) => KNBCore.getSymbolName(pa[0]),
      "methodTemplate": (List pa, Map na) =>
          KNBCore.methodTemplate(pa[0], pa[1]?.cast<String, dynamic>())
    };
  }
}
