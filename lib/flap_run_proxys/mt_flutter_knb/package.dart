import 'package:flap_proxy/flap_proxy.dart';
import 'FlapProxyKNBCore.dart';
import 'global.dart';

class FlapProxyPackageMtFlutterKnb extends BaseFlapProxyPackage {
  @override
  String getPackageName() => 'mt_flutter_knb';

  @override
  Map<String, Function> getGlobalVariables() {
    Map<String, Function> map = {};
    registerKNBVariable(map);
    return map;
  }

  @override
  List<BaseFlapProxy> getProxyList() {
    return [FlapProxyKNBCore()];
  }
}
