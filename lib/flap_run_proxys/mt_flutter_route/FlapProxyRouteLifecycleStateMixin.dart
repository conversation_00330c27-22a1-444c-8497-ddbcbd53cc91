import 'package:flap_proxy/flap_proxy.dart';
import 'package:mt_flutter_route/src/lifecycle/route_lifecycle/route_lifecycle_native.dart';

class FlapProxyRouteLifecycleStateMixin
    extends BaseFlapProxy<RouteLifecycleStateMixin> {
  @override
  String getProxyName() {
    return "RouteLifecycleStateMixin";
  }

  @override
  Map<String, Function> methods() {
    return {
      "didAppear": (RouteLifecycleStateMixin instance, List pa, Map na) =>
          instance.didAppear(),
      "didChangeDependencies":
          (RouteLifecycleStateMixin instance, List pa, Map na) =>
              instance.didChangeDependencies(),
      "didDisappear": (RouteLifecycleStateMixin instance, List pa, Map na) =>
          instance.didDisappear(),
      "dispose": (RouteLifecycleStateMixin instance, List pa, Map na) =>
          instance.dispose(),
      "initState": (RouteLifecycleStateMixin instance, List pa, Map na) =>
          instance.initState()
    };
  }

  @override
  Map<String, Function> getters() {
    return {
      "isActive": (RouteLifecycleStateMixin instance) => instance.isActive
    };
  }
}
