import 'dart:core';

import 'package:flutter/widgets.dart';

import 'package:flap_proxy/flap_proxy.dart';
import 'package:mt_flutter_route/src/basic_types.dart';
import 'package:mt_flutter_route/src/route_utils.dart';

class FlapProxyRouteUtils extends BaseFlapProxy<RouteUtils> {
  @override
  String getProxyName() {
    return "RouteUtils";
  }

  @override
  Map<String, Function> staticMethods() {
    return {
      "close": (List pa, Map na) => RouteUtils.close(pa[0],
          animated: na['animated'],
          resultCode: na['resultCode'],
          resultData: na['resultData']),
      "enableSwipeBack": (List pa, Map na) =>
          RouteUtils.enableSwipeBack(pa[0], pa[1]),
      "flutterUrl": (List pa, Map na) => RouteUtils.flutterUrl(pa[0],
          package: na['package'], params: na['params']?.cast<String, String>()),
      "informationParser": (List pa, Map na) => RouteUtils.informationParser(),
      "initRouter": (List pa, Map na) => RouteUtils.initRouter(
          observers:
              getNa(na, 'observers', defaultValue: const <NavigatorObserver>[])
                  ?.cast<NavigatorObserver>(),
          pages: na['pages']?.cast<String,
              Map<String, Widget Function(Map<String, dynamic>)>>(),
          transitioner: na['transitioner'] == null
              ? null
              : (context, child) => na['transitioner'](context, child)),
      "open": (List pa, Map na) => RouteUtils.open(pa[0],
          context: na['context'],
          data: na['data'],
          ignoreResult: na['ignoreResult'],
          opaque: na['opaque'],
          present: na['present']),
      "publish": (List pa, Map na) => RouteUtils.publish(pa[0], getPa(pa, 1),
          getPa(pa, 2, defaultValue: RouteBroadcastChannel.remote)),
      "receiveEvents": (List pa, Map na) => RouteUtils.receiveEvents(pa[0]),
      "setResult": (List pa, Map na) =>
          RouteUtils.setResult(pa[0], pa[1], data: na['data']),
      "subscribe": (List pa, Map na) => RouteUtils.subscribe(
          pa[0], pa[1] == null ? null : (data) => pa[1](data))
    };
  }
}
