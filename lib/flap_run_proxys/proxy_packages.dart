import 'package:flap_proxy/flap_proxy.dart';
import 'flutter_easyrefresh/package.dart';
import 'flutter_lx/package.dart';
import 'mt_flutter_knb/package.dart';
import 'mt_flutter_route/package.dart';
import 'roo_flutter/package.dart';
import 'toast_widget_flutter/package.dart';
import 'waimai_e_base_ui/package.dart';
import 'waimai_e_common_utils/package.dart';
import 'waimai_e_fe_ffw_utils/package.dart';
import 'waimai_e_native_business/package.dart';
import 'wef_network/package.dart';

List<BaseFlapProxyPackage> proxyPacakges = [
  FlapProxyPackageFlutterEasyrefresh(),
  FlapProxyPackageFlutterLx(),
  FlapProxyPackageMtFlutterKnb(),
  FlapProxyPackageMtFlutterRoute(),
  FlapProxyPackageRooFlutter(),
  FlapProxyPackageToastWidgetFlutter(),
  FlapProxyPackageWaimaiEBaseUi(),
  FlapProxyPackageWaimaiECommonUtils(),
  FlapProxyPackageWaimaiEFeFfwUtils(),
  FlapProxyPackageWaimaiENativeBusiness(),
  FlapProxyPackageWefNetwork()
];
