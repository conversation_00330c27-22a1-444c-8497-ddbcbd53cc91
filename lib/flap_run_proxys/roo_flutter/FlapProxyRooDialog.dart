import 'package:flap_proxy/flap_proxy.dart';
import 'package:flutter/painting.dart';
import 'package:roo_flutter/common/constants.dart';
import 'package:roo_flutter/roo_flutter.dart';

class FlapProxyRooDialog extends BaseFlapProxy<RooDialog> {
  @override
  String getProxyName() {
    return "RooDialog";
  }

  @override
  Map<String, Function> constructors() {
    return {
      "": (List pa, Map na) => RooDialog(pa[0],
          autoClose: getNa(na, 'autoClose', defaultValue: true),
          backgroundColor: na['backgroundColor'],
          cancelAutoClose: getNa(na, 'cancelAutoClose', defaultValue: true),
          cancelCallback: na['cancelCallback'],
          cancelText: na['cancelText'],
          confirmAutoClose: getNa(na, 'confirmAutoClose', defaultValue: true),
          confirmCallback: na['confirmCallback'],
          confirmDisableListenable: na['confirmDisableListenable'],
          confirmText: na['confirmText'],
          content: na['content'],
          contentMaxSize: getNa(na, 'contentMaxSize',
              defaultValue: const Size(242.0, 172.0)),
          contentPadding: na['contentPadding'],
          contentText: na['contentText'],
          contentTextAlign: na['contentTextAlign'],
          contentTextStyle: na['contentTextStyle'],
          defaultInputValue:
              getNa(na, 'defaultInputValue', defaultValue: "请输入内容"),
          descriptionTextOnFooter: na['descriptionTextOnFooter'],
          edgeInsets: na['edgeInsets'],
          elevation:
              getNa(na, 'elevation', defaultValue: kDefaultRooDialogElevation)
                  ?.toDouble(),
          footer: na['footer'],
          head: na['head'],
          headPadding: getNa(na, 'headPadding',
              defaultValue: const EdgeInsets.fromLTRB(0, 24, 0, 0)),
          inputAutoFocus: getNa(na, 'inputAutoFocus', defaultValue: false),
          inputChange: na['inputChange'] == null
              ? null
              : (value) => na['inputChange'](value),
          inputContentPadding: getNa(na, 'inputContentPadding',
              defaultValue: const EdgeInsets.symmetric(horizontal: 11)),
          inputFocus: na['inputFocus'] == null
              ? null
              : (value) => na['inputFocus'](value),
          inputTextStyle: getNa(na, 'inputTextStyle',
              defaultValue: const TextStyle(
                  color: RooColors.grayDarker,
                  fontSize: 14,
                  fontWeight: FontWeight.normal)),
          key: na['key'],
          operationList: na['operationList']?.cast<OperationItem>(),
          shape: na['shape'],
          showInput: getNa(na, 'showInput', defaultValue: false),
          subTitle: na['subTitle'],
          subTitlePadding: na['subTitlePadding'],
          subTitleText: na['subTitleText'],
          subTitleTextStyle: na['subTitleTextStyle'],
          title: na['title'],
          titlePadding: na['titlePadding'],
          titleText: na['titleText'],
          titleTextStyle: na['titleTextStyle']),
      "fromTheme": (List pa, Map na) => RooDialog.fromTheme(pa[0], pa[1],
          autoClose: getNa(na, 'autoClose', defaultValue: true),
          cancelCallback: na['cancelCallback'],
          cancelText: na['cancelText'],
          confirmCallback: na['confirmCallback'],
          confirmText: na['confirmText'],
          content: na['content'],
          descriptionTextOnFooter: na['descriptionTextOnFooter'],
          head: na['head'],
          key: na['key'],
          subTitleText: na['subTitleText'],
          titleText: na['titleText'])
    };
  }

  @override
  Map<String, Function> methods() {
    return {
      "createState": (RooDialog instance, List pa, Map na) =>
          instance.createState()
    };
  }

  @override
  Map<String, Function> getters() {
    return {
      "autoClose": (RooDialog instance) => instance.autoClose,
      "backgroundColor": (RooDialog instance) => instance.backgroundColor,
      "cancelAutoClose": (RooDialog instance) => instance.cancelAutoClose,
      "cancelCallback": (RooDialog instance) => instance.cancelCallback,
      "cancelText": (RooDialog instance) => instance.cancelText,
      "confirmAutoClose": (RooDialog instance) => instance.confirmAutoClose,
      "confirmCallback": (RooDialog instance) => instance.confirmCallback,
      "confirmDisableListenable": (RooDialog instance) =>
          instance.confirmDisableListenable,
      "confirmText": (RooDialog instance) => instance.confirmText,
      "content": (RooDialog instance) => instance.content,
      "contentMaxSize": (RooDialog instance) => instance.contentMaxSize,
      "contentPadding": (RooDialog instance) => instance.contentPadding,
      "contentText": (RooDialog instance) => instance.contentText,
      "contentTextAlign": (RooDialog instance) => instance.contentTextAlign,
      "contentTextStyle": (RooDialog instance) => instance.contentTextStyle,
      "context": (RooDialog instance) => instance.context,
      "defaultContentPadding": (RooDialog instance) =>
          instance.defaultContentPadding,
      "defaultContentTextStyle": (RooDialog instance) =>
          instance.defaultContentTextStyle,
      "defaultInputValue": (RooDialog instance) => instance.defaultInputValue,
      "defaultShape": (RooDialog instance) => instance.defaultShape,
      "defaultTitleTextStyle": (RooDialog instance) =>
          instance.defaultTitleTextStyle,
      "descriptionTextOnFooter": (RooDialog instance) =>
          instance.descriptionTextOnFooter,
      "edgeInsets": (RooDialog instance) => instance.edgeInsets,
      "elevation": (RooDialog instance) => instance.elevation,
      "footer": (RooDialog instance) => instance.footer,
      "head": (RooDialog instance) => instance.head,
      "headPadding": (RooDialog instance) => instance.headPadding,
      "inputAutoFocus": (RooDialog instance) => instance.inputAutoFocus,
      "inputChange": (RooDialog instance) => instance.inputChange,
      "inputContentPadding": (RooDialog instance) =>
          instance.inputContentPadding,
      "inputFocus": (RooDialog instance) => instance.inputFocus,
      "inputTextStyle": (RooDialog instance) => instance.inputTextStyle,
      "operationList": (RooDialog instance) => instance.operationList,
      "shape": (RooDialog instance) => instance.shape,
      "showInput": (RooDialog instance) => instance.showInput,
      "subTitle": (RooDialog instance) => instance.subTitle,
      "subTitlePadding": (RooDialog instance) => instance.subTitlePadding,
      "subTitleText": (RooDialog instance) => instance.subTitleText,
      "subTitleTextStyle": (RooDialog instance) => instance.subTitleTextStyle,
      "title": (RooDialog instance) => instance.title,
      "titlePadding": (RooDialog instance) => instance.titlePadding,
      "titleText": (RooDialog instance) => instance.titleText,
      "titleTextStyle": (RooDialog instance) => instance.titleTextStyle
    };
  }
}
