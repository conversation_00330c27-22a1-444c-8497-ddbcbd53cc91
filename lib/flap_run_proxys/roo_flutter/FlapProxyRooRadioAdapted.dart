import 'package:flap_proxy/flap_proxy.dart';
import 'package:roo_flutter/basic_components/radio/roo_radio.dart';
import 'package:roo_flutter/basic_components/radio_adapted/roo_radio_adapted.dart';

class FlapProxyRooRadioAdapted extends BaseFlapProxy<RooRadioAdapted> {
  @override
  String getProxyName() {
    return "RooRadioAdapted";
  }

  @override
  Map<String, Function> constructors() {
    return {
      "": (List pa, Map na) => RooRadioAdapted(
          appDivider: na['appDivider'],
          checkedValue: getNa(na, 'checkedValue',
              defaultValue: const RooRadioOptionsItem()),
          iconPosition:
              getNa(na, 'iconPosition', defaultValue: RooRadioIconAlign.left),
          iconType:
              getNa(na, 'iconType', defaultValue: RooRadioIconType.circle),
          onChanged:
              na['onChanged'] == null ? null : (pa0) => na['onChanged'](pa0),
          options: na['options']?.cast<RooRadioOptionsItem>(),
          radioStyle: getNa(na, 'radioStyle', defaultValue: const RadioStyle()),
          radioTextStyle:
              getNa(na, 'radioTextStyle', defaultValue: const RadioTextStyle()))
    };
  }

  @override
  Map<String, Function> methods() {
    return {
      "build": (RooRadioAdapted instance, List pa, Map na) =>
          instance.build(pa[0])
    };
  }

  @override
  Map<String, Function> getters() {
    return {
      "appDivider": (RooRadioAdapted instance) => instance.appDivider,
      "checkedValue": (RooRadioAdapted instance) => instance.checkedValue,
      "iconPosition": (RooRadioAdapted instance) => instance.iconPosition,
      "iconType": (RooRadioAdapted instance) => instance.iconType,
      "onChanged": (RooRadioAdapted instance) => instance.onChanged,
      "options": (RooRadioAdapted instance) => instance.options,
      "radioStyle": (RooRadioAdapted instance) => instance.radioStyle,
      "radioTextStyle": (RooRadioAdapted instance) => instance.radioTextStyle
    };
  }
}
