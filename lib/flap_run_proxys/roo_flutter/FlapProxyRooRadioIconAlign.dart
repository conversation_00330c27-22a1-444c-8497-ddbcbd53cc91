import 'package:flap_proxy/flap_proxy.dart';
import 'package:roo_flutter/basic_components/radio/roo_radio.dart';

class FlapProxyRooRadioIconAlign extends BaseFlapProxy<RooRadioIconAlign> {
  @override
  String getProxyName() {
    return "RooRadioIconAlign";
  }

  @override
  Map<String, Function> methods() {
    return {
      "toString": (RooRadioIconAlign instance, List pa, Map na) =>
          instance.toString()
    };
  }

  @override
  Map<String, Function> getters() {
    return {"index": (RooRadioIconAlign instance) => instance.index};
  }

  @override
  Map<String, Function> staticGetters() {
    return {
      "left": () => RooRadioIconAlign.left,
      "right": () => RooRadioIconAlign.right,
      "values": () => RooRadioIconAlign.values
    };
  }
}
