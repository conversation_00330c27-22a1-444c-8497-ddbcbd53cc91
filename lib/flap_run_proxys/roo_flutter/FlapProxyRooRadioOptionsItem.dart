import 'package:flap_proxy/flap_proxy.dart';
import 'package:roo_flutter/basic_components/radio_adapted/roo_radio_adapted.dart';

class FlapProxyRooRadioOptionsItem extends BaseFlapProxy<RooRadioOptionsItem> {
  @override
  String getProxyName() {
    return "RooRadioOptionsItem";
  }

  @override
  Map<String, Function> constructors() {
    return {
      "": (List pa, Map na) => RooRadioOptionsItem(
          disabled: getNa(na, 'disabled', defaultValue: false),
          label: getNa(na, 'label', defaultValue: ''),
          tip: getNa(na, 'tip', defaultValue: ''),
          value: getNa(na, 'value', defaultValue: -1))
    };
  }

  @override
  Map<String, Function> getters() {
    return {
      "disabled": (RooRadioOptionsItem instance) => instance.disabled,
      "label": (RooRadioOptionsItem instance) => instance.label,
      "tip": (RooRadioOptionsItem instance) => instance.tip,
      "value": (RooRadioOptionsItem instance) => instance.value
    };
  }
}
