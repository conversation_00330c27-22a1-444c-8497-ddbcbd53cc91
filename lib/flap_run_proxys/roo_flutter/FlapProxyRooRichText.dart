import 'package:flutter/painting.dart';

import 'package:flap_proxy/flap_proxy.dart';
import 'package:roo_flutter/basic_components/rich_text/app.dart';

class FlapProxyRooRichText extends BaseFlapProxy<RooRichText> {
  @override
  String getProxyName() {
    return "RooRichText";
  }

  @override
  Map<String, Function> constructors() {
    return {
      "": (List pa, Map na) => RooRichText(
          content: na['content'],
          imgCanPreview: getNa(na, 'imgCanPreview', defaultValue: true),
          key: na['key'],
          padding: getNa(na, 'padding', defaultValue: const EdgeInsets.all(20)))
    };
  }

  @override
  Map<String, Function> methods() {
    return {
      "build": (RooRichText instance, List pa, Map na) => instance.build(pa[0])
    };
  }

  @override
  Map<String, Function> getters() {
    return {
      "content": (RooRichText instance) => instance.content,
      "imgCanPreview": (RooRichText instance) => instance.imgCanPreview,
      "padding": (RooRichText instance) => instance.padding
    };
  }
}
