import 'package:flap_proxy/flap_proxy.dart';
import 'package:roo_flutter/basic_components/selector/model/select_item.dart';
import 'package:roo_flutter/basic_components/selector/roo_selector.dart';

class FlapProxyRooSelector extends BaseFlapProxy<RooSelector> {
  @override
  String getProxyName() {
    return "RooSelector";
  }

  @override
  Map<String, Function> constructors() {
    return {
      "": (List pa, Map na) => RooSelector(
          activeColor: na['activeColor'],
          appItemHeight: na['appItemHeight']?.toDouble(),
          clearable: na['clearable'],
          clickTargetWidget: na['clickTargetWidget'],
          commonTextStyleApp: na['commonTextStyleApp'],
          commonTextStylePC: na['commonTextStylePC'],
          disabledTextStyleApp: na['disabledTextStyleApp'],
          disabledTextStylePC: na['disabledTextStylePC'],
          filterable: na['filterable'],
          inputType: getNa(na, 'inputType', defaultValue: InputType.border),
          itemHeight: na['itemHeight']?.toDouble(),
          onChange: na['onChange'] == null
              ? null
              : (item, index) => na['onChange'](item, index),
          options: na['options']?.cast<SelectItem>(),
          placeholder: na['placeholder'],
          prefixIcon: na['prefixIcon'],
          selectTextStyleApp: na['selectTextStyleApp'],
          selectTextStylePC: na['selectTextStylePC'],
          value: na['value'],
          widthPC: na['widthPC']?.toDouble())
    };
  }

  @override
  Map<String, Function> methods() {
    return {
      "createState": (RooSelector instance, List pa, Map na) =>
          instance.createState()
    };
  }

  @override
  Map<String, Function> getters() {
    return {
      "activeColor": (RooSelector instance) => instance.activeColor,
      "appItemHeight": (RooSelector instance) => instance.appItemHeight,
      "clearable": (RooSelector instance) => instance.clearable,
      "clickTargetWidget": (RooSelector instance) => instance.clickTargetWidget,
      "commonTextStyleApp": (RooSelector instance) =>
          instance.commonTextStyleApp,
      "commonTextStylePC": (RooSelector instance) => instance.commonTextStylePC,
      "disabledTextStyleApp": (RooSelector instance) =>
          instance.disabledTextStyleApp,
      "disabledTextStylePC": (RooSelector instance) =>
          instance.disabledTextStylePC,
      "filterable": (RooSelector instance) => instance.filterable,
      "inputType": (RooSelector instance) => instance.inputType,
      "itemHeight": (RooSelector instance) => instance.itemHeight,
      "onChange": (RooSelector instance) => instance.onChange,
      "options": (RooSelector instance) => instance.options,
      "placeholder": (RooSelector instance) => instance.placeholder,
      "prefixIcon": (RooSelector instance) => instance.prefixIcon,
      "selectTextStyleApp": (RooSelector instance) =>
          instance.selectTextStyleApp,
      "selectTextStylePC": (RooSelector instance) => instance.selectTextStylePC,
      "value": (RooSelector instance) => instance.value,
      "widthPC": (RooSelector instance) => instance.widthPC
    };
  }
}
