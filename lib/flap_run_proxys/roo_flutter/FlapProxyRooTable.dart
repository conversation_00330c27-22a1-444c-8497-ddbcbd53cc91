import 'package:flap_proxy/flap_proxy.dart';
import 'package:roo_flutter/basic_components/table/roo_table.dart';

class FlapProxyRooTable extends BaseFlapProxy<RooTable> {
  @override
  String getProxyName() {
    return "RooTable";
  }

  @override
  Map<String, Function> constructors() {
    return {
      "": (List pa, Map na) => RooTable(
          appRender: na['appRender'],
          columns: na['columns']?.cast<RooTableColumn>(),
          dataRowHeight:
              getNa(na, 'dataRowHeight', defaultValue: 76.0)?.toDouble(),
          dataSource: na['dataSource'],
          decoration: na['decoration'],
          emptyProperty: na['emptyProperty'],
          headingRowHeight:
              getNa(na, 'headingRowHeight', defaultValue: 50.0)?.toDouble(),
          key: na['key'],
          rowColorSelector: na['rowColorSelector'] == null
              ? null
              : (index) => na['rowColorSelector'](index))
    };
  }

  @override
  Map<String, Function> methods() {
    return {
      "createState": (RooTable instance, List pa, Map na) =>
          instance.createState()
    };
  }

  @override
  Map<String, Function> getters() {
    return {
      "appRender": (RooTable instance) => instance.appRender,
      "columns": (RooTable instance) => instance.columns,
      "dataRowHeight": (RooTable instance) => instance.dataRowHeight,
      "dataSource": (RooTable instance) => instance.dataSource,
      "decoration": (RooTable instance) => instance.decoration,
      "emptyProperty": (RooTable instance) => instance.emptyProperty,
      "headingRowHeight": (RooTable instance) => instance.headingRowHeight,
      "rowColorSelector": (RooTable instance) => instance.rowColorSelector
    };
  }
}
