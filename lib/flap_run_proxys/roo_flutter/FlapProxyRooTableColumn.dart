import 'package:flutter/painting.dart';

import 'package:flap_proxy/flap_proxy.dart';
import 'package:roo_flutter/basic_components/table/roo_table.dart';

class FlapProxyRooTableColumn extends BaseFlapProxy<RooTableColumn> {
  @override
  String getProxyName() {
    return "RooTableColumn";
  }

  @override
  Map<String, Function> constructors() {
    return {
      "": (List pa, Map na) => RooTableColumn(
          cellBackground: na['cellBackground'],
          cellRender:
              na['cellRender'] == null ? null : (pa0) => na['cellRender'](pa0),
          headerAlign: getNa(na, 'headerAlign', defaultValue: Alignment.center),
          headerBackground: na['headerBackground'],
          headerRender: na['headerRender'] == null
              ? null
              : (pa0) => na['headerRender'](pa0),
          label: na['label'],
          textAlign: getNa(na, 'textAlign', defaultValue: Alignment.center),
          value: na['value'] == null ? null : (pa0) => na['value'](pa0),
          width: getNa(na, 'width', defaultValue: -1)?.toDouble())
    };
  }

  @override
  Map<String, Function> getters() {
    return {
      "cellBackground": (RooTableColumn instance) => instance.cellBackground,
      "cellRender": (RooTableColumn instance) => instance.cellRender,
      "headerAlign": (RooTableColumn instance) => instance.headerAlign,
      "headerBackground": (RooTableColumn instance) =>
          instance.headerBackground,
      "headerRender": (RooTableColumn instance) => instance.headerRender,
      "label": (RooTableColumn instance) => instance.label,
      "textAlign": (RooTableColumn instance) => instance.textAlign,
      "value": (RooTableColumn instance) => instance.value,
      "width": (RooTableColumn instance) => instance.width
    };
  }
}
