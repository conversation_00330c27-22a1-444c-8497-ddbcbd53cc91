import 'package:flap_proxy/flap_proxy.dart';
import 'package:roo_flutter/basic_components/table/roo_table.dart';

class FlapProxyRooTableEmptyProperty
    extends BaseFlapProxy<RooTableEmptyProperty> {
  @override
  String getProxyName() {
    return "RooTableEmptyProperty";
  }

  @override
  Map<String, Function> constructors() {
    return {
      "": (List pa, Map na) => RooTableEmptyProperty(
          decoration: na['decoration'],
          image: na['image'],
          text: getNa(na, 'text', defaultValue: '暂无数据'),
          widget: na['widget'])
    };
  }

  @override
  Map<String, Function> getters() {
    return {
      "decoration": (RooTableEmptyProperty instance) => instance.decoration,
      "image": (RooTableEmptyProperty instance) => instance.image,
      "text": (RooTableEmptyProperty instance) => instance.text,
      "widget": (RooTableEmptyProperty instance) => instance.widget
    };
  }
}
