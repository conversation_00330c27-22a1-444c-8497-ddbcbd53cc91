import 'package:flap_proxy/flap_proxy.dart';
import 'package:roo_flutter/basic_components/top_tip/roo_top_tip.dart';

class FlapProxyRooTopTip extends BaseFlapProxy<RooTopTip> {
  @override
  String getProxyName() {
    return "RooTopTip";
  }

  @override
  Map<String, Function> constructors() {
    return {
      "": (List pa, Map na) => RooTopTip(
          actionIcon: na['actionIcon'],
          backgroundColor: na['backgroundColor'],
          content: na['content'],
          key: na['key'],
          leftDrawable: na['leftDrawable'],
          leftIconColor: na['leftIconColor'],
          margin: na['margin'],
          maxLine: na['maxLine'],
          onPressActionIcon: na['onPressActionIcon'],
          padding: na['padding'],
          radius: getNa(na, 'radius', defaultValue: 0)?.toDouble(),
          rightButtonTxt: na['rightButtonTxt'],
          showLeftIcon: na['showLeftIcon'],
          showRightArrow: na['showRightArrow'],
          showRightButton: na['showRightButton'],
          showRightClose: na['showRightClose'],
          textStyle: na['textStyle'])
    };
  }

  @override
  Map<String, Function> methods() {
    return {
      "createState": (RooTopTip instance, List pa, Map na) =>
          instance.createState()
    };
  }

  @override
  Map<String, Function> getters() {
    return {
      "actionIcon": (RooTopTip instance) => instance.actionIcon,
      "backgroundColor": (RooTopTip instance) => instance.backgroundColor,
      "content": (RooTopTip instance) => instance.content,
      "leftDrawable": (RooTopTip instance) => instance.leftDrawable,
      "leftIconColor": (RooTopTip instance) => instance.leftIconColor,
      "margin": (RooTopTip instance) => instance.margin,
      "maxLine": (RooTopTip instance) => instance.maxLine,
      "onPressActionIcon": (RooTopTip instance) => instance.onPressActionIcon,
      "padding": (RooTopTip instance) => instance.padding,
      "radius": (RooTopTip instance) => instance.radius,
      "rightButtonTxt": (RooTopTip instance) => instance.rightButtonTxt,
      "showLeftIcon": (RooTopTip instance) => instance.showLeftIcon,
      "showRightArrow": (RooTopTip instance) => instance.showRightArrow,
      "showRightButton": (RooTopTip instance) => instance.showRightButton,
      "showRightClose": (RooTopTip instance) => instance.showRightClose,
      "textStyle": (RooTopTip instance) => instance.textStyle
    };
  }
}
