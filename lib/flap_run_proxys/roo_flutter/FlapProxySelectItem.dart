import 'package:flap_proxy/flap_proxy.dart';
import 'package:roo_flutter/basic_components/selector/model/select_item.dart';

class FlapProxySelectItem extends BaseFlapProxy<SelectItem> {
  @override
  String getProxyName() {
    return "SelectItem";
  }

  @override
  Map<String, Function> constructors() {
    return {
      "": (List pa, Map na) => SelectItem(
          disabled: na['disabled'], label: na['label'], value: na['value']),
      "fromJson": (List pa, Map na) =>
          SelectItem.fromJson(pa[0]?.cast<String, dynamic>())
    };
  }

  @override
  Map<String, Function> methods() {
    return {
      "toJson": (SelectItem instance, List pa, Map na) => instance.toJson()
    };
  }

  @override
  Map<String, Function> getters() {
    return {
      "disabled": (SelectItem instance) => instance.disabled,
      "label": (SelectItem instance) => instance.label,
      "value": (SelectItem instance) => instance.value
    };
  }

  @override
  Map<String, Function> setters() {
    return {
      "disabled": (SelectItem instance, value) => instance.disabled = value,
      "label": (SelectItem instance, value) => instance.label = value,
      "value": (SelectItem instance, value) => instance.value = value
    };
  }
}
