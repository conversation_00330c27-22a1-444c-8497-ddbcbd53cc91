import 'package:flap_proxy/flap_proxy.dart';
import 'FlapProxyRooDialog.dart';
import 'FlapProxyRooRadioAdapted.dart';
import 'FlapProxyRooRadioIconAlign.dart';
import 'FlapProxyRooRadioOptionsItem.dart';
import 'FlapProxyRooRichText.dart';
import 'FlapProxyRooSelector.dart';
import 'FlapProxyRooTable.dart';
import 'FlapProxyRooTableColumn.dart';
import 'FlapProxyRooTableEmptyProperty.dart';
import 'FlapProxyRooTopTip.dart';
import 'FlapProxySelectItem.dart';

class FlapProxyPackageRooFlutter extends BaseFlapProxyPackage {
  @override
  String getPackageName() => 'roo_flutter';

  @override
  List<BaseFlapProxy> getProxyList() {
    return [
      FlapProxyRooDialog(),
      FlapProxyRooRadioAdapted(),
      FlapProxyRooRadioIconAlign(),
      FlapProxyRooRadioOptionsItem(),
      FlapProxyRooRichText(),
      FlapProxyRooSelector(),
      FlapProxyRooTable(),
      FlapProxyRooTableColumn(),
      FlapProxyRooTableEmptyProperty(),
      FlapProxyRooTopTip(),
      FlapProxySelectItem()
    ];
  }
}
