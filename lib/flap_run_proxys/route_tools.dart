import 'dart:async';

import 'package:flap/flap.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';

class RouteTools {
  static Future<RouteResult> open(
    String url, {
    String routeName,
    Map<String, dynamic> params,
    BuildContext context,
    String entryName,
  }) {
    if (kDebugMode && context != null) {
      if (kFlapMode) {
        return Navigator.push(
          context,
          MaterialPageRoute(
            builder: (BuildContext context) {
              // ignore: deprecated_member_use
              return FlapWidgetContainer(
                widgetId: 'fe_college',
                entryName: entryName,
                moduleName: 'waimai_e_fe_flutter_finance',
                params: params,
              );
            },
          ),
        );
      } else {
        Completer<RouteResult> c = Completer<RouteResult>();
        Navigator.pushNamed(context, routeName, arguments: params)
            .then((value) {
          c.complete(RouteResult(code: 1));
        });
        return c.future;
      }
    }
    return RouteUtils.open(url, data: params);
  }
}
