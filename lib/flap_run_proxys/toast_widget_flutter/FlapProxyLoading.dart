import 'dart:core';

import 'package:flap_proxy/flap_proxy.dart';
import 'package:toast_widget_flutter/src/loading_toast/index.dart';

class FlapProxyLoading extends BaseFlapProxy<Loading> {
  @override
  String getProxyName() {
    return "Loading";
  }

  @override
  Map<String, Function> constructors() {
    return {"": (List pa, Map na) => Loading()};
  }

  @override
  Map<String, Function> staticMethods() {
    return {
      "addContextInstance": (List pa, Map na) =>
          Loading.addContextInstance(pa[0]),
      "addInstance": (List pa, Map na) => Loading.addInstance(pa[0]),
      "addOverLayEntryInstance": (List pa, Map na) =>
          Loading.addOverLayEntryInstance(pa[0]),
      "dismiss": (List pa, Map na) => Loading.dismiss(),
      "dismissLoading": (List pa, Map na) => Loading.dismissLoading(),
      "dismissOverLay": (List pa, Map na) => Loading.dismissOverLay(),
      "dispose": (List pa, Map na) => Loading.dispose(context: na['context']),
      "disposeOverLay": (List pa, Map na) => Loading.disposeOverLay(),
      "init": (List pa, Map na) => Loading.init(pa[0]),
      "showDialogToast": (List pa, Map na) => Loading.showDialogToast(pa[0],
          childWidget: na['childWidget'],
          duration: na['duration'],
          maskColor: na['maskColor'],
          message: na['message'],
          mounted: na['mounted']),
      "showLoading": (List pa, Map na) => Loading.showLoading(
          context: na['context'],
          duration: na['duration'],
          maskOverNav: na['maskOverNav'],
          showmask: na['showmask'],
          title: getNa(na, 'title', defaultValue: '加载中...')),
      "showToast": (List pa, Map na) => Loading.showToast(
          context: na['context'],
          duration:
              getNa(na, 'duration', defaultValue: const Duration(seconds: 2)),
          message: getNa(na, 'message', defaultValue: ''),
          showmask: na['showmask'])
    };
  }

  @override
  Map<String, Function> staticGetters() {
    return {
      "contextInstance": () => Loading.contextInstance,
      "instance": () => Loading.instance,
      "overLayEntryInstance": () => Loading.overLayEntryInstance
    };
  }
}
