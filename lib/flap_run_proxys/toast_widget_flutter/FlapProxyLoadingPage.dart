import 'package:flap_proxy/flap_proxy.dart';
import 'package:toast_widget_flutter/src/loading_toast/index.dart';

class FlapProxyLoadingPage extends BaseFlapProxy<LoadingPage> {
  @override
  String getProxyName() {
    return "LoadingPage";
  }

  @override
  Map<String, Function> constructors() {
    return {
      "": (List pa, Map na) => LoadingPage(
          acceptParams: na['acceptParams'],
          child: na['child'],
          params: na['params'])
    };
  }

  @override
  Map<String, Function> methods() {
    return {
      "createState": (LoadingPage instance, List pa, Map na) =>
          instance.createState()
    };
  }

  @override
  Map<String, Function> getters() {
    return {
      "acceptParams": (LoadingPage instance) => instance.acceptParams,
      "child": (LoadingPage instance) => instance.child,
      "params": (LoadingPage instance) => instance.params
    };
  }
}
