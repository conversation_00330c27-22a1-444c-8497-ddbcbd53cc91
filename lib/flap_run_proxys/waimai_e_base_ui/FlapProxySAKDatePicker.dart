import 'package:flap_proxy/flap_proxy.dart';
import 'package:waimai_e_base_ui/src/date_picker/date_picker.dart';

class FlapProxySAKDatePicker extends BaseFlapProxy<SAKDatePicker> {
  @override
  String getProxyName() {
    return "SAKDatePicker";
  }

  @override
  Map<String, Function> constructors() {
    return {"": (List pa, Map na) => SAKDatePicker()};
  }

  @override
  Map<String, Function> staticMethods() {
    return {
      "showDatePicker": (List pa, Map na) => SAKDatePicker.showDatePicker(
          cancelButton: na['cancelButton'],
          confirmButton: na['confirmButton'],
          context: na['context'],
          currentTime: na['currentTime'],
          maxTime: na['maxTime'],
          minTime: na['minTime'],
          onCancel: na['onCancel'],
          onChanged:
              na['onChanged'] == null ? null : (time) => na['onChanged'](time),
          onConfirm:
              na['onConfirm'] == null ? null : (time) => na['onConfirm'](time),
          subTitleWidget: na['subTitleWidget'],
          title: na['title'],
          type: na['type']),
      "showLinkedTimeRangePicker": (List pa, Map na) =>
          SAKDatePicker.showLinkedTimeRangePicker(
              cancelButton: na['cancelButton'],
              confirmButton: na['confirmButton'],
              context: na['context'],
              currentTime: na['currentTime'],
              disabled: na['disabled'],
              endTime: na['endTime'],
              isNextDay: na['isNextDay'],
              onCancel: na['onCancel'],
              onChanged: na['onChanged'] == null
                  ? null
                  : (startTime, endTime) => na['onChanged'](startTime, endTime),
              onConfirm: na['onConfirm'] == null
                  ? null
                  : (startTime, endTime) => na['onConfirm'](startTime, endTime),
              startTime: na['startTime'],
              subTitleWidget: na['subTitleWidget'],
              title: na['title']),
      "showTimePicker": (List pa, Map na) => SAKDatePicker.showTimePicker(
          cancelButton: na['cancelButton'],
          confirmButton: na['confirmButton'],
          context: na['context'],
          currentTime: na['currentTime'],
          onCancel: na['onCancel'],
          onChanged:
              na['onChanged'] == null ? null : (time) => na['onChanged'](time),
          onConfirm:
              na['onConfirm'] == null ? null : (time) => na['onConfirm'](time),
          showSecondsColumn:
              getNa(na, 'showSecondsColumn', defaultValue: false),
          subTitleWidget: na['subTitleWidget'],
          title: na['title']),
      "showTimeRangePicker": (List pa, Map na) =>
          SAKDatePicker.showTimeRangePicker(
              cancelButton: na['cancelButton'],
              confirmButton: na['confirmButton'],
              context: na['context'],
              currentTime: na['currentTime'],
              disabled: na['disabled'],
              endTime: na['endTime'],
              onCancel: na['onCancel'],
              onChanged:
                  na['onChanged'] == null
                      ? null
                      : (startTime, endTime) =>
                          na['onChanged'](startTime, endTime),
              onConfirm: na['onConfirm'] == null
                  ? null
                  : (startTime, endTime) => na['onConfirm'](startTime, endTime),
              startTime: na['startTime'],
              subTitleWidget: na['subTitleWidget'],
              title: na['title'])
    };
  }
}
