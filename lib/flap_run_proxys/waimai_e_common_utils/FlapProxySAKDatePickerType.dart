import 'package:flap_proxy/flap_proxy.dart';
import 'package:waimai_e_common_utils/src/common/sak_date_picker_type.dart';

class FlapProxySAKDatePickerType extends BaseFlapProxy<SAKDatePickerType> {
  @override
  String getProxyName() {
    return "SAKDatePickerType";
  }

  @override
  Map<String, Function> methods() {
    return {
      "toString": (SAKDatePickerType instance, List pa, Map na) =>
          instance.toString()
    };
  }

  @override
  Map<String, Function> getters() {
    return {"index": (SAKDatePickerType instance) => instance.index};
  }

  @override
  Map<String, Function> staticGetters() {
    return {
      "date": () => SAKDatePickerType.date,
      "month": () => SAKDatePickerType.month,
      "values": () => SAKDatePickerType.values,
      "year": () => SAKDatePickerType.year,
      "yearMonth": () => SAKDatePickerType.yearMonth
    };
  }
}
