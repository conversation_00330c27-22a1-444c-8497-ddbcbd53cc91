import 'package:flap_proxy/flap_proxy.dart';
import 'package:waimai_e_common_utils/src/utils/router_util.dart';

class FlapProxySchemeUrls extends BaseFlapProxy<SchemeUrls> {
  @override
  String getProxyName() {
    return "SchemeUrls";
  }

  @override
  Map<String, Function> constructors() {
    return {"": (List pa, Map na) => SchemeUrls()};
  }

  @override
  Map<String, Function> staticMethods() {
    return {
      "flutterPageUrl": (List pa, Map na) => SchemeUrls.flutterPageUrl(pa[0],
          channel: na['channel'],
          params: na['params']?.cast<String, dynamic>()),
      "mrnBundleUrl": (List pa, Map na) => SchemeUrls.mrnBundleUrl(
          pa[0], pa[1], pa[2],
          params: na['params']?.cast<String, dynamic>()),
      "nativePageUrl": (List pa, Map na) => SchemeUrls.nativePageUrl(pa[0],
          params: na['params']?.cast<String, dynamic>()),
      "webPageUrl": (List pa, Map na) => SchemeUrls.webPageUrl(pa[0])
    };
  }

  @override
  Map<String, Function> staticGetters() {
    return {"urlPrefix": () => SchemeUrls.urlPrefix};
  }
}
