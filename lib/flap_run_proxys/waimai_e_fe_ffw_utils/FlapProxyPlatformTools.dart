import 'package:flap_proxy/flap_proxy.dart';
import 'package:waimai_e_fe_ffw_utils/platform_tool.dart';

class FlapProxyPlatformTools extends BaseFlapProxy<PlatformTools> {
  @override
  String getProxyName() {
    return "PlatformTools";
  }

  @override
  Map<String, Function> constructors() {
    return {"": (List pa, Map na) => PlatformTools()};
  }

  @override
  Map<String, Function> staticGetters() {
    return {
      "isAndroid": () => PlatformTools.isAndroid,
      "isFuchsia": () => PlatformTools.isFuchsia,
      "isIOS": () => PlatformTools.isIOS,
      "isLinux": () => PlatformTools.isLinux,
      "isMacOS": () => PlatformTools.isMacOS,
      "isNative": () => PlatformTools.isNative,
      "isPC": () => PlatformTools.isPC,
      "isPCClient": () => PlatformTools.isPCClient,
      "isWeb": () => PlatformTools.isWeb,
      "isWindows": () => PlatformTools.isWindows
    };
  }
}
