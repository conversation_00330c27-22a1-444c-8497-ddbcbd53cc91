import 'package:flap_proxy/flap_proxy.dart';
import 'package:waimai_e_native_business/src/poi_index.dart';
import 'package:waimai_e_native_business/src/poi_info.dart';

class FlapProxyPoiInfo extends BaseFlapProxy<PoiInfo> {
  @override
  String getProxyName() {
    return "PoiInfo";
  }

  @override
  Map<String, Function> constructors() {
    return {
      "": (List pa, Map na) => PoiInfo(
          acrossDay: na['acrossDay'],
          addressForbidHour: na['addressForbidHour'],
          arriveTimeout: na['arriveTimeout'],
          autoDispatchDeliveryRequest: na['autoDispatchDeliveryRequest'],
          bdId: na['bdId'],
          byteOptionSwitch: na['byteOptionSwitch'],
          customerServiceDeviceName: na['customerServiceDeviceName'],
          dispatcherPrivacyExpire: na['dispatcherPrivacyExpire'],
          firstTagId: na['firstTagId'],
          foodButtonTimeWindow: na['foodButtonTimeWindow'],
          hasQikeDistributor: na['hasQikeDistributor'],
          iSignUrl: na['iSignUrl'],
          indexModules: na['indexModules']?.cast<PoiIndex>(),
          isDelayAutoDeliveryAllowed: na['isDelayAutoDeliveryAllowed'],
          isDessert: na['isDessert'],
          isFastDeliveryTeam: na['isFastDeliveryTeam'],
          isGroup: na['isGroup'],
          isHybridDeliveryTeam: na['isHybridDeliveryTeam'],
          isMeituanDeliveryTeam: na['isMeituanDeliveryTeam'],
          isNeedPreRestaurant: na['isNeedPreRestaurant'],
          isNewShopEnable: na['isNewShopEnable'],
          isRetail: na['isRetail'],
          isSLAMerchant: na['isSLAMerchant'],
          isShowVideoModule: na['isShowVideoModule'],
          latitude: na['latitude'],
          longitude: na['longitude'],
          mSignUrl: na['mSignUrl'],
          modifyDispatchDuration: na['modifyDispatchDuration'],
          newOrderRemindFlag: na['newOrderRemindFlag'],
          permission: na['permission']?.cast<UserPermission>(),
          poiCategoryType: na['poiCategoryType'],
          poiId: na['poiId'],
          poiImSwitch: na['poiImSwitch'],
          poiLogistics: na['poiLogistics'],
          poiLogoURLString: na['poiLogoURLString'],
          poiName: na['poiName'],
          poiTag: na['poiTag'],
          poiZbResourceInfo: na['poiZbResourceInfo'],
          preOrderReminder: na['preOrderReminder'],
          prepareMealTime: na['prepareMealTime'],
          privacyProtectHours: na['privacyProtectHours'],
          privacySoundRecordRemind: na['privacySoundRecordRemind'],
          qualificationUrl: na['qualificationUrl'],
          restType: na['restType'],
          shippingTimeX: na['shippingTimeX'],
          showCustomerService: na['showCustomerService'],
          showReserveDayFlag: na['showReserveDayFlag'],
          showRiderPosition: na['showRiderPosition'],
          status: na['status'],
          statusDesc: na['statusDesc'],
          statusDescInfo: na['statusDescInfo'],
          subStatus: na['subStatus'],
          thirdDispatchAsynTime: na['thirdDispatchAsynTime'],
          valid: na['valid'],
          waitingForDeliveryOrderRemainDay:
              na['waitingForDeliveryOrderRemainDay'])
    };
  }

  @override
  Map<String, Function> methods() {
    return {
      "getDelivery": (PoiInfo instance, List pa, Map na) =>
          instance.getDelivery(),
      "getDeliveryId": (PoiInfo instance, List pa, Map na) =>
          instance.getDeliveryId(),
      "hasDelivery": (PoiInfo instance, List pa, Map na) =>
          instance.hasDelivery(),
      "hasLogisticsTeam": (PoiInfo instance, List pa, Map na) =>
          instance.hasLogisticsTeam(),
      "isHasThirdDelivery": (PoiInfo instance, List pa, Map na) =>
          instance.isHasThirdDelivery(),
      "isLogisticCountDownForMSFInvisable":
          (PoiInfo instance, List pa, Map na) =>
              instance.isLogisticCountDownForMSFInvisable()
    };
  }

  @override
  Map<String, Function> getters() {
    return {
      "acrossDay": (PoiInfo instance) => instance.acrossDay,
      "addressForbidHour": (PoiInfo instance) => instance.addressForbidHour,
      "arriveTimeout": (PoiInfo instance) => instance.arriveTimeout,
      "autoDispatchDeliveryRequest": (PoiInfo instance) =>
          instance.autoDispatchDeliveryRequest,
      "bdId": (PoiInfo instance) => instance.bdId,
      "byteOptionSwitch": (PoiInfo instance) => instance.byteOptionSwitch,
      "customerServiceDeviceName": (PoiInfo instance) =>
          instance.customerServiceDeviceName,
      "dispatcherPrivacyExpire": (PoiInfo instance) =>
          instance.dispatcherPrivacyExpire,
      "firstTagId": (PoiInfo instance) => instance.firstTagId,
      "foodButtonTimeWindow": (PoiInfo instance) =>
          instance.foodButtonTimeWindow,
      "hasQikeDistributor": (PoiInfo instance) => instance.hasQikeDistributor,
      "iSignUrl": (PoiInfo instance) => instance.iSignUrl,
      "indexModules": (PoiInfo instance) => instance.indexModules,
      "isDelayAutoDeliveryAllowed": (PoiInfo instance) =>
          instance.isDelayAutoDeliveryAllowed,
      "isDessert": (PoiInfo instance) => instance.isDessert,
      "isFastDeliveryTeam": (PoiInfo instance) => instance.isFastDeliveryTeam,
      "isGroup": (PoiInfo instance) => instance.isGroup,
      "isHybridDeliveryTeam": (PoiInfo instance) =>
          instance.isHybridDeliveryTeam,
      "isMeituanDeliveryTeam": (PoiInfo instance) =>
          instance.isMeituanDeliveryTeam,
      "isNeedPreRestaurant": (PoiInfo instance) => instance.isNeedPreRestaurant,
      "isNeedReprint": (PoiInfo instance) => instance.isNeedReprint,
      "isNewShopEnable": (PoiInfo instance) => instance.isNewShopEnable,
      "isReceiptNeedHideAddrss": (PoiInfo instance) =>
          instance.isReceiptNeedHideAddrss,
      "isReceiptNeedHidePhone": (PoiInfo instance) =>
          instance.isReceiptNeedHidePhone,
      "isRetail": (PoiInfo instance) => instance.isRetail,
      "isSLAMerchant": (PoiInfo instance) => instance.isSLAMerchant,
      "isShowVideoModule": (PoiInfo instance) => instance.isShowVideoModule,
      "isWaimaiPoi": (PoiInfo instance) => instance.isWaimaiPoi,
      "latitude": (PoiInfo instance) => instance.latitude,
      "longitude": (PoiInfo instance) => instance.longitude,
      "mSignUrl": (PoiInfo instance) => instance.mSignUrl,
      "modifyDispatchDuration": (PoiInfo instance) =>
          instance.modifyDispatchDuration,
      "newOrderRemindFlag": (PoiInfo instance) => instance.newOrderRemindFlag,
      "permission": (PoiInfo instance) => instance.permission,
      "poiCategoryType": (PoiInfo instance) => instance.poiCategoryType,
      "poiId": (PoiInfo instance) => instance.poiId,
      "poiImSwitch": (PoiInfo instance) => instance.poiImSwitch,
      "poiLogistics": (PoiInfo instance) => instance.poiLogistics,
      "poiLogoURLString": (PoiInfo instance) => instance.poiLogoURLString,
      "poiName": (PoiInfo instance) => instance.poiName,
      "poiTag": (PoiInfo instance) => instance.poiTag,
      "poiZbResourceInfo": (PoiInfo instance) => instance.poiZbResourceInfo,
      "preOrderReminder": (PoiInfo instance) => instance.preOrderReminder,
      "prepareMealTime": (PoiInfo instance) => instance.prepareMealTime,
      "privacyProtectHours": (PoiInfo instance) => instance.privacyProtectHours,
      "privacySoundRecordRemind": (PoiInfo instance) =>
          instance.privacySoundRecordRemind,
      "qualificationUrl": (PoiInfo instance) => instance.qualificationUrl,
      "restType": (PoiInfo instance) => instance.restType,
      "shippingTimeX": (PoiInfo instance) => instance.shippingTimeX,
      "showCustomerService": (PoiInfo instance) => instance.showCustomerService,
      "showReserveDayFlag": (PoiInfo instance) => instance.showReserveDayFlag,
      "showRiderPosition": (PoiInfo instance) => instance.showRiderPosition,
      "status": (PoiInfo instance) => instance.status,
      "statusDesc": (PoiInfo instance) => instance.statusDesc,
      "statusDescInfo": (PoiInfo instance) => instance.statusDescInfo,
      "subStatus": (PoiInfo instance) => instance.subStatus,
      "thirdDispatchAsynTime": (PoiInfo instance) =>
          instance.thirdDispatchAsynTime,
      "thirdDispatchWaitTime": (PoiInfo instance) =>
          instance.thirdDispatchWaitTime,
      "time_diff": (PoiInfo instance) => instance.time_diff,
      "valid": (PoiInfo instance) => instance.valid,
      "waitingForDeliveryOrderRemainDay": (PoiInfo instance) =>
          instance.waitingForDeliveryOrderRemainDay
    };
  }

  @override
  Map<String, Function> setters() {
    return {
      "acrossDay": (PoiInfo instance, value) => instance.acrossDay = value,
      "addressForbidHour": (PoiInfo instance, value) =>
          instance.addressForbidHour = value,
      "arriveTimeout": (PoiInfo instance, value) =>
          instance.arriveTimeout = value,
      "autoDispatchDeliveryRequest": (PoiInfo instance, value) =>
          instance.autoDispatchDeliveryRequest = value,
      "bdId": (PoiInfo instance, value) => instance.bdId = value,
      "byteOptionSwitch": (PoiInfo instance, value) =>
          instance.byteOptionSwitch = value,
      "customerServiceDeviceName": (PoiInfo instance, value) =>
          instance.customerServiceDeviceName = value,
      "dispatcherPrivacyExpire": (PoiInfo instance, value) =>
          instance.dispatcherPrivacyExpire = value,
      "firstTagId": (PoiInfo instance, value) => instance.firstTagId = value,
      "foodButtonTimeWindow": (PoiInfo instance, value) =>
          instance.foodButtonTimeWindow = value,
      "hasQikeDistributor": (PoiInfo instance, value) =>
          instance.hasQikeDistributor = value,
      "iSignUrl": (PoiInfo instance, value) => instance.iSignUrl = value,
      "indexModules": (PoiInfo instance, value) =>
          instance.indexModules = value,
      "isDelayAutoDeliveryAllowed": (PoiInfo instance, value) =>
          instance.isDelayAutoDeliveryAllowed = value,
      "isDessert": (PoiInfo instance, value) => instance.isDessert = value,
      "isFastDeliveryTeam": (PoiInfo instance, value) =>
          instance.isFastDeliveryTeam = value,
      "isGroup": (PoiInfo instance, value) => instance.isGroup = value,
      "isHybridDeliveryTeam": (PoiInfo instance, value) =>
          instance.isHybridDeliveryTeam = value,
      "isMeituanDeliveryTeam": (PoiInfo instance, value) =>
          instance.isMeituanDeliveryTeam = value,
      "isNeedPreRestaurant": (PoiInfo instance, value) =>
          instance.isNeedPreRestaurant = value,
      "isNewShopEnable": (PoiInfo instance, value) =>
          instance.isNewShopEnable = value,
      "isRetail": (PoiInfo instance, value) => instance.isRetail = value,
      "isSLAMerchant": (PoiInfo instance, value) =>
          instance.isSLAMerchant = value,
      "isShowVideoModule": (PoiInfo instance, value) =>
          instance.isShowVideoModule = value,
      "latitude": (PoiInfo instance, value) => instance.latitude = value,
      "longitude": (PoiInfo instance, value) => instance.longitude = value,
      "mSignUrl": (PoiInfo instance, value) => instance.mSignUrl = value,
      "modifyDispatchDuration": (PoiInfo instance, value) =>
          instance.modifyDispatchDuration = value,
      "newOrderRemindFlag": (PoiInfo instance, value) =>
          instance.newOrderRemindFlag = value,
      "permission": (PoiInfo instance, value) => instance.permission = value,
      "poiCategoryType": (PoiInfo instance, value) =>
          instance.poiCategoryType = value,
      "poiId": (PoiInfo instance, value) => instance.poiId = value,
      "poiImSwitch": (PoiInfo instance, value) => instance.poiImSwitch = value,
      "poiLogistics": (PoiInfo instance, value) =>
          instance.poiLogistics = value,
      "poiLogoURLString": (PoiInfo instance, value) =>
          instance.poiLogoURLString = value,
      "poiName": (PoiInfo instance, value) => instance.poiName = value,
      "poiTag": (PoiInfo instance, value) => instance.poiTag = value,
      "poiZbResourceInfo": (PoiInfo instance, value) =>
          instance.poiZbResourceInfo = value,
      "preOrderReminder": (PoiInfo instance, value) =>
          instance.preOrderReminder = value,
      "prepareMealTime": (PoiInfo instance, value) =>
          instance.prepareMealTime = value,
      "privacyProtectHours": (PoiInfo instance, value) =>
          instance.privacyProtectHours = value,
      "privacySoundRecordRemind": (PoiInfo instance, value) =>
          instance.privacySoundRecordRemind = value,
      "qualificationUrl": (PoiInfo instance, value) =>
          instance.qualificationUrl = value,
      "restType": (PoiInfo instance, value) => instance.restType = value,
      "shippingTimeX": (PoiInfo instance, value) =>
          instance.shippingTimeX = value,
      "showCustomerService": (PoiInfo instance, value) =>
          instance.showCustomerService = value,
      "showReserveDayFlag": (PoiInfo instance, value) =>
          instance.showReserveDayFlag = value,
      "showRiderPosition": (PoiInfo instance, value) =>
          instance.showRiderPosition = value,
      "status": (PoiInfo instance, value) => instance.status = value,
      "statusDesc": (PoiInfo instance, value) => instance.statusDesc = value,
      "statusDescInfo": (PoiInfo instance, value) =>
          instance.statusDescInfo = value,
      "subStatus": (PoiInfo instance, value) => instance.subStatus = value,
      "thirdDispatchAsynTime": (PoiInfo instance, value) =>
          instance.thirdDispatchAsynTime = value,
      "time_diff": (PoiInfo instance, value) => instance.time_diff = value,
      "valid": (PoiInfo instance, value) => instance.valid = value,
      "waitingForDeliveryOrderRemainDay": (PoiInfo instance, value) =>
          instance.waitingForDeliveryOrderRemainDay = value
    };
  }

  @override
  Map<String, Function> staticMethods() {
    return {
      "fromJson": (List pa, Map na) =>
          PoiInfo.fromJson(pa[0]?.cast<String, dynamic>())
    };
  }
}
