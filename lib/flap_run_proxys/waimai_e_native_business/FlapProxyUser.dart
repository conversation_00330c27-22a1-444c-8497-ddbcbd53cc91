import 'package:flap_proxy/flap_proxy.dart';
import 'package:waimai_e_native_business/src/user_info.dart';

class FlapProxyUser extends BaseFlapProxy<User> {
  @override
  String getProxyName() {
    return "User";
  }

  @override
  Map<String, Function> constructors() {
    return {
      "": (List pa, Map na) =>
          User(pa[0], pa[1], pa[2], pa[3], pa[4], pa[5], pa[6])
    };
  }

  @override
  Map<String, Function> getters() {
    return {
      "accessToken": (User instance) => instance.accessToken,
      "acctId": (User instance) => instance.acctId,
      "acctName": (User instance) => instance.acctName,
      "bindPhone": (User instance) => instance.bindPhone,
      "id": (User instance) => instance.id,
      "selfOpen": (User instance) => instance.selfOpen,
      "username": (User instance) => instance.username
    };
  }

  @override
  Map<String, Function> setters() {
    return {
      "accessToken": (User instance, value) => instance.accessToken = value,
      "acctId": (User instance, value) => instance.acctId = value,
      "acctName": (User instance, value) => instance.acctName = value,
      "bindPhone": (User instance, value) => instance.bindPhone = value,
      "id": (User instance, value) => instance.id = value,
      "selfOpen": (User instance, value) => instance.selfOpen = value,
      "username": (User instance, value) => instance.username = value
    };
  }

  @override
  Map<String, Function> staticMethods() {
    return {
      "fromJson": (List pa, Map na) =>
          User.fromJson(pa[0]?.cast<String, dynamic>())
    };
  }
}
