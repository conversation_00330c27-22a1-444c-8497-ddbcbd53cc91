import 'package:flap_proxy/flap_proxy.dart';
import 'package:waimai_e_native_business/src/native_business_channel.dart';

class FlapProxyWaimaiENativeBusiness
    extends BaseFlapProxy<WaimaiENativeBusiness> {
  @override
  String getProxyName() {
    return "WaimaiENativeBusiness";
  }

  @override
  Map<String, Function> constructors() {
    return {"": (List pa, Map na) => WaimaiENativeBusiness()};
  }

  @override
  Map<String, Function> staticMethods() {
    return {
      "cancelPlayingSound": (List pa, Map na) =>
          WaimaiENativeBusiness.cancelPlayingSound(),
      "contactToBD": (List pa, Map na) => WaimaiENativeBusiness.contactToBD(),
      "copyToClipboard": (List pa, Map na) =>
          WaimaiENativeBusiness.copyToClipboard(pa[0]),
      "fetchOceanParameters": (List pa, Map na) =>
          WaimaiENativeBusiness.fetchOceanParameters(),
      "getBizLoginToken": (List pa, Map na) =>
          WaimaiENativeBusiness.getBizLoginToken(),
      "getEnvUrl": (List pa, Map na) => WaimaiENativeBusiness.getEnvUrl(pa[0]),
      "getEnvironmentInfo": (List pa, Map na) =>
          WaimaiENativeBusiness.getEnvironmentInfo(),
      "getPoiInfo": (List pa, Map na) => WaimaiENativeBusiness.getPoiInfo(),
      "getPoiInfoStr": (List pa, Map na) =>
          WaimaiENativeBusiness.getPoiInfoStr(),
      "getPushToken": (List pa, Map na) => WaimaiENativeBusiness.getPushToken(),
      "getUserInfo": (List pa, Map na) => WaimaiENativeBusiness.getUserInfo(),
      "getWebHost": (List pa, Map na) => WaimaiENativeBusiness.getWebHost(),
      "gotoNotificationSettingsPage": (List pa, Map na) =>
          WaimaiENativeBusiness.gotoNotificationSettingsPage(),
      "isGrantNotificationPermission": (List pa, Map na) =>
          WaimaiENativeBusiness.isGrantNotificationPermission(),
      "isShowingPresentVC": (List pa, Map na) =>
          WaimaiENativeBusiness.isShowingPresentVC(),
      "logOut": (List pa, Map na) => WaimaiENativeBusiness.logOut(),
      "modifySound": (List pa, Map na) =>
          WaimaiENativeBusiness.modifySound(pa[0]),
      "openNotificationPermissionPage": (List pa, Map na) =>
          WaimaiENativeBusiness.openNotificationPermissionPage(),
      "playSoundUrl": (List pa, Map na) => WaimaiENativeBusiness.playSoundUrl(
          pa[0],
          playCount: getNa(na, 'playCount', defaultValue: 1)),
      "registerSound": (List pa, Map na) =>
          WaimaiENativeBusiness.registerSound(pa[0]),
      "saveImageToGallery": (List pa, Map na) =>
          WaimaiENativeBusiness.saveImageToGallery(pa[0], pa[1]),
      "startSoundPlay": (List pa, Map na) =>
          WaimaiENativeBusiness.startSoundPlay(pa[0]),
      "stopSoundPlay": (List pa, Map na) =>
          WaimaiENativeBusiness.stopSoundPlay(pa[0])
    };
  }
}
