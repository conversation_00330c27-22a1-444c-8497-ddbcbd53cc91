import 'package:flap_proxy/flap_proxy.dart';
import 'FlapProxyPoiInfo.dart';
import 'FlapProxyUser.dart';
import 'FlapProxyWaimaiENativeBusiness.dart';

class FlapProxyPackageWaimaiENativeBusiness extends BaseFlapProxyPackage {
  @override
  String getPackageName() => 'waimai_e_native_business';

  @override
  List<BaseFlapProxy> getProxyList() {
    return [
      FlapProxyPoiInfo(),
      FlapProxyUser(),
      FlapProxyWaimaiENativeBusiness()
    ];
  }
}
