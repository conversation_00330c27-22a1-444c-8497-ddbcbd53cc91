import 'package:flap_proxy/flap_proxy.dart';
import 'package:wef_network/wef_request.dart';

class FlapProxyResponseData extends BaseFlapProxy<ResponseData> {
  @override
  String getProxyName() {
    return "ResponseData";
  }

  @override
  Map<String, Function> constructors() {
    return {"": (List pa, Map na) => ResponseData(pa[0], pa[1], pa[2])};
  }

  @override
  Map<String, Function> getters() {
    return {
      "code": (ResponseData instance) => instance.code,
      "data": (ResponseData instance) => instance.data,
      "msg": (ResponseData instance) => instance.msg
    };
  }

  @override
  Map<String, Function> setters() {
    return {
      "code": (ResponseData instance, value) => instance.code = value,
      "data": (ResponseData instance, value) => instance.data = value,
      "msg": (ResponseData instance, value) => instance.msg = value
    };
  }

  @override
  Map<String, Function> staticMethods() {
    return {
      "fromJson": (List pa, Map na) =>
          ResponseData.fromJson(pa[0]?.cast<String, dynamic>())
    };
  }
}
