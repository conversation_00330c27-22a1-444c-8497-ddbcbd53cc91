import 'package:wef_network/wef_request.dart';

registerGetApiFunction(Map<String, Function> map) {
  map.addAll({
    "getApi": (List pa, Map na) => getApi(
        baseUrl: na['baseUrl'],
        isControlShowToast:
            getNa(na, 'isControlShowToast', defaultValue: false),
        params: na['params'],
        path: na['path'])
  });
}

registerPostApiFunction(Map<String, Function> map) {
  map.addAll({
    "postApi": (List pa, Map na) => postApi(
        baseUrl: na['baseUrl'],
        isControlShowToast:
            getNa(na, 'isControlShowToast', defaultValue: false),
        params: na['params'],
        path: na['path'])
  });
}
