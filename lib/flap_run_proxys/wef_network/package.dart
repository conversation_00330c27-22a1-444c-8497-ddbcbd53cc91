import 'package:flap_proxy/flap_proxy.dart';
import 'FlapProxyResponseData.dart';
import 'global.dart';

class FlapProxyPackageWefNetwork extends BaseFlapProxyPackage {
  @override
  String getPackageName() => 'wef_network';

  @override
  Map<String, Function> getGlobalFunctions() {
    Map<String, Function> map = {};
    registerGetApiFunction(map);
    registerPostApiFunction(map);
    return map;
  }

  @override
  List<BaseFlapProxy> getProxyList() {
    return [FlapProxyResponseData()];
  }
}
