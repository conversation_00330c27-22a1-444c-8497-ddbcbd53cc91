import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:mt_flutter_route/mt_flutter_route_web.dart';
import 'package:waimai_e_fe_flutter_finance/main.route.dart';
import 'package:waimai_e_fe_flutter_finance/waimai_e_fe_flutter_finance.dart';

void main() {
  runZonedGuarded(() {
    runApp(MyApp());
  }, (error, stackTrace) {
    Zone.current.handleUncaughtError(error, stackTrace);
  });
}

class NoTransitionsOnWeb extends PageTransitionsTheme {
  @override
  Widget buildTransitions<T>(
    route,
    context,
    animation,
    secondaryAnimation,
    child,
  ) {
    if (PlatformTool.isWeb) {
      return child;
    }
    return super.buildTransitions(
      route,
      context,
      animation,
      secondaryAnimation,
      child,
    );
  }
}

class FallbackCupertinoLocalisationsDelegate
    extends LocalizationsDelegate<CupertinoLocalizations> {
  const FallbackCupertinoLocalisationsDelegate();

  @override
  bool isSupported(Locale locale) => true;

  @override
  Future<CupertinoLocalizations> load(Locale locale) =>
      DefaultCupertinoLocalizations.load(locale);

  @override
  bool shouldReload(FallbackCupertinoLocalisationsDelegate old) => false;
}

class _MyAppState extends State<MyApp> {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFF7F8FA),
      child: MaterialApp(
        localizationsDelegates: [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
          FallbackCupertinoLocalisationsDelegate(),
        ],
        onGenerateRoute: (settings) {
          // 当路由表(routes)存在，则路由表中没有注册，才会调用onGenerateRoute来生成路由。当前此处只处理FFW中带url参数的情况。如/college/detail?id=xx&contentType=xx
          Uri uriParse = Uri.parse(settings.name);
          String path = uriParse.path;
          // print('onGenerateRoute: $path');
          Map<dynamic, dynamic> params = Map.from(uriParse.queryParameters)
            ..addAll(settings.arguments ?? {});
          if ($pages[path] != null) {
            // print('onGenerateRoute: $path, $params');
            return MaterialPageRoute(
              builder: $pages[path],
              settings: RouteSettings(name: path, arguments: params),
            );
          } else {
            return null;
          }
        },
        title: '财务对账',
        theme: ThemeData(
          // web 去除路由动画
          pageTransitionsTheme: NoTransitionsOnWeb(),
          primarySwatch: MaterialColor(0xFFF89800, {
            50: Color(0xFFFCCC80),
            100: Color(0xFFFBC166),
            200: Color(0xFFFAB74D),
            300: Color(0xFFF9AD33),
            400: Color(0xFFF9A21A),
            500: Color(0xFFF89800),
            600: Color(0xFFF78E00),
            700: Color(0xFFF78300),
            800: Color(0xFFF67900),
            900: Color(0xFFF56F00),
          }),
          cupertinoOverrideTheme: CupertinoThemeData(
            primaryColor: Color(0xFF222222),
            barBackgroundColor: Colors.white,
            textTheme: CupertinoTextThemeData(
              navTitleTextStyle: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF222222),
              ),
              textStyle: TextStyle(
                fontSize: 16,
                color: Color(0xFF222222),
                fontWeight: FontWeight.normal,
              ),
            ),
          ),
          scaffoldBackgroundColor: Color(0xFFF7F8FA),
          appBarTheme: AppBarTheme(
            systemOverlayStyle: SystemUiOverlayStyle.light,
            color: Colors.white,
            elevation: 0,
          ),
        ),
        initialRoute: "/",
        builder: (BuildContext context, Widget child) {
          // 设置最大宽度
          final mediaQuery = MediaQuery.of(context);
          Size size = Size.copy(mediaQuery.size);
          num maxWidth = 1400;
          num margin = 0;
          if (size.width > 0 && size.height > 0) {
            margin = (size.width - maxWidth) / 2;
          }
          return FlutterEasyLoading(
              child: MediaQuery(
            data: MediaQuery.of(context).copyWith(),
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: margin > 0 ? margin : 0),
              child: child,
            ),
          ));
        },
        routes: $pages, // 页面路由
        // navigatorKey.currentState 相当于 Navigator.of(context)
        navigatorKey: MTFlutterRoutePlugin.navigatorKey,
        onUnknownRoute: (settings) =>
            MaterialPageRoute(builder: (context) => HomePage()),
      ),
    );
  }
}

class MyApp extends StatefulWidget {
  @override
  _MyAppState createState() => _MyAppState();
}
