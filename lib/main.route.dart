// ！！！ lib 根目录下引入工程中的文件只能用相对路径，不能用package的方式引入
import 'package:flutter/material.dart';

import 'src/pages/accountDetails/index.dart' deferred as AccountDetailsPage;
import 'src/pages/accountInfo/index.dart' deferred as AccountInfoPage;
import 'src/pages/adFlowDetail/index.dart' deferred as AdFlowDetailPage;
import 'src/pages/balanceFlow/index.dart' deferred as BalanceFlowPage;
import 'src/pages/balanceRecharge/index.dart' deferred as BalanceRechargePage;
import 'src/pages/balanceWithdraw/index.dart' deferred as BalanceWithdrawPage;
import 'src/pages/bee/calcServiceFeeTool/index.dart'
    deferred as CalcServiceFeeToolPage;
import 'src/pages/home/<USER>' deferred as Home;
import 'src/pages/month_daily_bill/dailyIndex.dart' deferred as DailyBills;
import 'src/pages/month_daily_bill/monthIndex.dart' deferred as MonthBills;
import 'src/pages/not_found/index.dart' deferred as NotFound;
import 'src/pages/orderDetail/index.dart' deferred as OrderDetail;
import 'src/pages/orderList/index.dart' deferred as OrderList;
import 'src/pages/orderQuery/index.dart' deferred as OrderQuery;
import 'src/pages/phoneVerify/index.dart' deferred as PhoneVerifyPage;
import 'src/pages/progressOrder/index.dart' deferred as ProgressOrderPage;
import 'src/pages/rechargeResult/index.dart' deferred as RechargeResultPage;
import 'src/pages/transactionDetail/export.dart' deferred as ExportDetailPage;
import 'src/pages/transactionDetail/remit.dart' deferred as RemitDetailPage;
import 'src/pages/transactionType/index.dart' deferred as TradingTypePage;

Widget _allPageBuilder(
    {Future<dynamic> future, Widget Function(BuildContext) builder}) {
  return FutureBuilder(
      future: future,
      builder: (_context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done) {
          return builder(_context);
        } else {
          return SizedBox.shrink();
        }
      });
}

// 注册路由表
Map<String, WidgetBuilder> $pages = {
  '/finance/home': (context) => _allPageBuilder(
        future: Home.loadLibrary(),
        builder: (context) => Home.HomePage(
          params: ModalRoute.of(context).settings.arguments,
        ),
      ),
  '/finance/progessOrder': (context) => _allPageBuilder(
        future: ProgressOrderPage.loadLibrary(),
        builder: (context) => ProgressOrderPage.ProgessOrderPage(
          params: ModalRoute.of(context).settings.arguments ?? {},
        ),
      ),
  '/finance/notfound': (context) => _allPageBuilder(
        future: NotFound.loadLibrary(),
        builder: (context) => NotFound.NotFindPage(),
      ),
  '/finance/orderDetail': (context) => _allPageBuilder(
        future: OrderDetail.loadLibrary(),
        builder: (context) => OrderDetail.OrderDetailPage(
          params: ModalRoute.of(context).settings.arguments,
        ),
      ),
  '/finance/monthBills': (context) => _allPageBuilder(
        future: MonthBills.loadLibrary(),
        builder: (context) => MonthBills.MonthBillsPage(
          params: ModalRoute.of(context).settings.arguments,
        ),
      ),
  '/finance/dailyBills': (context) => _allPageBuilder(
        future: DailyBills.loadLibrary(),
        builder: (context) => DailyBills.DailyBillsPage(
          params: ModalRoute.of(context).settings.arguments,
        ),
      ),
  '/finance/accountInfo': (context) => _allPageBuilder(
        future: AccountInfoPage.loadLibrary(),
        builder: (context) => AccountInfoPage.AccountInfoPage(
          params: ModalRoute.of(context).settings.arguments,
        ),
      ),
  '/finance/balanceFlow': (context) => _allPageBuilder(
        future: BalanceFlowPage.loadLibrary(),
        builder: (context) => BalanceFlowPage.BalanceFlowPage(
          params: ModalRoute.of(context).settings.arguments,
        ),
      ),
  '/finance/orderQuery': (context) => _allPageBuilder(
        future: OrderQuery.loadLibrary(),
        builder: (context) => OrderQuery.OrderQueryPage(
          params: ModalRoute.of(context).settings.arguments,
        ),
      ),
  '/finance/exportDetail': (context) => _allPageBuilder(
        future: ExportDetailPage.loadLibrary(),
        builder: (context) => ExportDetailPage.ExportDetailPage(
          params: ModalRoute.of(context).settings.arguments,
        ),
      ),
  '/finance/remitDetail': (context) => _allPageBuilder(
        future: RemitDetailPage.loadLibrary(),
        builder: (context) => RemitDetailPage.RemitDetailPage(
          params: ModalRoute.of(context).settings.arguments,
        ),
      ),
  '/finance/accountDetails': (context) => _allPageBuilder(
        future: AccountDetailsPage.loadLibrary(),
        builder: (context) => AccountDetailsPage.AccountDetailsPage(
          params: ModalRoute.of(context).settings.arguments,
        ),
      ),
  '/finance/balanceRecharge': (context) => _allPageBuilder(
        future: BalanceRechargePage.loadLibrary(),
        builder: (context) => BalanceRechargePage.BalanceRechargePage(
          params: ModalRoute.of(context).settings.arguments ?? {},
        ),
      ),
  '/finance/balanceWithdraw': (context) => _allPageBuilder(
        future: BalanceWithdrawPage.loadLibrary(),
        builder: (context) => BalanceWithdrawPage.BalanceWithdrawPage(
          params: ModalRoute.of(context).settings.arguments,
        ),
      ),
  '/finance/tradingType': (context) => _allPageBuilder(
        future: TradingTypePage.loadLibrary(),
        builder: (context) => TradingTypePage.TradingTypePage(
          params: ModalRoute.of(context).settings.arguments,
        ),
      ),
  '/finance/phoneVerify': (context) => _allPageBuilder(
        future: PhoneVerifyPage.loadLibrary(),
        builder: (context) => PhoneVerifyPage.PhoneVerifyPage(
          params: ModalRoute.of(context).settings.arguments,
        ),
      ),

  /// 充值结果页面
  '/finance/rechargeResult': (context) => _allPageBuilder(
        future: RechargeResultPage.loadLibrary(),
        builder: (context) => RechargeResultPage.RechargeResultPage(
          params: ModalRoute.of(context).settings.arguments,
        ),
      ),

  /// 蜜蜂APP 测算服务费工具结果页面
  '/finance/beeCalcServiceFee': (context) => _allPageBuilder(
        future: CalcServiceFeeToolPage.loadLibrary(),
        builder: (context) => CalcServiceFeeToolPage.CalcServiceFeeToolPage(
          params: ModalRoute.of(context).settings.arguments,
        ),
      ),

  /// 智能账户流水明细
  '/finance/adFlowDetail': (context) => _allPageBuilder(
        future: AdFlowDetailPage.loadLibrary(),
        builder: (context) => AdFlowDetailPage.AdFlowDetailPage(
          params: ModalRoute.of(context).settings.arguments,
        ),
      ),

  /// 订单tab页面
  '/finance/OrderList': (context) => _allPageBuilder(
        future: OrderList.loadLibrary(),
        builder: (context) => OrderList.OrderListPage(
          param: ModalRoute.of(context).settings.arguments,
        ),
      ),
};
