import 'dart:async';

import 'package:flap/flap.dart';
import 'package:flutter/material.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>';

// 本地调试是否使用flap 0-不使用，1-使用
int useFlap = 0;
void main() {
  runZonedGuarded(() {
    runApp(MyApp());
  }, (error, stackTrace) {
    Zone.current.handleUncaughtError(error, stackTrace);
  });
}

class MyApp extends StatefulWidget {
  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    AppLifecycleNotifier.instance.value = AppLifecycleState.resumed;
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    AppLifecycleNotifier.instance.value = state;
    print("App 状态改变:$state");
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: useFlap == 1
          ? FlapWidget(
              flapId: 'finance',
              widgetName: 'HomePage',
              moduleName: 'waimai_e_fe_flutter_finance',
              minVersion: '0.0.0',
            )
          : Container(child: HomePage()),
    );
  }
}
