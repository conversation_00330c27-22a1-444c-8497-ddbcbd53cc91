import 'package:flutter/material.dart';

enum ArrowIconColorEnum { black, grey, orange }

const double pi = 3.1415926535897932;

enum DirectionEnum {
  up,
  down,
  left,
  right,
}

class ArrowIcon extends StatelessWidget {
  ArrowIcon({
    this.onTap,
    this.color = ArrowIconColorEnum.black,
    this.direction = DirectionEnum.right,
  });

  // 点击回调
  final Function onTap;

  // 箭头颜色
  final ArrowIconColorEnum color;

  final DirectionEnum direction;

  String getImageUrl() {
    String imageUrl;

    switch (this.color) {
      case ArrowIconColorEnum.grey:
        imageUrl =
            'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/27aa028dad636936701ddc3f46bf9c6f/arrow_right_grey.png';
        break;
      case ArrowIconColorEnum.orange:
        imageUrl =
            'https://p0.meituan.net/ingee/d025d6afdcdb6617eda125ae5f83f09b534.png';
        break;
      default:
        imageUrl =
            'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/85fcc140e8ea8c02ae796164f20f801d/arrow_right.png';
    }
    return imageUrl;
  }

  double getAngle() {
    double doubleAngle = 0;
    switch (this.direction) {
      case DirectionEnum.right:
        doubleAngle = 0;
        break;
      case DirectionEnum.left:
        doubleAngle = pi;
        break;
      case DirectionEnum.down:
        doubleAngle = pi / 2;
        break;
      case DirectionEnum.up:
        doubleAngle = pi * 1.5;
        break;
    }
    return doubleAngle;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (this.onTap != null) {
          this.onTap();
        }
      },
      child: Transform.rotate(
        angle: getAngle(),
        child: Image.network(
          getImageUrl(),
          width: 12,
          height: 12,
        ),
      ),
    );
  }
}
