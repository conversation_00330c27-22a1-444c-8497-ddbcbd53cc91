import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/util/index.dart';

Widget backPCTop(BuildContext context, {Function callback}) {
  return GestureDetector(
      child: Padding(
        padding: EdgeInsets.only(
          bottom: 12,
        ),
        child: Row(
          children: [
            Icon(
              Icons.arrow_back_ios_new,
              size: 12,
            ),
            Text('返回')
          ],
        ),
      ),
      onTap: () {
        if (callback != null) {
          callback();
        } else {
          Util.back(context);
        }
      });
}
