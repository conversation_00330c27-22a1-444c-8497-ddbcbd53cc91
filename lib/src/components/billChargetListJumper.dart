import 'package:ffw_components_package/ffw_components_package.dart';
import 'package:flutter/material.dart';
import 'package:mt_flutter_web_utils/mt_flutter_web_utils.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:roo_flutter/tools/response_system.dart';
import 'package:toast_widget_flutter/toast_widget_flutter.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/modal.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/orderDetail/index.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/billChargeListForOrder.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/util/index.dart';

class BillChargeListJumper extends StatelessWidget with DailyBillMixin {
  BillChargeListJumper(
      {@required this.child,
      @required this.bill,
      @required this.dailyBillDate,
      this.keys,
      this.from,
      this.toWmPoiId});

  final Widget child;
  final WmPoiBillChargeDynamicVoList bill;
  final String dailyBillDate;
  final String from;
  // 用作神抢手订单 第一次进入订单卡片详情标识
  final String keys;
  final String toWmPoiId;

  // 判断APP是否有点击跳转
  hasAction(row) {
    return [1, 2, 3].contains(row.appDetailType);
  }

  /// 跳转static/html的是老App h5页面，日期格式必须是-分割
  handleAppJump(BuildContext context, WmPoiBillChargeDynamicVoList bill,
      Map<String, dynamic> params) {
    if (hasAction(bill)) {
      if (bill.appDetailType == 1) {
        if (bill.appTemplate == 'new') {
          if (bill.chargeTypeCode == 6020 || bill.webTemplate == 'kv') {
            // 调账
            if (params["dailyBillDate"] != null) {
              params["dailyBillDate"] =
                  DateFormat.changeSplitChar(params["dailyBillDate"]);
              RouterTools.openWebPageUrl(
                  '/finance/static/html/adjustDetail.html',
                  params: params);
            }
          } else {
            RouterTools.flutterPageUrl(
              context,
              '/orderDetail',
              params: params,
            );
          }
        } else {
          if (params["dailyBillDate"] != null) {
            params["dailyBillDate"] =
                DateFormat.changeSplitChar(params["dailyBillDate"]);
            RouterTools.openWebPageUrl('/finance/static/html/orderDetail.html',
                params: params);
          }
        }
      } else if (bill.appDetailType == 2) {
        if (bill.chargeTypeCode == 6174 || bill.chargeTypeCode == 6198) {
          // 违约金类型
          String appUrl = bill.appUrl;
          if (appUrl != null && appUrl != '') {
            RouterTools.flutterPageUrl(context, appUrl);
          }
        } else if (bill.specialType == 1) {
          if (bill.isKa == 1) {
            Loading.showToast(message: '活动由总部提报，查询明细请联系总部');
          } else {
            RouterTools.openWebPageUrl(bill.appUrl);
          }
        } else if (bill != null && bill.appUrl?.isNotEmpty == true) {
          RouterTools.openWebPageUrl(bill.appUrl);
        }
      } else if (bill.appDetailType == 3) {
        // SD先收后返-查看服务费返还激励明细列表
        if (params["dailyBillDate"] != null) {
          params["dailyBillDate"] =
              DateFormat.changeSplitChar(params["dailyBillDate"]);
          RouterTools.openWebPageUrl('/finance/static/html/rebateList.html',
              params: params);
        }
      }
      if (from == 'daily') {
        ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_z9e7rynk_mc');
      } else {
        ReportLX.mc(
            pageKeyInfo, 'c_waimai_e_kj57nuct', 'b_waimai_e_tvzjvil5_mc');
      }
    }
  }

  // 内部页面跳转
  goToOrderDetailPage(BuildContext context, WmPoiBillChargeDynamicVoList bill,
      Map<String, dynamic> params) {
    Modal.openDialogPCAndPageApp(
      context,
      '/orderDetail',
      OrderDetailPage(
        params: params,
      ),
      params: params,
      title: '交易详情',
      width: 550,
      height: 650,
      backgroundColor: Color(0xFFF5F6FA),
    );
  }

  // webDetailType == 3
  // 以iframe的形式展示外部链接
  showIframeModal(
    BuildContext context,
    WmPoiBillChargeDynamicVoList bill,
  ) {
    String webUrl = bill?.webUrl ?? '';
    String path =
        webUrl.startsWith('http') ? webUrl : '${Util.getOriginUrl()}${webUrl}';
    Modal.showModalIframe(
      context,
      IFrame(
        src: path,
      ),
      '交易详情',
      width: 550,
      height: 700,
    );
  }

  // 其他跳转Web页面
  goToWebByBridge(
    WmPoiBillChargeDynamicVoList bill,
  ) {
    String webUrl = bill.webUrl;
    String path = webUrl[0] == '/' ? '${Util.getOriginUrl()}${webUrl}' : webUrl;
    MTFlutterWebUtils.bridgeJump(path);
  }

  handlePCJump(BuildContext context, WmPoiBillChargeDynamicVoList bill,
      Map<String, dynamic> params) {
    // 内部链接
    if (bill.webDetailType == 1) {
      goToOrderDetailPage(context, bill, params);
    } else if (bill.webDetailType == 2) {
      // 外部链接
      if (bill.specialType == 1) {
        if (bill.isKa == 1) {
          Loading.showToast(message: '活动由总部提报，查询明细请联系总部');
        } else {
          if (bill.webUrl?.isNotEmpty == true) {
            goToWebByBridge(bill);
          }
        }
      } else if (bill.webUrl?.isNotEmpty == true) {
        goToWebByBridge(bill);
      } else {
        goToOrderDetailPage(context, bill, params);
      }
    } else {
      // 违约金类型，直接跳转到规则中心
      if (bill.webUrl?.isNotEmpty == true &&
          (bill.chargeTypeCode == 6174 || bill.chargeTypeCode == 6198)) {
        showIframeModal(context, bill);
      } else {
        goToOrderDetailPage(context, bill, params);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      child: child,
      onTap: () {
        if (bill != null) {
          Map<String, dynamic> params = {
            "chargeTypeCode": bill.chargeTypeCode,
            "billChargeId": bill.billChargeId,
            "wmOrderViewId": bill.wmOrderViewId,
            "dailyBillDate": dailyBillDate,
            "keys": keys,
            "toWmPoiId": toWmPoiId,
          };
          ResponsiveSystem.bothAppPc(
              runApp: () => handleAppJump(context, bill, params),
              runPc: () => handlePCJump(context, bill, params));
        }
      },
    );
  }
}
