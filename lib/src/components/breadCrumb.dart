import 'package:flutter/material.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_fe_ffw_utils/platform_tool.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/uiTools.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/util/index.dart';

Color hoverColor = Color(0xFFF89800);

class Breadcrumb extends StatefulWidget {
  Breadcrumb({this.items});
  final List<Map<String, dynamic>> items;
  @override
  _BreadcrumbState createState() => _BreadcrumbState();
}

class _BreadcrumbState extends State<Breadcrumb> {
  int selectIndex = null;

  List<Widget> renderBreads() {
    final List<Map<String, dynamic>> items = widget.items;
    List<Widget> arr = [];
    if (items == null) {
      return arr;
    }
    items.asMap().forEach((int index, Map<String, dynamic> item) {
      String url = item['url'];
      bool urlIsNotEmpty = url != null && url != '';
      arr.add(
        MouseRegion(
          onHover: (event) {
            if (urlIsNotEmpty) {
              selectIndex = index;
              setState(() {});
            }
          },
          onExit: (event) {
            selectIndex = null;
            setState(() {});
          },
          cursor: urlIsNotEmpty ? SystemMouseCursors.click : MouseCursor.defer,
          child: GestureDetector(
            onTap: () {
              if (urlIsNotEmpty) {
                if (PlatformTools.isPC) {
                  Util.href(url);
                } else {
                  String url = SchemeUrls.flutterPageUrl(item['url'],
                      channel: 'waimai_e_flutter');
                  RouteUtils.open(url);
                }
              }
            },
            child: Container(
              child: Text(
                item['title'] ?? '无标题',
                style: TextStyle(
                  color: selectIndex == index
                      ? Color(0xFFf89800)
                      : Color(0xFF858692),
                  fontSize: 14,
                ),
              ),
            ),
          ),
        ),
      );
      if (index != items.length - 1) {
        arr.add(
          Center(
              child: Image.network(
            'http://p0.meituan.net/scarlett/5fff794eec6c89db69999b98c9121a932083.png',
            width: 14,
            height: 14,
          )),
        );
      }
    });
    return arr;
  }

  @override
  Widget build(BuildContext context) {
    if (!UITools.isPc) {
      return SizedBox.shrink();
    }
    return Container(
      padding: EdgeInsets.only(left: 12, top: 12),
      alignment: Alignment.center,
      child: Row(
        children: renderBreads(),
      ),
    );
  }
}
