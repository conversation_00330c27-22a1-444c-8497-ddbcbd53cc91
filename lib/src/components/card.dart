import 'package:flutter/material.dart';
import 'package:roo_flutter/tools/response_system.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/gradient.dart';

class RadiusCard extends StatelessWidget {
  RadiusCard({
    @required this.child,
    this.padding = const EdgeInsets.all(16),
    this.margin = const EdgeInsets.all(0),
    this.width,
    this.height,
    this.isActive = false,
    this.onClick,
  });
  final Widget child;
  final EdgeInsets padding;
  final EdgeInsets margin;
  final double width;
  final double height;
  // 是否是选中状态
  final bool isActive;
  // 点击卡片的回调事件
  final Function onClick;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Container(
        width: width,
        height: height,
        padding: padding,
        margin: margin,
        decoration: BoxDecoration(
          color: Colors.white,
          gradient: isActive ? yellowGradient : null,
          boxShadow: [
            BoxShadow(
              color: Color(0x0D000000),
              offset: Offset(0, 1),
              blurRadius: 10.5,
            )
          ],
          borderRadius: BorderRadius.circular(
              ResponsiveSystem.bothAppPc(runApp: 10.5, runPc: 8)),
        ),
        child: child,
      ),
      onTap: () {
        if (onClick != null) {
          onClick();
        }
      },
    );
  }
}
