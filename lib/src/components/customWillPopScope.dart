import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/platformTool.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/util/index.dart';

class CustomWillPopScope extends StatelessWidget {
  const CustomWillPopScope({
    @required this.child,
    @required this.from,
    this.forbiddenPcBack = false,
  });

  /// 监听路由弹出的子元素
  final Widget child;

  /// 业务历史逻辑，判断页面路由来源
  final String from;

  /// 是否需要禁止PC上返回操作
  final bool forbiddenPcBack;

  handleOnWillPop(BuildContext context) async {
    // from还可能来自，其他技术栈下的iframe， flutter内部路由跳转时from=flutter
    if (from != 'flutter') {
      if (PlatformTool.isPC && forbiddenPcBack == true) {
        // PC 页面为弹窗展示，禁止浏览器返回，do nothing
        return false;
      } else {
        // web下使用html.window.history.back(); 上一个页面是PC老页面的时候
        Util.back(context);
      }
    } else {
      // 走flutter页面内部路由跳转
      Navigator.pop(context);
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      child: child,

      /// 在 iOS 上，这个类的作用仅有一个，就是 Swipe Back 手势被取消，回调也永远不会触发，建议native侧路由返回写到navBar里面
      /// 此方法仅用做 web上特殊返回跳转方式，或者阻止浏览器后退行为
      onWillPop: PlatformTool.isWeb ? () => handleOnWillPop(context) : null,
    );
  }
}
