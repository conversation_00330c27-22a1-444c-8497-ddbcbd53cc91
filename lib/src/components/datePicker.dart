import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

const double itemHeight = 42.0;

Future<DateTime> showCustomDatePicker({
  @required BuildContext context,
  @required DateTime initialDate,
  @required DateTime firstDate,
  @required DateTime lastDate,
}) {
  Widget dialog = _DatePickerDialog(
    firstDate: firstDate,
    lastDate: lastDate,
    initialDate: initialDate,
  );
  return showModalBottomSheet<DateTime>(
      context: context,
      builder: (BuildContext context) {
        return dialog;
      });
}

class _DatePickerDialog extends StatefulWidget {
  final DateTime initialDate;
  final DateTime firstDate;
  final DateTime lastDate;

  _DatePickerDialog({
    @required this.initialDate,
    @required this.firstDate,
    @required this.lastDate,
  })  : assert(initialDate != null),
        assert(firstDate != null),
        assert(lastDate != null),
        super() {
    assert(!this.lastDate.isBefore(this.firstDate),
        'lastDate ${this.lastDate} must be on or after firstDate ${this.firstDate}.');
    assert(!this.initialDate.isBefore(this.firstDate),
        'initialDate ${this.initialDate} must be on or after firstDate ${this.firstDate}.');
    assert(!this.initialDate.isAfter(this.lastDate),
        'initialDate ${this.initialDate} must be on or before lastDate ${this.lastDate}.');
  }
  @override
  State<StatefulWidget> createState() => _DatePickerDialogState();
}

class _DatePickerDialogState extends State<_DatePickerDialog> {
  DateTime _selectedDate;

  @override
  initState() {
    super.initState();
    _selectedDate = widget.initialDate;
  }

  _handleDaySelected(DateTime day) {
    setState(() {
      _selectedDate = day;
    });
    Navigator.pop(context, _selectedDate);
  }

  @override
  build(BuildContext context) {
    return _DatePicker(
      firstDate: widget.firstDate,
      lastDate: widget.lastDate,
      initialDate: _selectedDate,
      onChanged: _handleDaySelected,
    );
  }
}

class _DatePicker extends StatefulWidget {
  final DateTime initialDate;
  final DateTime firstDate;
  final DateTime lastDate;
  final ValueChanged<DateTime> onChanged;

  _DatePicker({
    @required this.initialDate,
    @required this.firstDate,
    @required this.lastDate,
    @required this.onChanged,
  });

  @override
  State<StatefulWidget> createState() => _DatePickerState();
}

class _DatePickerState extends State<_DatePicker> {
  DateTime displayDate;
  DateTime firstDate;
  DateTime lastDate;

  bool openYearSelector = false;
  @override
  initState() {
    super.initState();
    displayDate = widget.initialDate;
    firstDate = widget.firstDate;
    lastDate = widget.lastDate;
  }

  _buildBtn(int type) {
    return IconButton(
      icon: type == 1 ? Icon(Icons.chevron_left) : Icon(Icons.chevron_right),
      onPressed: () {
        if (type == 1) {
          DateTime preMonth = DateTime(
            displayDate.year,
            displayDate.month - 1,
          );
          if (preMonth.isBefore(DateTime(firstDate.year, firstDate.month))) {
            return;
          }
          displayDate = preMonth;
        } else {
          DateTime nextMonth = DateTime(
            displayDate.year,
            displayDate.month + 1,
          );
          if (nextMonth.isAfter(DateTime(lastDate.year, lastDate.month))) {
            return;
          }
          displayDate = nextMonth;
        }
        setState(() {});
      },
    );
  }

  _monthTap() {
    openYearSelector = !openYearSelector;
    setState(() {});
  }

  _buildYearMonth() {
    return Container(
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: _monthTap,
        child: Row(
          children: [
            Text(
              '${displayDate.year}年${displayDate.month}月',
              style: TextStyle(
                color: Color(0xFF222426),
                fontWeight: FontWeight.w500,
                fontSize: 16,
              ),
            ),
            Icon(
              openYearSelector ? Icons.arrow_drop_up : Icons.arrow_drop_down,
            ),
          ],
        ),
      ),
    );
  }

  _buildHeader() {
    return Container(
      height: 50,
      padding: EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFEEEEEE), width: 0.5),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          openYearSelector ? SizedBox() : _buildBtn(1),
          _buildYearMonth(),
          openYearSelector ? SizedBox() : _buildBtn(2),
        ],
      ),
    );
  }

  _buildWeekName(String name) {
    return Center(
      child: Text(
        '$name',
        style: TextStyle(
          color: Color(0xFF999999),
          fontWeight: FontWeight.w400,
          fontSize: 14,
        ),
      ),
    );
  }

  int firstDayOffset(int year, int month) {
    // 0-based day of week for the month and year, with 0 representing Monday.
    final int weekdayFromMonday = DateTime(year, month).weekday - 1;

    // 0-based start of week depending on the locale, with 0 representing Sunday.
    int firstDayOfWeekIndex = 7;

    // firstDayOfWeekIndex recomputed to be Monday-based, in order to compare with
    // weekdayFromMonday.
    firstDayOfWeekIndex = (firstDayOfWeekIndex - 1) % 7;

    // Number of days between the first day of week appearing on the calendar,
    // and the day corresponding to the first of the month.
    return (weekdayFromMonday - firstDayOfWeekIndex) % 7;
  }

  _buildWeeks() {
    List<Widget> labels = [];
    ['日', '一', '二', '三', '四', '五', '六'].forEach((ele) {
      labels.add(_buildWeekName(ele));
    });
    return Container(
      height: itemHeight,
      child: GridView.custom(
        gridDelegate: _DayGridDelegates(),
        childrenDelegate: SliverChildListDelegate(
          labels,
          addRepaintBoundaries: false,
        ),
      ),
    );
  }

  _isToday(DateTime inputDt) {
    if (inputDt == null) {
      return false;
    }
    DateTime now = DateTime.now();
    if (now.year == inputDt.year &&
        now.month == inputDt.month &&
        now.day == inputDt.day) {
      return true;
    }
    return false;
  }

  _buildDay(DateTime dateTime) {
    String text = '';
    int day = dateTime?.day ?? 0;
    if (day != 0) {
      text = day < 10 ? '0$day' : '$day';
    }
    Widget dayWidget;

    bool dateIsAvailable =
        (dateTime.isBefore(lastDate) && dateTime.isAfter(firstDate)) ||
            dateTime.isAtSameMomentAs(lastDate) ||
            dateTime.isAtSameMomentAs(firstDate);

    BoxDecoration boxDecoration;
    if (_isToday(dateTime)) {
      boxDecoration = BoxDecoration(
        border: Border.all(width: 1, color: Color(0xFFFFCC33)),
        borderRadius: BorderRadius.circular(2),
      );
    }
    DateTime iniDate = widget.initialDate;
    if (dateTime.year == iniDate.year &&
        dateTime.month == iniDate.month &&
        dateTime.day == iniDate.day) {
      boxDecoration = BoxDecoration(
        color: Color(0xFFFFCC33),
        borderRadius: BorderRadius.circular(2),
      );
    }

    dayWidget = Container(
      alignment: Alignment.center,
      decoration: boxDecoration,
      child: Text(
        text,
        style: TextStyle(
          color: dateIsAvailable ? Color(0xFF222222) : Color(0xFF999999),
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      ),
    );

    // if (_isToday(dateTime)) {
    //   dayWidget = Container(
    //     alignment: Alignment.center,
    //     decoration: BoxDecoration(
    //       color: Color(0xFFFFCC33),
    //       borderRadius: BorderRadius.circular(2),
    //     ),
    //     child: Text(
    //       '今天',
    //       style: TextStyle(
    //           fontWeight: FontWeight.w500,
    //           color: Color(0xFF222222),
    //           fontSize: 14),
    //     ),
    //   );
    // } else if (dateTime.isAtSameMomentAs(displayDate)) {
    //   dayWidget = Container(
    //     alignment: Alignment.center,
    //     decoration: BoxDecoration(
    //       color: Color(0xFFFFCC33),
    //       borderRadius: BorderRadius.circular(2),
    //     ),
    //     child: Text(
    //       text,
    //       style: TextStyle(
    //           fontWeight: FontWeight.w500,
    //           color: Color(0xFF222222),
    //           fontSize: 14),
    //     ),
    //   );
    // } else {
    //   dayWidget = Text(
    //     text,
    //     style: TextStyle(
    //         color: dateIsAvailable ? Color(0xFF222222) : Color(0xFF999999),
    //         fontSize: 14,
    //         fontWeight: FontWeight.w500),
    //   );
    // }

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (dateIsAvailable == false) {
          return;
        }
        widget?.onChanged(dateTime);
      },
      child: Center(
        child: dayWidget,
      ),
    );
  }

  _buildDays() {
    DateTime nextMonth = DateTime(displayDate.year, displayDate.month + 1, 1);
    DateTime displayMonth = DateTime.fromMillisecondsSinceEpoch(
        nextMonth.millisecondsSinceEpoch - 1);

    List<Widget> itemList = [];

    // 距离周日空x位
    int offset = firstDayOffset(displayDate.year, displayDate.month);
    // 这个月有多少天
    int daysInMonth = displayMonth.day;

    int year = displayMonth.year;
    int month = displayMonth.month;

    int day = -offset;
    while (day < daysInMonth) {
      day++;
      if (day < 1) {
        itemList.add(Container());
      } else {
        DateTime dayToBuild = DateTime(year, month, day);
        itemList.add(_buildDay(dayToBuild));
      }
    }

    return Container(
      height: 250,
      child: GridView.builder(
        itemCount: itemList.length,
        gridDelegate: _DayGridDelegates(),
        itemBuilder: (context, index) {
          return itemList[index];
        },
      ),
    );
  }

  _buildContent() {
    return Container(
      padding: EdgeInsets.all(16),
      child: Container(
        child: Column(
          children: [_buildWeeks(), _buildDays()],
        ),
      ),
    );
  }

  _buildYear(int index) {
    DateTime initialDate = widget.initialDate;
    int year = initialDate.year - 8 + index;
    bool selected = year == displayDate.year;
    bool isDisable = (year < firstDate.year || year > lastDate.year);
    return GestureDetector(
      onTap: () {
        if (isDisable) {
          return;
        }
        displayDate = DateTime(year, initialDate.month, initialDate.day);
        openYearSelector = false;
        setState(() {});
      },
      child: Center(
        child: Container(
          width: 70,
          height: 40,
          decoration: BoxDecoration(
              color: selected ? Color(0xFFFFCC33) : Colors.transparent,
              borderRadius: BorderRadius.circular(2)),
          alignment: Alignment.center,
          child: Text(
            '$year',
            style: TextStyle(
              color: selected || !isDisable
                  ? Color(0xFF222222)
                  : Color(0xFF999999),
            ),
          ),
        ),
      ),
    );
  }

  _buildYearSelector() {
    return openYearSelector
        ? Container(
            margin: EdgeInsets.only(top: 50),
            height: 300,
            color: Colors.white,
            child: GridView.builder(
              itemCount: 12,
              gridDelegate: _YearPickerGridDelegates(),
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemBuilder: (context, index) {
                return _buildYear(index);
              },
            ),
          )
        : SizedBox();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 400,
      child: Stack(
        children: [
          Container(
            color: Color(0xFFFFFFFF),
            child: Column(
              children: [
                _buildHeader(),
                _buildContent(),
              ],
            ),
          ),
          // 年选择器
          _buildYearSelector(),
        ],
      ),
    );
  }
}

class _DayGridDelegates extends SliverGridDelegate {
  @override
  SliverGridLayout getLayout(SliverConstraints constraints) {
    print('_DayGridDelegate constraints $constraints');

    const int columnCount = DateTime.daysPerWeek;
    double tileWidth = constraints.crossAxisExtent / columnCount;
    // double tileHeight = constraints.viewportMainAxisExtent / rowCount;
    double tileHeight = itemHeight;
    return SliverGridRegularTileLayout(
      childCrossAxisExtent: tileWidth,
      childMainAxisExtent: tileHeight,
      crossAxisCount: columnCount,
      crossAxisStride: tileWidth,
      mainAxisStride: tileHeight,
      reverseCrossAxis: false,
    );
  }

  @override
  bool shouldRelayout(_DayGridDelegates oldDelegate) => false;
}

class _YearPickerGridDelegates extends SliverGridDelegate {
  @override
  SliverGridLayout getLayout(SliverConstraints constraints) {
    print('constraints $constraints');

    const int _yearPickerColumnCount = 3;
    const int _yearPickerRowCount = 4;
    const double _yearPickerRowSpacing = 8.0;
    final double tileWidth = (constraints.crossAxisExtent -
            (_yearPickerColumnCount - 1) * _yearPickerRowSpacing) /
        _yearPickerColumnCount;
    final double tileHeight = (constraints.viewportMainAxisExtent -
            (_yearPickerRowCount - 1) * _yearPickerRowSpacing) /
        _yearPickerRowCount;
    return SliverGridRegularTileLayout(
      childCrossAxisExtent: tileWidth,
      childMainAxisExtent: tileHeight,
      crossAxisCount: _yearPickerColumnCount,
      crossAxisStride: tileWidth + _yearPickerRowSpacing,
      mainAxisStride: tileHeight + _yearPickerRowSpacing,
      reverseCrossAxis: false,
    );
  }

  @override
  bool shouldRelayout(_YearPickerGridDelegates oldDelegate) => false;
}
