import 'package:flutter/material.dart';
import 'package:roo_flutter/basic_components/date_picker_adapted/date_picker_input.dart';
import 'package:roo_flutter/basic_components/date_picker_adapted/date_picker_utils.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/date_picker_adapted/date_picker_adapted.dart';
// import 'package:roo_flutter/basic_components/date_picker_adapted/roo_date_picker_adapted.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/date_picker_adapted/roo_date_picker_adapted.dart';

class AppDatePicker extends StatefulWidget {
  /// 日期默认值, 不传入默认今天
  final DateTime initialDate;

  /// 起始日期限制，该日期之前不可选择
  final DateTime firstDate;

  /// 截止日期限制，该日期之后不可选择
  final DateTime lastDate;

  /// 选择日期完成时的回调
  final ValueChanged<DateTime> onChanged;

  // 输入框中显示文本
  final String hintText;

  /// 时间选择控制器
  final DatePickerControllers controller;

  /// 自定义展示
  final Widget customDisplayWidget;

  AppDatePicker(
      {@required this.initialDate,
      @required this.firstDate,
      @required this.lastDate,
      @required this.onChanged,
      this.hintText,
      this.controller,
      this.customDisplayWidget});

  @override
  State<StatefulWidget> createState() => AppDatePickerState();
}

class AppDatePickerState extends State<AppDatePicker> {
  final TextEditingController _controller = TextEditingController();
  DateTime selectedDt;

  /// 时间选择控制器
  DatePickerControllers _dateController;

  @override
  initState() {
    super.initState();

    /// 初始化dateController控制器
    if (widget.controller != null) {
      _dateController = widget.controller;
      _dateController.date = _dateController.date ?? widget.initialDate;
      _dateController.dateEarly = _dateController.dateEarly ?? widget.firstDate;
      _dateController.dateLate = _dateController.dateLate ?? widget.lastDate;
    } else {
      _dateController = DatePickerControllers(
          date: widget.initialDate,
          dateEarly: widget.firstDate,
          dateLate: widget.lastDate);
    }

    _dateController.addListener(() {
      setState(() {
        _controller.text = RooDatePickerUtil.formatDate(_dateController.date);
      });
    });

    _controller.text = RooDatePickerUtil.formatDate(_dateController.date);
  }

  @override
  Widget build(BuildContext context) {
    DatePickerAdapted picker = DatePickerAdapted(
      initialDate: _dateController.date,
      firstDate: _dateController.dateEarly ?? RooDatePickerUtil.startDate,
      lastDate: _dateController.dateLate ?? RooDatePickerUtil.endDate,
      onChanged: (DateTime dt) {
        _dateController.date = dt;
        if (widget.onChanged != null) widget.onChanged(dt);
        Navigator.pop(context, dt);
      },
    );

    return widget.customDisplayWidget != null
        ? GestureDetector(
            child: widget.customDisplayWidget,
            onTap: () {
              showModalBottomSheet(
                context: context,
                builder: (context) {
                  return picker;
                },
              );
            },
          )
        : RooDatePickerInput(
            initialDate: _dateController.date,
            controller: _controller,
            hintText: widget.hintText,
            onTap: () {
              showModalBottomSheet(
                context: context,
                builder: (context) {
                  return picker;
                },
              );
            },
          );
  }
}
