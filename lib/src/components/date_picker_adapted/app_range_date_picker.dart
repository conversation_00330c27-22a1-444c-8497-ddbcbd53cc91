import 'package:flutter/material.dart';
import 'package:roo_flutter/basic_components/date_picker_adapted/date_picker_adapted.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/date_picker_adapted/app_scroll_date_picker.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/date_picker_adapted/roo_date_picker_adapted.dart';

import 'date_picker_utils.dart';

class AppRangeDatePicker extends StatefulWidget {
  /// 开始和结束的选中日期限制
  final List<DateTime> rangeInitialDate;

  /// 开始和结束的起始日期限制
  final List<DateTime> rangeFirstLastDate;

  /// 选择日期完成时的回调
  final ValueChanged<List<DateTime>> onRangeChange;

  /// 选择开始日期完成时的回调
  final ValueChanged<DateTime> onFirseRangeChange;

  /// 控制器
  final RangeDatePickerControllers controller;

  /// 是否合并开始结束日期选择
  final bool isCalendar;

  final bool isScroll;

  /// 自定义展示
  final Widget customDisplayWidget;

  AppRangeDatePicker(
      {this.rangeFirstLastDate,
      this.rangeInitialDate,
      this.onRangeChange,
      this.controller,
      this.isCalendar = false,
      this.isScroll = false,
      this.customDisplayWidget,
      this.onFirseRangeChange});

  @override
  State<StatefulWidget> createState() => _AppRangeDatePickerState();
}

class _AppRangeDatePickerState extends State<AppRangeDatePicker> {
  /// 时间选择控制器
  RangeDatePickerControllers _controller;

  /// 开始日期最早时间限制
  DateTime startDateEarly;

  /// 开始日期最晚时间限制
  DateTime startDateLate;

  /// 结束日期最早时间限制
  DateTime endDateEarly;

  /// 结束日期最晚时间限制
  DateTime endDateLate;

  /// 开始日期
  DateTime startDate;

  /// 结束日期
  DateTime endDate;

  List<DateTime> selectedDate = [];

  GlobalKey<DatePickerState> myGlobalKey = GlobalKey<DatePickerState>();

  @override
  initState() {
    super.initState();

    if (widget.controller != null) {
      _controller = widget.controller;
      _controller.rangeDate = _controller.rangeDate ?? widget.rangeInitialDate;
    } else {
      _controller =
          RangeDatePickerControllers(rangeDate: widget.rangeInitialDate);
    }

    _controller.addListener(() {
      setState(() {
        updateDate(_controller.rangeDate);
      });
    });

    /// 初始化开始和结束日期
    updateDate(_controller.rangeDate);

    /// 初始化开始和结束日期范围
    updateRangeDate(widget.rangeFirstLastDate);
  }

  @override
  // ignore: must_call_super
  void didUpdateWidget(covariant AppRangeDatePicker oldWidget) {
    if (oldWidget.rangeFirstLastDate != widget.rangeFirstLastDate) {
      /// 更新开始和结束的起始日期限制
      updateRangeDate(widget.rangeFirstLastDate);
    }
  }

  updateDate(List<DateTime> rangeDate) {
    if (rangeDate != null && rangeDate.length == 2) {
      startDate = rangeDate[0];
      endDate = rangeDate[1];
    }
  }

  /// 开始&结束分开时的处理方式
  onChange(DateTime dt, int index) {
    if (index == 1) {
      startDate = dt;
    } else {
      endDate = dt;
    }

    /// 当开始日期和结束日期都选择时才进行排序并触发onRangeChange回调
    if (startDate != null && endDate != null) {
      if (startDate.isAfter(endDate)) {
        /// 在已经选中日期的情况下，需要对值进行替换
        DateTime temp = startDate;
        startDate = endDate;
        endDate = temp;
      }
      if (widget.onRangeChange != null) {
        widget.onRangeChange([startDate, endDate]);
      }
    }
    _controller.rangeDate = [startDate, endDate];
  }

  /// 开始&结束合并时的处理方式
  onRangeChange(BuildContext context, DateTime dt) {
    selectedDate.add(dt);
    // 排序
    selectedDate.sort();
    if (selectedDate.length == 1) {
      // 隐藏结束日期方块，修改子组件中的值
      myGlobalKey.currentState?.updateSelectedDate(dt);
      myGlobalKey.currentState?.updateAnotherInitDate(null);
    } else if (selectedDate.length == 2) {
      // 重新初始化
      startDate = selectedDate[0];
      endDate = selectedDate[1];
      myGlobalKey.currentState?.updateSelectedDate(startDate);
      myGlobalKey.currentState?.updateAnotherInitDate(endDate);
      if (widget.onRangeChange != null) {
        widget.onRangeChange([startDate, endDate]);
      }
      _controller.rangeDate = selectedDate;
      // 清空，下次再加
      selectedDate = [];
      Navigator.pop(context, dt);
    }
  }

  /// 更新开始和结束日期限制
  updateRangeDate(List<DateTime> rangeDate) {
    if (rangeDate != null && rangeDate.length == 2) {
      startDateEarly = endDateEarly = rangeDate[0];
      startDateLate = endDateLate = rangeDate[1];
    } else {
      startDateEarly = endDateEarly = RooDatePickerUtil.startDate;
      startDateLate = endDateLate = RooDatePickerUtil.endDate;
    }
  }

  Widget _buildText(DateTime dt, String defaultText) {
    String newText = RooDatePickerUtil.formatDate(dt);
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 10),
      child: Text(
        '${newText != '' ? newText : defaultText}',
        style: TextStyle(fontSize: 12),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // 开始选择框
    DatePickerAdapted picker1 = DatePickerAdapted(
      initialDate: startDate,
      firstDate: startDateEarly,
      lastDate: startDateLate,
      onChanged: (DateTime dt) {
        onChange(dt, 1);
        Navigator.pop(context, dt);
      },
    );

    // 结束选择框
    DatePickerAdapted picker2 = DatePickerAdapted(
      initialDate: endDate,
      firstDate: endDateEarly,
      lastDate: endDateLate,
      onChanged: (DateTime dt) {
        onChange(dt, 2);
        Navigator.pop(context, dt);
      },
    );

    // 开始&结束选择框
    DatePickerAdapted picker3 = DatePickerAdapted(
      key: myGlobalKey,
      initialDate: startDate,
      anotherInitDate: endDate,
      firstDate: startDateEarly,
      lastDate: endDateLate,
      onChanged: (DateTime dt) {
        onRangeChange(context, dt);
      },
    );

    return Padding(
      padding: EdgeInsets.all(10),
      child: Row(
        children: !widget.isCalendar
            ? [
                GestureDetector(
                  child: _buildText(startDate, '开始时间'),
                  onTap: () {
                    showModalBottomSheet(
                      context: context,
                      builder: (context) {
                        return picker1;
                      },
                    );
                  },
                ),
                _buildText(null, '至'),
                GestureDetector(
                  child: _buildText(endDate, '结束时间'),
                  onTap: () {
                    showModalBottomSheet(
                      context: context,
                      builder: (context) {
                        return picker2;
                      },
                    );
                  },
                ),
              ]
            : [
                GestureDetector(
                  child: widget.customDisplayWidget != null
                      ? widget.customDisplayWidget
                      : Row(
                          children: [
                            _buildText(startDate, '开始时间'),
                            _buildText(null, '至'),
                            _buildText(endDate, '结束时间'),
                          ],
                        ),
                  onTap: () {
                    showModalBottomSheet(
                      // isScrollControlled: true,
                      context: context,
                      builder: (context) {
                        return widget.isScroll
                            ? ScrollPicker(
                                rangeInitialDate: [startDate, endDate],
                                rangeFirstLastDate: widget.rangeFirstLastDate,
                                onRangeChange: (dtList) {
                                  widget.onRangeChange(dtList);
                                  Navigator.pop(context, dtList);
                                  setState(() {
                                    startDate = dtList[0];
                                    endDate = dtList[1];
                                  });
                                },
                                onFirseRangeChange: (dts) {
                                  widget.onFirseRangeChange(dts);
                                  // Navigator.pop(context, dts);
                                  setState(() {
                                    startDate = dts;
                                    // endDate = dtList[1];
                                  });
                                },
                                controller: widget.controller,
                              )
                            : picker3;
                      },
                    );
                  },
                ),
              ],
      ),
    );
  }
}