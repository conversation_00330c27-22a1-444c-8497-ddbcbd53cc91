import 'package:flutter/material.dart';
import 'package:roo_flutter/basic_components/date_picker_adapted/date_picker_adapted.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/date_picker_adapted/roo_date_picker_adapted.dart';

import 'date_picker_utils.dart';

class ScrollPicker extends StatefulWidget {
  /// 开始和结束的选中日期限制
  final List<DateTime> rangeInitialDate;

  /// 开始和结束的起始日期限制
  final List<DateTime> rangeFirstLastDate;

  /// 选择开始日期完成时的回调
  final ValueChanged<DateTime> onFirseRangeChange;

  /// 选择日期完成时的回调
  final ValueChanged<List<DateTime>> onRangeChange;

  /// 控制器
  final RangeDatePickerControllers controller;

  ScrollPicker({
    this.rangeFirstLastDate,
    this.rangeInitialDate,
    this.onRangeChange,
    this.controller,
    this.onFirseRangeChange,
  });

  @override
  State<StatefulWidget> createState() => _ScrollPicker();
}

class _ScrollPicker extends State<ScrollPicker> {
  /// 时间选择控制器
  RangeDatePickerControllers _controller;

  /// 开始日期最早时间限制
  DateTime startDateEarly;

  /// 开始日期最晚时间限制
  DateTime startDateLate;

  /// 结束日期最早时间限制
  DateTime endDateEarly;

  /// 结束日期最晚时间限制
  DateTime endDateLate;

  /// 开始日期
  DateTime startDate;

  /// 结束日期
  DateTime endDate;

  /// 已选择的日期
  List<DateTime> selectedDate = [];

  /// 存储为滚动时所有的日历
  List<GlobalKey<DatePickerState>> myGlobalKeys = [];

  /// 日期打开默认滚动到当前日期
  final ScrollController _scrollController =
      ScrollController(initialScrollOffset: 823.4);

  controllerListener() {
    setState(() {
      updateDate(_controller.rangeDate);
    });
  }

  @override
  initState() {
    super.initState();

    if (widget.controller != null) {
      _controller = widget.controller;
      _controller.rangeDate = _controller.rangeDate ?? widget.rangeInitialDate;
    } else {
      _controller =
          RangeDatePickerControllers(rangeDate: widget.rangeInitialDate);
    }

    _controller.addListener(controllerListener);

    /// 初始化开始和结束日期
    updateDate(_controller.rangeDate);

    /// 初始化开始和结束日期范围
    updateRangeDate(widget.rangeFirstLastDate);

    // _scrollController.initialScrollOffset = 0.0;
  }

  @override
  void dispose() {
    _controller.removeListener(controllerListener);
    super.dispose();
  }

  @override
  // ignore: must_call_super
  void didUpdateWidget(covariant ScrollPicker oldWidget) {
    if (oldWidget.rangeFirstLastDate != widget.rangeFirstLastDate) {
      /// 更新开始和结束的起始日期限制
      updateRangeDate(widget.rangeFirstLastDate);
    }
  }

  updateDate(List<DateTime> rangeDate) {
    if (rangeDate != null && rangeDate.length == 2) {
      startDate = rangeDate[0];
      endDate = rangeDate[1];
    }
  }

  /// 开始&结束合并时的处理方式
  onRangeChange(BuildContext context, DateTime dt) {
    selectedDate.add(dt);
    // 排序
    selectedDate.sort();
    if (selectedDate.length == 1) {
      setState(() {
        startDate = dt;
        endDate = null;
      });
      if (widget.onFirseRangeChange != null) {
        widget.onFirseRangeChange(dt);
      }
      int indays = DateTime.now().difference(dt).inDays;
      int indays1 = dt
          .difference(DateTime(DateTime.now().year, DateTime.now().month,
              DateTime.now().day - 89))
          .inDays;

      print(indays1);
      _controller.rangeDate = selectedDate;
      updateRangeDate([
        dt.subtract(Duration(days: indays1 < 31 ? indays1 : 31)),
        dt.add(Duration(days: indays < 31 ? indays : 31))
      ]);
    } else if (selectedDate.length == 2) {
      // 重新初始化ß
      setState(() {
        startDate = selectedDate[0];
        endDate = selectedDate[1];
      });
      if (widget.onRangeChange != null) {
        widget.onRangeChange([startDate, endDate]);
      }
      _controller.rangeDate = selectedDate;
      // 清空，下次再加
      selectedDate = [];
    }
  }

  /// 更新开始和结束日期限制
  updateRangeDate(List<DateTime> rangeDate) {
    if (rangeDate != null && rangeDate.length == 2) {
      startDateEarly = endDateEarly = rangeDate[0];
      startDateLate = endDateLate = rangeDate[1];
    } else {
      startDateEarly = endDateEarly = RooDatePickerUtil.startDate;
      startDateLate = endDateLate = RooDatePickerUtil.endDate;
    }
  }

  /// 构建滚动日历列表
  List<Widget> _buildScrollPickerList() {
    DateTime startMonth =
        DateTime(startDateEarly.year, startDateEarly.month, 1);
    DateTime endMonth = DateTime(endDateLate.year, endDateLate.month, 2);
    // ignore: non_constant_identifier_names
    List<Widget> PickerList = [];
    Widget curPicker;
    for (DateTime curMonth = startMonth;
        curMonth.isBefore(endMonth);
        curMonth = RooDatePickerUtil.nextMonth(curMonth)) {
      GlobalKey curGlobalKey = GlobalKey<DatePickerState>();
      myGlobalKeys.add(curGlobalKey);
      curPicker = DatePickerAdapted(
        key: curGlobalKey,
        initialDate: startDate,
        anotherInitDate: endDate,
        firstDate: startDateEarly,
        lastDate: endDateLate,
        onChanged: (DateTime dt) {
          onRangeChange(context, dt);
        },
        isScroll: true,
        displayMonth: curMonth,
      );
      PickerList.add(curPicker);
    }
    return PickerList;
  }

  @override
  Widget build(context) {
    return Container(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.7,
        ),
        child: Column(
          children: [
            Container(
              alignment: Alignment.center,
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                      color: Color.fromARGB(221, 229, 229, 229), width: 0.5),
                ),
                color: Color(0xFFFFFFFF),
              ),
              height: 36,
              child: Text(
                '选择日期',
                style: TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
              ),
            ),
            Expanded(
              child: GridView.count(
                shrinkWrap: true,
                crossAxisCount: 1,
                children: _buildScrollPickerList(),
                childAspectRatio: 5 / 4,
                controller: _scrollController,
              ),
            ),
          ],
        ));
  }
}
