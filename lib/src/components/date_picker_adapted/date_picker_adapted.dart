import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
// import 'package:roo_flutter/roo_theme/theme.dart';
// import 'package:roo_flutter/roo_theme/theme_data.dart';
import 'package:roo_flutter/tools/response_system.dart';
// import 'package:waimai_e_fe_flutter_finance/src/components/date_picker_adapted/roo_date_picker_adapted_theme.dart';
// import 'package:waimai_e_fe_flutter_finance/src/components/roo_theme/theme.dart';
// import 'package:waimai_e_fe_flutter_finance/src/components/roo_theme/theme_data.dart';

typedef ClickCallback = void Function(DateTime dt);

class DatePickerAdapted extends StatefulWidget {
  final DateTime firstDate;
  final DateTime lastDate;
  final DateTime initialDate;
  // 支持展示两个选中的日期
  final DateTime anotherInitDate;

  /// 默认展示的月份
  final DateTime displayMonth;
  final ClickCallback onChanged;

  /// 是否展示年月行
  final bool isScroll;

  DatePickerAdapted({
    Key key,
    this.firstDate,
    this.lastDate,
    this.onChanged,
    this.initialDate,
    this.displayMonth,
    this.anotherInitDate,
    this.isScroll = false,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => DatePickerState();
}

class DatePickerState extends State<DatePickerAdapted> {
  bool openYearSelector = false;
  DateTime firstDate;
  DateTime lastDate;
  // 当前选中的日期
  DateTime selectedDate;
  // 当前展示的月份
  DateTime displayMonth;
  DateTime anotherInitDate;

  String leftIconUrlDefault =
      'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/738c9d6dfcf96b50c97cf496200ed2d3/arrow_left.png';
  String upIconUrlDefault =
      'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/c19d474844a3eb2763490cf367ee27e6/up.png';
  String rightIconUrlDefault =
      'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/d91f6c2cff8daea2b28935a5da283379/arrow_right.png';
  String downIconUrlDefault =
      'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/a83256eef5d4e5dabc3404b900d4b851/down.png';

  @override
  initState() {
    super.initState();

    firstDate = widget.firstDate;
    lastDate = widget.lastDate;
    selectedDate = widget.initialDate;
    displayMonth = widget.displayMonth;
    // 有的话以displayMonth为准
    if (displayMonth == null) {
      if (selectedDate != null) {
        displayMonth = selectedDate;
      } else {
        displayMonth = widget.displayMonth ?? DateTime.now();
      }
    }
    anotherInitDate = widget.anotherInitDate;
  }

  // RooDatePickerAdaptedTheme getTheme() {
  //   RooThemeData rooThemeData = RooTheme.of(context);
  //   return rooThemeData?.datePickerAdaptedTheme;
  // }

  // 用于外层手动触发，因为anotherInitDate并不能实时传递进来
  updateAnotherInitDate(DateTime dt) {
    setState(() {
      anotherInitDate = dt;
    });
  }

  updateSelectedDate(DateTime dt) {
    setState(() {
      selectedDate = dt;
    });
  }

  updateDisplayMont(DateTime dt) {
    setState(() {
      displayMonth = dt;
    });
  }

  _buildImg(String type) {
    // RooDatePickerAdaptedTheme theme = getTheme();
    Map imgMap = {
      'left': leftIconUrlDefault,
      'right': rightIconUrlDefault,
      'up': upIconUrlDefault,
      'down': downIconUrlDefault,
    };
    return Image.network(
      imgMap[type],
      width: 16,
      height: 16,
    );
  }

  _buildBtn(int type) {
    return IconButton(
      enableFeedback: false,
      icon: type == 1 ? _buildImg('left') : _buildImg('right'),
      onPressed: () {
        // -1月
        if (type == 1) {
          DateTime preMonth = DateTime(
            displayMonth.year,
            displayMonth.month - 1,
          );

          if (preMonth.isBefore(DateTime(firstDate.year, firstDate.month))) {
            return;
          }
          displayMonth = preMonth;
          // +1月
        } else {
          DateTime nextMonth = DateTime(
            displayMonth.year,
            displayMonth.month + 1,
          );
          if (nextMonth.isAfter(DateTime(lastDate.year, lastDate.month))) {
            return;
          }
          displayMonth = nextMonth;
        }
        setState(() {});
      },
    );
  }

  _monthTap() {
    openYearSelector = !openYearSelector;
    setState(() {});
  }

  _buildYearMonth() {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: _monthTap,
      child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [
        ResponsiveSystem(
          pc: Text(
            '${displayMonth.year} 年 ${displayMonth.month} 月',
            style: const TextStyle(
              color: Color(0xFF333333),
              fontWeight: FontWeight.w400,
              fontSize: 14,
              height: 1.0,
            ),
          ),
          app: Text(
            '${displayMonth.year} 年 ${displayMonth.month} 月',
            style: const TextStyle(
              color: Color(0xFF222426),
              fontWeight: FontWeight.w500,
              fontSize: 16,
            ),
          ),
        ),
        ResponsiveSystem.bothAppPc(
          runPc: SizedBox.shrink(),
          runApp: openYearSelector ? _buildImg('up') : _buildImg('down'),
        )
      ]),
    );
  }

  _buildHeader() {
    // RooDatePickerAdaptedTheme theme = getTheme();
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFEEEEEE), width: 0.5),
        ),
      ),
      child: Row(
        mainAxisAlignment: ResponsiveSystem.bothAppPc(
            runPc: MainAxisAlignment.center,
            runApp: MainAxisAlignment.spaceBetween),
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          openYearSelector ? const SizedBox() : _buildBtn(1),
          _buildYearMonth(),
          openYearSelector ? const SizedBox() : _buildBtn(2),
        ],
      ),
    );
  }

  _buildWeekName(String name) {
    return Center(
      child: Text(name,
          style: TextStyle(
              color: ResponsiveSystem.bothAppPc(
                  runPc: Color(0xFF666666), runApp: Color(0xFF999999)),
              fontWeight: FontWeight.w400,
              fontSize: ResponsiveSystem.bothAppPc(runPc: 12.0, runApp: 14.0))),
    );
  }

  _buildWeeks() {
    // RooDatePickerAdaptedTheme theme = getTheme();
    List<Widget> labels = [];
    for (var ele in ['日', '一', '二', '三', '四', '五', '六']) {
      labels.add(_buildWeekName(ele));
    }
    return SizedBox(
      height: 40,
      child: GridView.custom(
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: _DayGridDelegate(40),
        childrenDelegate: SliverChildListDelegate(
          labels,
          addRepaintBoundaries: false,
        ),
      ),
    );
  }

  _isToday(DateTime inputDt) {
    if (inputDt == null) {
      return false;
    }
    DateTime now = DateTime.now();
    if (now.year == inputDt.year &&
        now.month == inputDt.month &&
        now.day == inputDt.day) {
      return true;
    }
    return false;
  }

  /// 占位日期
  _buildPlaceholderDay() {
    return Center(
      child: Container(
        margin: ResponsiveSystem.only4PC(EdgeInsets.symmetric(
          horizontal: 6,
          vertical: 4,
        )),
        alignment: Alignment.center,
      ),
    );
  }

  // 渲染当前天，isCurrentMonth是否为当前天的，不是则为灰色
  _buildDay(DateTime dateTime, bool isCurrentMonth) {
    // RooDatePickerAdaptedTheme theme = getTheme();
    String text = '';
    int day = dateTime?.day ?? 0;
    if (day != 0) {
      text = '$day';
    }
    Widget dayWidget;

    /// 去除时分秒带来的时间判断的影响 2022-12-06 12:15:03.544 格式化为 2022-12-06 00:00:00.000
    DateTime _lastDate = DateTime(lastDate.year, lastDate.month, lastDate.day);
    DateTime _firstDate =
        DateTime(firstDate.year, firstDate.month, firstDate.day);

    bool dateIsAvailable =
        (dateTime.isBefore(_lastDate) && dateTime.isAfter(_firstDate)) ||
            dateTime.isAtSameMomentAs(_lastDate) ||
            dateTime.isAtSameMomentAs(_firstDate);

    // 总共有3种颜色：超出日期｜非当前月 color1，周末color2，工作日color3
    Color color;
    if (isCurrentMonth && dateIsAvailable) {
      // 当前月周六周日color2
      if (dateTime.weekday == 6 || dateTime.weekday == 7) {
        color = const Color(0xFF999999);
      } else {
        // 当前月周一到周五color3
        color = const Color(0xFF222222);
      }
    } else {
      // 非当前月 ｜ 无效日期color1
      color = ResponsiveSystem.bothAppPc(
          runPc: Color(0xFFCCCCCC), runApp: Color(0xFFEEEEEE));
    }
    BoxDecoration boxDecoration;
    // 设置border
    if (_isToday(dateTime)) {
      boxDecoration = BoxDecoration(
        border: Border.all(width: 1, color: const Color(0xFFFFCC33)),
        borderRadius: BorderRadius.circular(2),
      );
      ;
    }
    // DateTime mergeDt = selectedDate ?? widget.initialDate;
    // 选中日期有背景色
    // 如果传进来的initDate为null，不展示已选中的颜色
    if (selectedDate != null) {
      if (dateTime.year == selectedDate.year &&
          dateTime.month == selectedDate.month &&
          dateTime.day == selectedDate.day) {
        color = const Color(0xFF222222);
        // bgColor = theme.selectedBgColor;
        boxDecoration = BoxDecoration(
          color: Color(0xFFFFCC33),
          borderRadius: BorderRadius.circular(2),
        );
        ;
      }
    }

    if (anotherInitDate != null) {
      if (dateTime.year == anotherInitDate.year &&
          dateTime.month == anotherInitDate.month &&
          dateTime.day == anotherInitDate.day) {
        color = const Color(0xFF222222);
        // bgColor = theme.selectedBgColor;
        boxDecoration = BoxDecoration(
          color: Color(0xFFFFCC33),
          borderRadius: BorderRadius.circular(2),
        );
      }
    }

    // BoxDecoration boxDecoration = BoxDecoration(
    //   color: bgColor,
    //   borderRadius: BorderRadius.circular(2),
    // );

    dayWidget = Container(
      margin: ResponsiveSystem.only4PC(EdgeInsets.symmetric(
        horizontal: 6,
        vertical: 4,
      )),
      alignment: Alignment.center,
      decoration: boxDecoration,
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
      ),
    );

    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        if (dateIsAvailable == false) {
          return;
        }
        setState(() {
          selectedDate = dateTime;
        });

        if (widget.onChanged != null) widget.onChanged(dateTime);
      },
      child: Center(
        child: dayWidget,
      ),
    );
  }

  _buildDays() {
    // RooDatePickerAdaptedTheme theme = getTheme();
    DateTime nextMonth = DateTime(displayMonth.year, displayMonth.month + 1, 1);
    DateTime lastDay = DateTime.fromMillisecondsSinceEpoch(
        nextMonth.millisecondsSinceEpoch - 1);
    DateTime firstDay = DateTime(displayMonth.year, displayMonth.month, 1);

    List<Widget> itemList = [];
    // 这个月的第一天是周几
    int offset = firstDay.weekday == 7 ? 0 : firstDay.weekday;

    // 这个月有多少天
    int daysInMonth = lastDay.day;

    int year = lastDay.year;
    int month = lastDay.month;

    int day = -offset;
    while (day < daysInMonth) {
      day++;
      DateTime dayToBuild = DateTime(year, month, day);
      if (day < 1) {
        /// 滚动模式不展示其它月份
        if (widget.isScroll) {
          itemList.add(_buildPlaceholderDay());
        } else {
          itemList.add(_buildDay(dayToBuild, false));
        }
      } else {
        itemList.add(_buildDay(dayToBuild, true));
      }
    }
    // 非滚动展示下月日期
    if (!widget.isScroll) {
      // 最后一天是星期几，补齐当月后的日期（灰色）
      int lastWeekday = lastDay.weekday == 7 ? 0 : lastDay.weekday;
      // 当月日期小于6行，补齐最后几行，统一样式
      int lastMonthDay = (6 - (itemList.length / 7).ceil()) * 7;
      for (int i = 1; i <= 6 - lastWeekday + lastMonthDay; i++) {
        DateTime dayToBuild = DateTime(nextMonth.year, nextMonth.month, i);
        itemList.add(_buildDay(dayToBuild, false));
      }
    }

    return Container(
      height: 240,
      child: GridView.builder(
        physics: const NeverScrollableScrollPhysics(),
        itemCount: itemList.length,
        gridDelegate: _DayGridDelegate(40),
        itemBuilder: (context, index) {
          return itemList[index];
        },
      ),
    );
  }

  _buildContent() {
    return Container(
      padding: const EdgeInsets.all(4),
      child: Column(
        // shrinkWrap: ,
        children: [
          if (widget.isScroll)
            Text(
              '${displayMonth.year} 年 ${displayMonth.month} 月',
              style: const TextStyle(
                color: Color(0xFF222426),
                fontWeight: FontWeight.w500,
                fontSize: 16,
              ),
            ),
          _buildWeeks(),
          _buildDays(),
        ],
      ),
    );
  }

  getYearTextColor(bool selected, bool isDisable) {
    // RooDatePickerAdaptedTheme theme = getTheme();
    if (isDisable) {
      return const Color(0xFF999999);
    } else {
      if (selected) {
        return const Color(0xFF222222);
      } else {
        return const Color(0xFF222222);
      }
    }
  }

  _buildYear(int index) {
    // RooDatePickerAdaptedTheme theme = getTheme();
    int year = displayMonth.year - 8 + index;
    bool selected = year == displayMonth.year;
    bool isDisable = (year < firstDate.year || year > lastDate.year);
    return GestureDetector(
      onTap: () {
        if (isDisable) {
          return;
        }
        displayMonth = DateTime(year, displayMonth.month, displayMonth.day);
        openYearSelector = false;
        setState(() {});
      },
      child: Center(
        child: Container(
          width: 70,
          height: 40,
          decoration: BoxDecoration(
              color: selected ? Color(0xFFFFCC33) : Colors.transparent,
              borderRadius: BorderRadius.circular(2)),
          alignment: Alignment.center,
          child: Text(
            '$year',
            style: TextStyle(
              color: getYearTextColor(selected, isDisable),
              fontSize: 14,
            ),
          ),
        ),
      ),
    );
  }

  _buildYearSelector() {
    // RooDatePickerAdaptedTheme theme = getTheme();

    return openYearSelector
        ? Container(
            margin: EdgeInsets.only(top: 50),
            height: 300,
            color: Colors.white,
            child: GridView.builder(
              itemCount: 12,
              gridDelegate: _YearPickerGridDelegate(),
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemBuilder: (context, index) {
                return _buildYear(index);
              },
            ),
          )
        : const SizedBox();
  }

  @override
  Widget build(BuildContext context) {
    // RooDatePickerAdaptedTheme theme = getTheme();
    return SizedBox(
      height: 400,
      child: Stack(
        children: [
          Container(
            color: const Color(0xFFFFFFFF),
            child: Column(
              children: [
                if (!widget.isScroll) _buildHeader(),
                _buildContent(),
              ],
            ),
          ),
          // 年选择器
          if (!widget.isScroll) _buildYearSelector(),
        ],
      ),
    );
  }
}

class _DayGridDelegate extends SliverGridDelegate {
  final double tileHeight;
  _DayGridDelegate(this.tileHeight);
  @override
  SliverGridLayout getLayout(SliverConstraints constraints) {
    const int columnCount = DateTime.daysPerWeek;
    double tileWidth = constraints.crossAxisExtent / columnCount;
    // double tileHeight = constraints.viewportMainAxisExtent / rowCount;
    double tileHeight = this.tileHeight;
    return SliverGridRegularTileLayout(
      childCrossAxisExtent: tileWidth,
      childMainAxisExtent: tileHeight,
      crossAxisCount: columnCount,
      crossAxisStride: tileWidth,
      mainAxisStride: tileHeight,
      reverseCrossAxis: false,
    );
  }

  @override
  bool shouldRelayout(_DayGridDelegate oldDelegate) => false;
}

class _YearPickerGridDelegate extends SliverGridDelegate {
  @override
  SliverGridLayout getLayout(SliverConstraints constraints) {
    const int _yearPickerColumnCount = 4;
    const int _yearPickerRowCount = 3;
    const double _yearPickerRowSpacing = 8.0;
    final double tileWidth = (constraints.crossAxisExtent -
            (_yearPickerColumnCount - 1) * _yearPickerRowSpacing) /
        _yearPickerColumnCount;
    final double tileHeight = (constraints.viewportMainAxisExtent -
            (_yearPickerRowCount - 1) * _yearPickerRowSpacing) /
        _yearPickerRowCount;
    return SliverGridRegularTileLayout(
      childCrossAxisExtent: tileWidth,
      childMainAxisExtent: tileHeight,
      crossAxisCount: _yearPickerColumnCount,
      crossAxisStride: tileWidth + _yearPickerRowSpacing,
      mainAxisStride: tileHeight + _yearPickerRowSpacing,
      reverseCrossAxis: false,
    );
  }

  @override
  bool shouldRelayout(_YearPickerGridDelegate oldDelegate) => false;
}
