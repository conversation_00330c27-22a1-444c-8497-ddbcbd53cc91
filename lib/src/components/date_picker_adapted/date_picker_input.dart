import 'package:flutter/material.dart';
import 'package:roo_flutter/roo_flutter.dart';

class RooDatePickerInput extends StatefulWidget {
  /// 日期默认值, 不传入默认今天
  final DateTime initialDate;
  final ValueChanged<String> onChange;
  final GestureTapCallback onTap;
  final TextEditingController controller;
  final String hintText;

  RooDatePickerInput(
      {this.initialDate,
      this.onChange,
      this.onTap,
      this.controller,
      this.hintText});

  @override
  State<StatefulWidget> createState() => _State();
}

class _State extends State<RooDatePickerInput> {
  TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
  }

  @override
  Widget build(BuildContext context) {
    return RooInput(
      controller: _controller,
      readOnly: true,
      hintText: '${widget.hintText ?? '请选择时间'}',
      keyboardType: TextInputType.text,
      onTap: () {
        if (widget.onTap != null) widget.onTap();
      },
    );
  }
}
