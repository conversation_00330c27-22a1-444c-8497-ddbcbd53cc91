class RooDatePickerUtil {
  // ignore: non_constant_identifier_names
  static final DateTime startDate = DateTime(1970);
  // ignore: non_constant_identifier_names
  static final DateTime endDate = DateTime(2970);

  /// 对日期进行格式化，前期只做简单处理
  static formatDate(DateTime dt) {
    if (dt != null) {
      return '${dt.year}-${dt.month}-${dt.day}';
    }
    return '';
  }

  /// 传入月份的下一个月
  static nextMonth(DateTime dt) {
    return DateTime(dt.year, dt.month + 1, dt.day);
  }
}
