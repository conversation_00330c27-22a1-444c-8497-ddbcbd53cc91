import 'package:flutter/material.dart';
import 'package:roo_flutter/basic_components/date_picker_adapted/date_picker_adapted.dart';

/// PC端只有一个时间
class PcDatePicker extends StatefulWidget {
  /// 日期默认值, 不传入默认今天
  final DateTime initialDate;

  /// 起始日期限制，该日期之前不可选择
  final DateTime firstDate;

  /// 截止日期限制，该日期之后不可选择
  final DateTime lastDate;

  /// 默认展示的月份
  final DateTime displayMonth;

  // 支持展示两个选中的日期
  final DateTime anotherInitDate;

  /// 选择日期完成时的回调
  final ValueChanged<DateTime> onChanged;

  PcDatePicker({
    Key key,
    @required this.initialDate,
    @required this.firstDate,
    @required this.lastDate,
    @required this.onChanged,
    this.displayMonth,
    this.anotherInitDate,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => PcDatePickerState();
}

class PcDatePickerState extends State<PcDatePicker> {
  DateTime anotherInitDate;

  @override
  void initState() {
    super.initState();
    anotherInitDate = widget.anotherInitDate;
  }

  // 用于外层手动触发，因为anotherInitDate并不能实时传递进来
  updateAnotherInitDate(DateTime dt) {
    setState(() {
      anotherInitDate = dt;
    });
  }

  @override
  Widget build(BuildContext context) {
    DatePickerAdapted picker = DatePickerAdapted(
      initialDate: widget.initialDate,
      firstDate: widget.firstDate,
      lastDate: widget.lastDate,
      displayMonth: widget.displayMonth,
      anotherInitDate: anotherInitDate,
      onChanged: (DateTime dt) {
        if (widget.onChanged != null) widget.onChanged(dt);
      },
    );

    return Container(
      width: 350,
      child: picker,
    );
  }
}
