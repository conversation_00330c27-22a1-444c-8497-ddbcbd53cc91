import 'package:flutter/material.dart';
import 'package:roo_flutter/basic_components/date_picker_adapted/date_picker_input.dart';
import 'package:roo_flutter/basic_components/date_picker_adapted/date_picker_utils.dart';
import 'package:roo_flutter/basic_components/date_picker_adapted/pc_date_picker.dart';
import 'package:roo_flutter/basic_components/date_picker_adapted/roo_date_picker_adapted.dart';
import 'package:roo_flutter/tools/follower.dart';

class PcDatePickerFollower extends StatefulWidget {
  /// 日期默认值, 不传入默认今天
  final DateTime initialDate;

  /// 起始日期限制，该日期之前不可选择
  final DateTime firstDate;

  /// 截止日期限制，该日期之后不可选择
  final DateTime lastDate;

  /// 选择日期完成时的回调
  final ValueChanged<DateTime> onChanged;

  /// 日期选择控制器
  final DatePickerController controller;

  // 输入框中显示文本
  final String hintText;

  /// 自定义展示
  final Widget customDisplayWidget;

  PcDatePickerFollower(
      {@required this.initialDate,
      @required this.firstDate,
      @required this.lastDate,
      @required this.onChanged,
      this.controller,
      this.hintText,
      this.customDisplayWidget});

  @override
  State<StatefulWidget> createState() => _PcDatePickerFollowerState();
}

class _PcDatePickerFollowerState extends State<PcDatePickerFollower> {
  EolFollowerWidget eolFollower = EolFollowerWidget();
  final TextEditingController _controller = TextEditingController();

  /// 时间选择控制器
  DatePickerController _dateController;

  @override
  initState() {
    super.initState();

    /// 初始化dateController控制器
    if (widget.controller != null) {
      _dateController = widget.controller;
      _dateController.date = _dateController.date ?? widget.initialDate;
      _dateController.dateEarly = _dateController.dateEarly ?? widget.firstDate;
      _dateController.dateLate = _dateController.dateLate ?? widget.lastDate;
    } else {
      _dateController = DatePickerController(
          date: widget.initialDate,
          dateEarly: widget.firstDate,
          dateLate: widget.lastDate);
    }

    _dateController.addListener(() {
      setState(() {
        _controller.text = RooDatePickerUtil.formatDate(_dateController.date);
      });
    });

    _controller.text = RooDatePickerUtil.formatDate(_dateController.date);
  }

  showPcDatePicker() {
    eolFollower.follower(context,
        width: 300,
        height: 282,
        child: Container(
          child: PcDatePicker(
            initialDate: _dateController.date,
            firstDate: _dateController.dateEarly ?? RooDatePickerUtil.startDate,
            lastDate: _dateController.dateLate ?? RooDatePickerUtil.endDate,
            onChanged: (DateTime dt) {
              _dateController.date = dt;
              eolFollower.cancel();
              if (widget.onChanged != null) widget.onChanged(dt);
              FocusScope.of(context).requestFocus(FocusNode());
            },
          ),
        ));
  }

  @override
  Widget build(BuildContext context) {
    return eolFollower.target(
      context,
      child: widget.customDisplayWidget != null
          ? GestureDetector(
              child: widget.customDisplayWidget,
              onTap: () {
                eolFollower.cancel();
                showPcDatePicker();
              },
            )
          : RooDatePickerInput(
              initialDate: _dateController.date,
              controller: _controller,
              hintText: widget.hintText,
              onTap: () {
                eolFollower.cancel();
                showPcDatePicker();
              },
            ),
    );
  }
}
