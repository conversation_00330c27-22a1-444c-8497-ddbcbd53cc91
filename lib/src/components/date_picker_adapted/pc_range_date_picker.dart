import 'package:flutter/material.dart';
import 'package:roo_flutter/basic_components/date_picker_adapted/date_picker_adapted.dart';

/// PC端有选择开始结束时间功能
class PcRangeDatePicker extends StatefulWidget {
  /// 开始和结束的选中日期限制
  final List<DateTime> rangeInitialDate;

  /// 开始和结束的起始日期限制
  final List<DateTime> rangeFirstLastDate;

  /// 选择日期完成时的回调
  final ValueChanged<List<DateTime>> onRangeChange;

  PcRangeDatePicker({
    this.rangeInitialDate,
    this.rangeFirstLastDate,
    @required this.onRangeChange,
  });

  @override
  State<StatefulWidget> createState() => PcRangeDatePickerState();
}

class PcRangeDatePickerState extends State<PcRangeDatePicker> {
  List<DateTime> selectedRangeFirstLastDate = [];
  int selectedPickerIndex = 1;

  @override
  void initState() {
    super.initState();
    selectedRangeFirstLastDate = widget.rangeInitialDate ?? [];
  }

  // _addRangeDateTime(DateTime dt, int pickerIndex) {
  //   if (selectedRangeFirstLastDate.length == 2) {
  //     selectedRangeFirstLastDate.clear();
  //   }
  //   selectedRangeFirstLastDate.add(dt);

  //   if (selectedRangeFirstLastDate.length == 2) {
  //     if (widget.onRangeChange != null)
  //       widget.onRangeChange(selectedRangeFirstLastDate);
  //   }
  //   this.setState(() {});
  // }

  /// 某一天的前一天
  // _getPreDay(DateTime dt) {
  //   return DateTime(dt.year, dt.month, dt.day - 1);
  // }

  @override
  Widget build(BuildContext context) {
    DateTime firstDate1;
    DateTime lastDate1;
    DateTime firstDate2;
    DateTime lastDate2;
    if (selectedRangeFirstLastDate != null &&
        selectedRangeFirstLastDate.length == 2) {
      firstDate1 = selectedRangeFirstLastDate[0];
      lastDate2 = selectedRangeFirstLastDate[1];
    }

    if (selectedRangeFirstLastDate != null &&
        selectedRangeFirstLastDate.length > 0) {
      if (selectedRangeFirstLastDate.length == 1) {
        DateTime d0 = selectedRangeFirstLastDate[0];
        // 设置可用/不可用的日期
        // firstDate1 = _getPreDay(d0);
        firstDate1 = d0;
        lastDate1 = null;
        firstDate2 = null;
        lastDate2 = null;
      } else if (selectedRangeFirstLastDate.length == 2) {
        DateTime d0 = selectedRangeFirstLastDate[0];
        DateTime d1 = selectedRangeFirstLastDate[1];
        // 同年同月，则代表它们的选中项在同一个组件里，否则在另外一个组件里
        if (d0.year == d1.year && d0.month == d1.month) {
        } else {
          // 交换顺序
          if (d0.isAfter(d1)) {
          } else {}
        }
        // firstDate1 = _getPreDay(d0);
        firstDate1 = d0;
        lastDate1 = d1;
        firstDate2 = d0;
        lastDate2 = d1;
      }
    }

    DatePickerAdapted picker1 = DatePickerAdapted(
      firstDate: firstDate1,
      lastDate: lastDate1,
      onChanged: (DateTime dt) {
        // this._addRangeDateTime(dt, pickerIndex);
      },
    );

    DatePickerAdapted picker2 = DatePickerAdapted(
      firstDate: firstDate2 ?? DateTime(1970),
      lastDate: lastDate2 ?? DateTime(2970),
      onChanged: (DateTime dt) {
        // this._addRangeDateTime(dt);
      },
    );

    return Row(
      children: [
        Container(
          width: 350,
          child: picker1,
        ),
        Container(
          width: 350,
          child: picker2,
        )
      ],
    );
  }
}
