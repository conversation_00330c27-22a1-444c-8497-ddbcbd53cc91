import 'package:flutter/material.dart';
import 'package:roo_flutter/basic_components/date_picker_adapted/date_picker_adapted.dart';
import 'package:roo_flutter/basic_components/date_picker_adapted/date_picker_input.dart';
import 'package:roo_flutter/basic_components/date_picker_adapted/date_picker_utils.dart';
import 'package:roo_flutter/basic_components/date_picker_adapted/roo_date_picker_adapted.dart';
import 'package:roo_flutter/basic_components/date_picker_adapted/roo_date_picker_adapted_theme.dart';
import 'package:roo_flutter/roo_theme/theme.dart';
import 'package:roo_flutter/roo_theme/theme_data.dart';
import 'package:roo_flutter/tools/follower.dart';

class PcRangeDatePickerFollower extends StatefulWidget {
  /// 开始和结束的选中日期限制
  final List<DateTime> rangeInitialDate;

  /// 开始和结束的起始日期限制
  final List<DateTime> rangeFirstLastDate;

  /// 选择日期完成时的回调
  final ValueChanged<List<DateTime>> onRangeChange;

  /// 日期选择控制器
  final RangeDatePickerController controller;

  /// 自定义展示
  final Widget customDisplayWidget;

  PcRangeDatePickerFollower({
    Key key,
    this.rangeFirstLastDate,
    this.rangeInitialDate,
    this.onRangeChange,
    this.controller,
    this.customDisplayWidget,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() => PcRangeDatePickerFollowerState();
}

class PcRangeDatePickerFollowerState extends State<PcRangeDatePickerFollower> {
  List<DateTime> selectedDate = [];
  EolFollowerWidget eolFollower = EolFollowerWidget();
  final TextEditingController _controller = TextEditingController();

  /// 时间选择控制器
  RangeDatePickerController _rangeController;
  // DateTime selectedDt;

  DateTime firstDate;
  DateTime lastDate;

  DateTime initDate1;
  DateTime initDate2;

  final GlobalKey<DatePickerState> _p1GlobalKey = GlobalKey<DatePickerState>();
  final GlobalKey<DatePickerState> _p2GlobalKey = GlobalKey<DatePickerState>();

  @override
  initState() {
    super.initState();

    if (widget.controller != null) {
      _rangeController = widget.controller;
      _rangeController.rangeDate =
          _rangeController.rangeDate ?? widget.rangeInitialDate;
    } else {
      _rangeController =
          RangeDatePickerController(rangeDate: widget.rangeInitialDate);
    }

    _rangeController.addListener(() {
      setState(() {
        updateDate(_rangeController.rangeDate);
      });
    });

    /// 初始化选中日期
    updateDate(_rangeController.rangeDate);

    /// 初始化起始日期范围
    final List<DateTime> rangeFirstLastDate = widget.rangeFirstLastDate;

    if (rangeFirstLastDate != null && rangeFirstLastDate.length == 2) {
      firstDate = rangeFirstLastDate[0];
      lastDate = rangeFirstLastDate[1];
    }
  }

  updateDate(List<DateTime> rangeDate) {
    if (rangeDate != null && rangeDate.length == 2) {
      initDate1 = rangeDate[0];
      initDate2 = rangeDate[1];
    }

    // 设置input的value
    if (initDate1 != null && initDate2 != null) {
      String newText = getRangeDateText(initDate1, initDate2);
      _controller.text = newText;
    }
  }

  getRangeDateText(DateTime dt1, DateTime dt2) {
    if (dt1 != null && dt2 != null) {
      return '${RooDatePickerUtil.formatDate(dt1)}  -  ${RooDatePickerUtil.formatDate(dt2)}';
    }
    return '';
  }

  onRangeChange(DateTime dt) {
    selectedDate.add(dt);

    // 排序
    selectedDate.sort();

    if (selectedDate.length == 2) {
      eolFollower.cancel();
      _controller.text =
          '${getRangeDateText(selectedDate[0], selectedDate[1])}';
      // 重新初始化
      initDate1 = selectedDate[0];
      initDate2 = selectedDate[1];
      FocusScope.of(context).requestFocus(FocusNode());
      if (widget.onRangeChange != null) widget.onRangeChange(selectedDate);
      _rangeController.rangeDate = selectedDate;
      // 清空，下次再加
      selectedDate = [];
    } else if (selectedDate.length == 1) {
      initDate2 = null;
    }

    // 只选中一个的情况
    if (selectedDate.length == 1) {
      DateTime firstDt = initDate1 ?? DateTime.now();
      if (dt.month != firstDt.month) {
        // 展示的月份切成当前月，并选中日期
        _p1GlobalKey?.currentState?.updateDisplayMont(dt);
        _p1GlobalKey?.currentState?.updateSelectedDate(dt);
        // 展示月份切换成下个月
        _p2GlobalKey?.currentState
            ?.updateDisplayMont(RooDatePickerUtil.nextMonth(dt));
      }
      // 把第二个日历数据清空
      _p2GlobalKey?.currentState?.updateSelectedDate(null);
    }
    // 手动更新组件里的数据，因为父无法把数据传递下去
    _p1GlobalKey?.currentState?.updateAnotherInitDate(initDate2);
  }

  /// 处理一个月份中有两个选中的日期
  dealAnotherInitDate() {
    if (initDate1 != null && initDate2 != null) {
      if (initDate1.year == initDate2.year &&
          initDate1.month == initDate2.month) {
        return initDate2;
      }
    }
    return null;
  }

  /// 处理默认展示的月份
  /// 当没有初始日期时，index=1时展示当前月，index=2时展示下个月
  /// 当有初始日期时，1和2为相同月时，2展示下个月
  dealDisplayMonth(int index) {
    if (index == 1) {
      if (initDate1 == null) {
        return DateTime.now();
      }
      return initDate1;
    } else {
      if (initDate2 == null) {
        return RooDatePickerUtil.nextMonth(DateTime.now());
      } else if (initDate1 != null && initDate2 != null) {
        if (initDate1.year == initDate2.year &&
            initDate1.month == initDate2.month) {
          return RooDatePickerUtil.nextMonth(initDate1);
        }
      }
      return initDate2;
    }
  }

  RooDatePickerAdaptedTheme getTheme() {
    RooThemeData rooThemeData = RooTheme.of(context);
    return rooThemeData?.datePickerAdaptedTheme;
  }

  // 暴露给GlobalKey的方法，用以解决Overlay下组件不更新的问题
  updateRangeFirstLastDate(List<DateTime> rangeFirstLastDate) {
    if (rangeFirstLastDate != null && rangeFirstLastDate.length == 2) {
      firstDate = rangeFirstLastDate[0];
      lastDate = rangeFirstLastDate[1];
    }
    _buildPickers();
  }

  _buildPickers() {
    DateTime anotherInitDate = dealAnotherInitDate();
    RooDatePickerAdaptedTheme theme = getTheme();
    return Row(
      children: [
        Container(
          width: theme.pickerWidth,
          child: DatePickerAdapted(
            key: _p1GlobalKey,
            initialDate: initDate1,
            firstDate: firstDate ?? RooDatePickerUtil.startDate,
            lastDate: lastDate ?? RooDatePickerUtil.endDate,
            displayMonth: dealDisplayMonth(1),
            // 如果有，会同时选中两个
            anotherInitDate: anotherInitDate,
            onChanged: (DateTime dt) {
              onRangeChange(dt);
            },
          ),
        ),
        Container(
          width: theme.pickerWidth,
          child: DatePickerAdapted(
            key: _p2GlobalKey,
            initialDate: initDate2,
            firstDate: firstDate ?? RooDatePickerUtil.startDate,
            lastDate: lastDate ?? RooDatePickerUtil.endDate,
            displayMonth: dealDisplayMonth(2),
            // 如果有，会同时选中两个
            anotherInitDate: anotherInitDate,
            onChanged: (DateTime dt) {
              onRangeChange(dt);
            },
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return eolFollower.target(
      context,
      child: widget.customDisplayWidget != null
          ? GestureDetector(
              child: widget.customDisplayWidget,
              onTap: () {
                eolFollower.cancel();
                eolFollower.follower(
                  context,
                  width: 600,
                  height: 282,
                  child: _buildPickers(),
                );
              },
            )
          : RooDatePickerInput(
              hintText: '开始时间  -  结束时间',
              controller: _controller,
              onTap: () {
                eolFollower.cancel();
                eolFollower.follower(
                  context,
                  width: 600,
                  height: 282,
                  child: _buildPickers(),
                );
              },
            ),
    );
  }
}
