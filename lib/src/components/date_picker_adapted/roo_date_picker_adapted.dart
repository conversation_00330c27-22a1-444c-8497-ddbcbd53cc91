import 'package:flutter/material.dart';
// import 'package:roo_flutter/basic_components/date_picker_adapted/pc_date_picker_follower.dart';
// import 'package:roo_flutter/basic_components/date_picker_adapted/pc_range_date_picker_follower.dart';
import 'package:roo_flutter/tools/response_system.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/date_picker_adapted/app_date_picker.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/date_picker_adapted/app_range_date_picker.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/date_picker_adapted/pc_date_picker_follower.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/date_picker_adapted/pc_range_date_picker_follower.dart';

/// 日期选择[new]
///
/// 已进行双端适配的日期选择组件
///
/// {@tool sample}
/// 单一日期 - 带前后限制
///
/// ```dart
///   Card(
///     child: RooDatePickerAdapted(
///       hintText: '单一日期 - 带前后限制',
///       firstDate: DateTime(today.year, today.month - 1, 10),
///       // initialDate: today,
///       lastDate: DateTime(today.year, today.month + 1, 10),
///       onChanged: onChange,
///     ),
///   ),
/// ```
///{@end-tool}
class RooDatePickerAdapteds extends StatefulWidget {
  /// 日期默认值, 不传默认今天
  final DateTime initialDate;

  /// 日期控制器
  final DatePickerControllers controller;

  /// 起始日期限制，该日期之前不可选择，不传无时间限制
  final DateTime firstDate;

  /// 截止日期限制，该日期之后不可选择，不传无时间限制
  final DateTime lastDate;

  /// 选择日期完成时的回调
  final ValueChanged<DateTime> onChanged;

  /// 是否为区间时间，如果设置为true，则以上属性都不会生效，需要设置如下的range属性，不传默认 false
  final bool isRange;

  /// 开始和结束的选中日期限制
  final List<DateTime> rangeInitialDate;

  /// 开始结束日期控制器
  final RangeDatePickerControllers rangeController;

  /// 开始和结束的起始日期限制
  final List<DateTime> rangeFirstLastDate;

  /// 选择日期完成时的回调
  final ValueChanged<List<DateTime>> onRangeChange;

  /// 选择开始日期完成时的回调
  final ValueChanged<DateTime> onFirseRangeChange;

  // 输入框中显示文本(只对isRange=false时生效)
  final String hintText;

  /// app合并开始和结束选择日历，默认分开
  final bool isCalendar;

  /// 是否滚动样式
  final bool isScroll;

  /// 自定义展示
  final Widget customDisplayWidget;

  RooDatePickerAdapteds({
    this.initialDate,
    this.firstDate,
    this.lastDate,
    this.onChanged,
    this.isRange,
    this.rangeFirstLastDate,
    this.rangeInitialDate,
    this.onRangeChange,
    this.hintText,
    this.isCalendar = false,
    this.isScroll = true,
    this.controller,
    this.rangeController,
    this.customDisplayWidget,
    this.onFirseRangeChange,
  });

  @override
  State<StatefulWidget> createState() => RooDatePickerAdaptedsState();
}

class RooDatePickerAdaptedsState extends State<RooDatePickerAdapteds> {
  /// 日期默认值, 不传默认今天
  DateTime initialDate;

  /// 起始日期限制，该日期之前不可选择，不传无时间限制
  DateTime firstDate;

  /// 截止日期限制，该日期之后不可选择，不传无时间限制
  DateTime lastDate;

  /// 开始和结束的选中日期限制
  List<DateTime> rangeInitialDate;

  /// 开始和结束的起始日期限制
  List<DateTime> rangeFirstLastDate;

  GlobalKey<PcRangeDatePickerFollowerState> pcGlobalKey =
      GlobalKey<PcRangeDatePickerFollowerState>();

  @override
  initState() {
    super.initState();
    // initialDate = widget.initialDate ?? DateTime.now();
    // firstDate = widget.firstDate ?? START_DATE;
    // lastDate = widget.lastDate ?? END_DATE;
    // rangeInitialDate = widget.rangeInitialDate ?? [];
    // rangeFirstLastDate = widget.rangeFirstLastDate ?? [START_DATE, END_DATE];

    initialDate = widget.initialDate;
    firstDate = widget.firstDate;
    lastDate = widget.lastDate;
    rangeInitialDate = widget.rangeInitialDate;
    rangeFirstLastDate = widget.rangeFirstLastDate;

    if (initialDate != null &&
        firstDate != null &&
        initialDate.isBefore(firstDate)) {
      throw Exception('initialDate=$initialDate 必须在 firstDate=$firstDate 之后');
    }

    if (initialDate != null &&
        lastDate != null &&
        initialDate.isAfter(lastDate)) {
      throw Exception('initialDate=$initialDate 必须在 lastDate=$lastDate 之前');
    }

    if (firstDate != null && lastDate != null && firstDate.isAfter(lastDate)) {
      throw Exception('firstDate=$firstDate 必须在 lastDate=$lastDate 之前');
    }
  }

  List<DateTime> selectedRangeInitialDate = [];

  @override
  void didUpdateWidget(covariant RooDatePickerAdapteds oldWidget) {
    super.didUpdateWidget(oldWidget);

    /// rangeFirstLastDate支持动态更新
    /// 由于pc组件渲染在Overlay中，因此通过GlobalKey方式更新rangeDate
    pcGlobalKey?.currentState
        ?.updateRangeFirstLastDate(widget.rangeFirstLastDate);

    /// app使用props方式更新rangeDate
    rangeFirstLastDate = widget.rangeFirstLastDate;
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveSystem(
      app: widget.isRange == true
          ? AppRangeDatePicker(
              controller: widget.rangeController,
              rangeFirstLastDate: rangeFirstLastDate,
              rangeInitialDate: rangeInitialDate,
              onRangeChange: (List<DateTime> dtList) {
                if (widget.onRangeChange != null) widget.onRangeChange(dtList);
              },
              onFirseRangeChange: (DateTime dts) {
                if (widget.onFirseRangeChange != null) {
                  widget.onFirseRangeChange(dts);
                }
              },
              isCalendar: widget.isCalendar,
              isScroll: widget.isScroll,
              customDisplayWidget: widget.customDisplayWidget,
            )
          : AppDatePicker(
              initialDate: initialDate,
              // controller: widget.controller,
              firstDate: firstDate,
              lastDate: lastDate,
              hintText: widget.hintText,
              onChanged: (dt) {
                if (widget.onChanged != null) widget.onChanged(dt);
              },
              customDisplayWidget: widget.customDisplayWidget,
            ),
      pc: widget.isRange == true
          ? PcRangeDatePickerFollower(
              key: pcGlobalKey,
              // controller: widget.rangeController,
              rangeFirstLastDate: rangeFirstLastDate,
              rangeInitialDate: rangeInitialDate,
              onRangeChange: (List<DateTime> dtList) {
                if (widget.onRangeChange != null) widget.onRangeChange(dtList);
              },
              customDisplayWidget: widget.customDisplayWidget,
            )
          : PcDatePickerFollower(
              initialDate: initialDate,
              // controller: widget.controller,
              firstDate: firstDate,
              lastDate: lastDate,
              hintText: widget.hintText,
              onChanged: (dt) {
                if (widget.onChanged != null) widget.onChanged(dt);
              },
              customDisplayWidget: widget.customDisplayWidget,
            ),
    );
  }
}

class RangeDatePickerControllers extends ChangeNotifier {
  List<DateTime> _rangeDate = [];

  RangeDatePickerControllers({List<DateTime> rangeDate})
      : this._rangeDate = rangeDate;

  List<DateTime> get rangeDate => _rangeDate;

  set rangeDate(List<DateTime> date) {
    if (_rangeDate == date) {
      return;
    }
    _rangeDate = date;
    notifyListeners();
  }
}

class DatePickerControllers extends ChangeNotifier {
  DateTime _date;
  DateTime _dateEarly;
  DateTime _dateLate;

  DatePickerControllers({DateTime date, DateTime dateEarly, DateTime dateLate})
      : this._date = date,
        this._dateEarly = dateEarly,
        this._dateLate = dateLate;

  DateTime get date => _date;

  set date(DateTime date) {
    if (_date == date) {
      return;
    }
    _date = date;
    notifyListeners();
  }

  DateTime get dateEarly => _dateEarly;

  set dateEarly(DateTime dateEarly) {
    if (_dateEarly == dateEarly) {
      return;
    }
    _dateEarly = dateEarly;
    notifyListeners();
  }

  DateTime get dateLate => _dateLate;

  set dateLate(DateTime dateLate) {
    if (_dateLate == dateLate) {
      return;
    }
    _dateLate = dateLate;
    notifyListeners();
  }
}
