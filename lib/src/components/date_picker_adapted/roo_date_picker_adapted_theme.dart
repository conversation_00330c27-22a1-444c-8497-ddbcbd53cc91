import 'package:flutter/material.dart';
import 'package:waimai_e_fe_ffw_utils/platform_tool.dart';

class RooDatePickerAdaptedTheme {
  // 日期组件的高度
  final double pickerHeight;
  // 日期组件的宽度
  final double pickerWidth;
  // 每一天的高度
  final double tileHeight;
  // 日历头的高度
  final double headerHeight;

  // 渲染日期区域的高度
  final double daysHeight;

  // 左上右下的图标地址
  final String leftIconUrl;
  final String rightIconUrl;
  final String upIconUrl;
  final String downIconUrl;

  // 图标的大小
  final double iconWidth;
  final double iconHeight;

  // 日期中用到的字体大小
  final double fontSize;
  // 日期中用到的字体加粗
  final FontWeight fontWeight;

  // 选中日期的样式
  BoxDecoration selectedBoxDecoration;
  // 选中日期的文字颜色
  Color selectedTextColor;

  // 今天的样式
  BoxDecoration todayBoxDecoration;

  // 选中年份的颜色
  Color selectedYearColor;
  // 选中年份的高度
  final double yearHeight;
  // 选中年份的宽度
  final double yearWidth;

  RooDatePickerAdaptedTheme({
    this.pickerHeight,
    this.tileHeight,
    this.leftIconUrl,
    this.rightIconUrl,
    this.upIconUrl,
    this.downIconUrl,
    this.iconWidth,
    this.iconHeight,
    this.selectedBoxDecoration,
    this.fontSize,
    this.fontWeight,
    this.selectedTextColor,
    this.pickerWidth,
    this.headerHeight,
    this.todayBoxDecoration,
    this.daysHeight,
    this.selectedYearColor,
    this.yearHeight,
    this.yearWidth,
  });

  static const String leftIconUrlDefault =
      'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/738c9d6dfcf96b50c97cf496200ed2d3/arrow_left.png';
  static const String upIconUrlDefault =
      'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/c19d474844a3eb2763490cf367ee27e6/up.png';
  static const String rightIconUrlDefault =
      'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/d91f6c2cff8daea2b28935a5da283379/arrow_right.png';
  static const String downIconUrlDefault =
      'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/a83256eef5d4e5dabc3404b900d4b851/down.png';

  // 选中日期的样式 - PC
  static final BoxDecoration selectedBoxDecorationPC = BoxDecoration(
    color: Color(0xFF222222),
    borderRadius: BorderRadius.circular(2),
  );

  // 选中日期的样式 - App
  static final BoxDecoration selectedBoxDecorationApp = BoxDecoration(
    color: Color(0xFFFFCC33),
    borderRadius: BorderRadius.circular(2),
  );

  // 选中日期的文字颜色 - App
  static final Color selectedTextColorApp = const Color(0xFF222222);

  // 选中日期的文字颜色 - App
  static final Color selectedTextColorPC = const Color(0xFFFFFFFF);

  // 今天的样式 - PC
  static final BoxDecoration todayBoxDecorationPC = BoxDecoration(
    border: Border.all(width: 1, color: Color(0xFF222222)),
    borderRadius: BorderRadius.circular(2),
  );

  // 今天的样式 - App
  static final BoxDecoration todayBoxDecorationApp = BoxDecoration(
    border: Border.all(width: 1, color: const Color(0xFFFFCC33)),
    borderRadius: BorderRadius.circular(2),
  );

  factory RooDatePickerAdaptedTheme.rooDesign() => PlatformTools.isPC
      ? RooDatePickerAdaptedTheme(
          tileHeight: 30,
          headerHeight: 36,
          daysHeight: 208,
          pickerWidth: 280,
          pickerHeight: 290,
          leftIconUrl: leftIconUrlDefault,
          upIconUrl: upIconUrlDefault,
          rightIconUrl: rightIconUrlDefault,
          downIconUrl: downIconUrlDefault,
          iconWidth: 12,
          iconHeight: 12,
          fontSize: 12,
          fontWeight: FontWeight.w400,
          selectedBoxDecoration: selectedBoxDecorationPC,
          selectedTextColor: selectedTextColorPC,
          todayBoxDecoration: todayBoxDecorationPC,
          selectedYearColor: Color(0xFF222222),
          yearHeight: 24,
          yearWidth: 32,
        )
      : RooDatePickerAdaptedTheme(
          tileHeight: 40,
          headerHeight: 50,
          daysHeight: 240,
          pickerWidth: 350,
          pickerHeight: 400,
          leftIconUrl: leftIconUrlDefault,
          upIconUrl: upIconUrlDefault,
          rightIconUrl: rightIconUrlDefault,
          downIconUrl: downIconUrlDefault,
          iconWidth: 16,
          iconHeight: 16,
          fontSize: 14,
          fontWeight: FontWeight.w500,
          selectedBoxDecoration: selectedBoxDecorationApp,
          selectedTextColor: selectedTextColorApp,
          todayBoxDecoration: todayBoxDecorationApp,
          selectedYearColor: Color(0xFFFFCC33),
          yearHeight: 40,
          yearWidth: 70,
        );
}
