import 'dart:math';

import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/double_line_chart/modal/index.dart';

class DrawMethod {
  /// 计算最大值
  static num findMaxNum(List<num> arr) {
    num maxNum = 0;
    arr.asMap().forEach((int index, num item) {
      if (item != null) {
        if (item > maxNum) {
          maxNum = item;
        }
      }
    });

    return maxNum;
  }

  /// 计算Y轴间隔
  static num findYAxisInterval(List<num> arr) {
    num maxNum = findMaxNum(arr);
    double rawInterval = maxNum / 5;
    double interval;
    if (rawInterval == 0) {
      // 默认取个 100
      interval = 100;
    } else {
      double magnitude = pow(10, (log(rawInterval) / ln10).floor());
      double residual = rawInterval / magnitude;

      if (residual > 5) {
        interval = 10 * magnitude;
      } else if (residual > 2) {
        interval = 5 * magnitude;
      } else if (residual > 1) {
        interval = 2 * magnitude;
      } else {
        interval = magnitude;
      }
    }

    return interval;
  }

  static num findYAxisMaxNum(List<num> arr) {
    num maxNum = findMaxNum(arr);
    double interval = findYAxisInterval(arr);

    return (maxNum / interval).ceil() * interval;
  }

  /// 绘制X轴坐标点
  static void paintText(
      {Canvas canvas,
      String text,
      Offset offset,
      TextAlign textAlign = TextAlign.center,

      /// 是否需要垂直居中
      bool needVerticalCenter = false,
      bool needhorizontalCenter = false,

      /// 是否为最后一个坐标
      bool isLast = false,
      double minWidth = 0}) {
    TextSpan span = TextSpan(
      text: text,
      style: TextStyle(color: Color(0xFF999999), fontSize: 10),
    );
    TextPainter tp = TextPainter(
      textDirection: TextDirection.ltr,
      text: span,
      textAlign: textAlign,
    );
    Offset _offset = offset;

    tp.layout(minWidth: minWidth);
    if (needVerticalCenter == true) {
      _offset = Offset(offset.dx, offset.dy - (tp?.height ?? 0) / 2);
    }
    if (needhorizontalCenter) {
      _offset = Offset(offset.dx - (tp.width / 2), offset.dy);
    }
    if (isLast) {
      _offset = Offset(offset.dx - tp.width, offset.dy);
    }
    tp.paint(canvas, _offset);
  }

  /// 绘制X轴坐标点
  static void paintYText(
      {Canvas canvas,
      String text,
      Offset offset,
      TextAlign textAlign = TextAlign.center,
      int type,

      /// 是否需要垂直居中
      bool needVerticalCenter = false,

      /// 是否为最后一个坐标
      bool isLast = false,
      double minWidth = 0}) {
    TextSpan span = TextSpan(
      text: text,
      style: TextStyle(color: Color(0xFF999999), fontSize: 10),
    );
    TextPainter tp = TextPainter(
      textDirection: TextDirection.ltr,
      text: span,
      textAlign: textAlign,
    );
    Offset _offset = offset;

    tp.layout(minWidth: minWidth);
    double dx = offset.dx;
    if (type == 0) {
      dx = offset.dx - tp.width;
    }

    _offset = Offset(dx, offset.dy);

    _offset = Offset(dx, offset.dy - (tp?.height ?? 0) / 2);
    tp.paint(canvas, _offset);
  }

  /// 获得曲线样本并生成曲线路径对象
  /// start: 0
  /// end: 1
  /// path: 画笔Path
  /// fillPatn: 填充Path
  /// bottomPadding: 画布padding,用于计算
  /// fillStartX: 用于设置填充起始位置
  static void makeCatmullRomCurvePath(double start, double end, Path path,
      Path fillPatn, num bottomPadding, num fillStartX,
      {double width, double height, CatmullRomSpline catmullRomPath}) {
    path.reset();
    final Iterable<Curve2DSample> cmSplines =
        catmullRomPath.generateSamples(start: start, end: end);

    final List<Point<double>> points = <Point<double>>[];
    for (final Curve2DSample c2dSample in cmSplines) {
      if (c2dSample.value.dy < height - bottomPadding - 2) {
        points.add(Point<double>(c2dSample.value.dx, c2dSample.value.dy));
      }
    }
    // 有负值时，可能会报错
    if (points.length > 0) {
      //路径对象设置
      fillPatn.moveTo(fillStartX, height - bottomPadding);
      fillPatn.lineTo(points[0].x - 1, points[0].y);
      path.moveTo(points[0].x, points[0].y);
      for (int i = 1; i < points.length; i++) {
        path.lineTo(points[i].x, points[i].y);
        fillPatn.lineTo(points[i].x + 1, points[i].y);
      }
    }
  }

  static void drawCircle(
      Canvas canvas, Offset offset, Paint _paint, bool isEmpty) {
    if (isEmpty) {
      canvas.drawCircle(
          offset,
          3.0,
          _paint
            ..color = Color(0xFF999999)
            ..style = PaintingStyle.fill //绘画风格改为stroke
          );
    }
  }

  static paintXAxisChart(
    Canvas canvas,
    List<DoubleLineChartData> doubleLineChartDatas,
    double width,
    double leftPadding,
    double rightPadding,
    double height,
    double bottomPadding,
    Paint _paint,
  ) {
    /// x轴每个之间的间距
    double xStep = (width - leftPadding - rightPadding) /
        (doubleLineChartDatas.length - 1);

    List<Map<dynamic, dynamic>> xTextList = [];

    bool isEmpty = true;

    doubleLineChartDatas.asMap().forEach((int index, DoubleLineChartData item) {
      // 当值为负数时，按0来处理
      if (isEmpty && (item.leftY > 0 || item.rightY > 0)) {
        isEmpty = false;
      }
    });

    /// 修改画笔
    ///
    _paint.color = Color(0xFF999999);
    _paint.strokeWidth = 1.0;

    canvas.drawLine(Offset(leftPadding, height - bottomPadding),
        Offset(width - rightPadding, height - bottomPadding), _paint);

    // X轴最大展示7条数据
    if (doubleLineChartDatas.length > 7) {
      List arr = List.from(doubleLineChartDatas);
      arr.removeLast();
      arr.removeAt(0);
      xTextList.add(
          {'text': doubleLineChartDatas[0].xAxisData.toString(), 'index': 0});

      for (var i = 0; i < arr.length; i++) {
        if (i == (arr.length / 2).ceil() - 1) {
          xTextList.add({'text': arr[i].xAxisData.toString(), 'index': i + 1});
        }
        if (i == (arr.length / 2 / 2).ceil() - 1 ||
            i == (arr.length / 2 / 2).ceil() - 1 + (arr.length / 2).ceil()) {
          xTextList.add({'text': arr[i].xAxisData.toString(), 'index': i + 1});
        }
      }

      xTextList.add({
        'text': doubleLineChartDatas[doubleLineChartDatas.length - 1]
            .xAxisData
            .toString(),
        'index': doubleLineChartDatas.length - 1
      });
    } else {
      doubleLineChartDatas
          .asMap()
          .forEach((int index, DoubleLineChartData item) {
        xTextList.add({'text': item.xAxisData.toString(), 'index': index});
      });
    }

    xTextList.asMap().forEach((int index, Map<dynamic, dynamic> item) {
      if (index == 0) {
        drawCircle(canvas, Offset(leftPadding, height - bottomPadding), _paint,
            isEmpty);
        paintText(
          canvas: canvas,
          text: item['text'],
          offset: Offset(leftPadding, height - bottomPadding + 3),
        );
      } else if (index == xTextList.length - 1) {
        drawCircle(canvas, Offset(width - rightPadding, height - bottomPadding),
            _paint, isEmpty);
        paintText(
          canvas: canvas,
          text: item['text'],
          isLast: true,
          offset: Offset(width - rightPadding, height - bottomPadding + 3),
        );
      } else {
        drawCircle(
            canvas,
            Offset(leftPadding + xStep * item['index'], height - bottomPadding),
            _paint,
            isEmpty);
        paintText(
          canvas: canvas,
          text: item['text'],
          needhorizontalCenter: true,
          offset: Offset(
              leftPadding + xStep * item['index'], height - bottomPadding + 3),
        );
      }
    });
  }

  static void paintYAxisChart(
    Canvas canvas,
    List<DoubleLineChartData> doubleLineChartDatas,
    double width,
    double leftPadding,
    double rightPadding,
    double height,
    double topPadding,
    double bottomPadding,
    Paint _paint,
    int type,
  ) {
    List<Map<dynamic, dynamic>> yTextList = [];

    List<num> yValue = [];
    if (type == 0) {
      yValue = doubleLineChartDatas.map((e) => e.leftY).toList();
    } else {
      yValue = doubleLineChartDatas.map((e) => e.rightY).toList();
    }

    num maxNum = findMaxNum(yValue);
    double interval = findYAxisInterval(yValue);

    List<double> ticks = [];
    double startTick = 0;

    double endTick = maxNum == 0 ? 500 : (maxNum / interval).ceil() * interval;
    for (double tick = startTick; tick <= endTick; tick += interval) {
      ticks.add(tick);
    }

    for (int i = ticks.length - 1; i >= 0; i--) {
      String yFormat = (i * interval).toStringAsFixed(1);
      if ((i * interval) >= 10000 || (i * interval) <= -10000) {
        yFormat = "${((i * interval) / 10000).toStringAsFixed(1)}w";
      }
      if (yFormat.endsWith('.0')) {
        yFormat = yFormat.substring(0, yFormat.length - 2);
      }
      yTextList.add({'text': yFormat, 'index': i});
    }

    /// y轴每个之间的间距
    double yStep = (height - bottomPadding - topPadding) / (ticks.length - 1);

    yTextList.asMap().forEach((int index, Map<dynamic, dynamic> item) {
      double dx = 0;
      if (type == 0) {
        dx = leftPadding - 3;
      } else {
        dx = width - rightPadding + 3;
      }
      if (index == 0) {
        paintYText(
            canvas: canvas,
            text: item['text'],
            offset: Offset(dx, topPadding),
            type: type);
      } else if (index == yTextList.length - 1) {
        paintYText(
            canvas: canvas,
            text: item['text'],
            isLast: true,
            offset: Offset(dx, height - bottomPadding),
            type: type);
      } else {
        paintYText(
            canvas: canvas,
            text: item['text'],
            offset: Offset(dx, height - topPadding - yStep * item['index']),
            type: type);
      }
    });
  }

  static void paintCurveLineChart(Canvas canvas, List<Offset> offsetList,
      num width, num height, num bottomPadding, Paint _paint, Color color) {
    /// 绘制路径
    Path path = Path();

    // 填充路径
    Path fillPatn = Path();

    while (offsetList.length < 4) {
      num x = offsetList[offsetList.length - 1].dx;
      num y = offsetList[offsetList.length - 1].dy + 0.001;
      offsetList.add(Offset(x, y));
    }

    final CatmullRomSpline catmullRomPath = CatmullRomSpline(
      offsetList,
      startHandle: const Offset(0.099, 0.102),
      tension: 0.0,
    );

    makeCatmullRomCurvePath(
      0,
      1,
      path,
      fillPatn,
      bottomPadding,
      offsetList[0].dx,
      height: height,
      width: width,
      catmullRomPath: catmullRomPath,
    );

    _paint.color = color;
    _paint.strokeWidth = 2.0;
    _paint.style = PaintingStyle.stroke;
    canvas.drawPath(path, _paint);

    // 填充画笔
    _paint.color = Colors.transparent;
    _paint.style = PaintingStyle.fill;
    fillPatn.lineTo(
        offsetList[offsetList.length - 1].dx, height - bottomPadding);

    canvas.drawPath(fillPatn, _paint);
  }

  static void paintBrokenLineChart(
    Canvas canvas,
    List<Offset> offsetList,
    num height,
    num bottomPadding,
    Paint _paint,
    Color color,
  ) {
    /// 绘制路径
    Path path = Path();

    // 填充路径
    Path fillPatn = Path();

    num lastX = 0;

    path.moveTo(offsetList[0].dx, offsetList[0].dy);
    fillPatn.moveTo(offsetList[0].dx, height - bottomPadding);

    offsetList.asMap().forEach((int index, Offset item) {
      path.lineTo(item.dx, item.dy);
      fillPatn.lineTo(item.dx, item.dy);
      lastX = item.dx;
    });

    /// 绘制
    _paint.color = color;
    _paint.strokeWidth = 2.0;
    _paint.style = PaintingStyle.stroke;
    canvas.drawPath(path, _paint);

    // 填充画笔
    _paint.color = Color.fromRGBO(16, 106, 246, 0.1);
    _paint.style = PaintingStyle.fill;
    fillPatn.lineTo(lastX, height - bottomPadding);

    canvas.drawPath(fillPatn, _paint);
  }
}
