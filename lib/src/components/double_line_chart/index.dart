import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/double_line_chart/draw/draw_method.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/double_line_chart/widgets/double_grid_line.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/double_line_chart/widgets/double_grid_points.dart';

import 'modal/chart_model.dart';
import 'modal/index.dart';

class DoubleLineCharts extends StatefulWidget {
  DoubleLineCharts(
      {this.height = 300,
      this.width,
      this.doubleLineChartDatas = const [],
      this.lineType = 0,
      this.buildTip,
      this.leftYColor = const Color(0xFF0074FF),
      this.rightYColor = const Color(0xFFFF8C42),
      this.leftYname,
      this.rightYname});

  /// 高度
  final double height;

  /// 宽度
  final double width;

  /// DoubleLineChartData
  final List<DoubleLineChartData> doubleLineChartDatas;

  /// 提示Widget
  final Function buildTip;

  /// 0: 折线图  1：曲线图
  final int lineType;

  /// 左Y轴颜色
  final Color leftYColor;

  /// 右Y轴颜色
  final Color rightYColor;

  /// 左Y轴名称
  final String leftYname;

  /// 右Y轴名称
  final String rightYname;

  @override
  _DoubleLineChartsState createState() => _DoubleLineChartsState();
}

class _DoubleLineChartsState extends State<DoubleLineCharts> {
  List<Offset> offsetLeftList = [];
  List<Offset> offsetRightList = [];
  double left = -1;
  Offset localPosition;
  double top;
  double pointLeftY;
  double pointRightY;
  DoubleLineChartData nowFocusItem;

  final double bottomPadding = 24;
  final double topPadding = 24;

  void calcOffset(List<Offset> offsetLeftList, List<Offset> offsetRightList) {
    // 两点横坐标相同，用左Y轴计算即可
    for (int i = 0; i < offsetLeftList.length; i++) {
      if (localPosition.dx > offsetLeftList[offsetLeftList.length - 1].dx) {
        break;
      }
      if (i < offsetLeftList.length - 1 &&
          offsetLeftList[i].dx <= localPosition.dx &&
          localPosition.dx < offsetLeftList[i + 1].dx) {
        final Offset nowLeftOffset = getMinOffset(
            offsetLeftList[i], localPosition, offsetLeftList[i + 1], i);
        final Offset nowRightOffset = getMinOffset(
            offsetRightList[i], localPosition, offsetRightList[i + 1], i);
        if (nowLeftOffset.dx != left) {
          left = nowLeftOffset.dx;
          top = nowLeftOffset.dy - 100;
          pointLeftY = nowLeftOffset.dy;
          pointRightY = nowRightOffset.dy;
          if (nowLeftOffset.dy < 100) {
            top = (widget.height - bottomPadding - 100) / 2;
          } else if (nowLeftOffset.dy > widget.height - bottomPadding - 100) {
            top = widget.height - bottomPadding - 100;
          }
          top = 0;
        }
      }
    }
    setState(() {});
  }

  Offset getMinOffset(Offset left, Offset center, Offset right, int nowIndex) {
    if (center.dx - left.dx >= (right.dx - left.dx) / 2) {
      nowFocusItem = widget.doubleLineChartDatas[nowIndex + 1];
      return right;
    }

    /// 赋值当前选中的数据项
    if (widget.doubleLineChartDatas != null &&
        widget.doubleLineChartDatas.length > 0) {
      nowFocusItem = widget.doubleLineChartDatas[nowIndex];
    }
    return left;
  }

  @override
  void initState() {
    super.initState();
  }

  drawSelectedChar() {
    // 只有月度收入才会画竖线
    if (widget.lineType == 1) {
      int selectedIndex = 0;

      if (offsetLeftList != null && offsetLeftList.length > 0) {
        double distance = 0;
        if (selectedIndex < offsetLeftList.length) {
          if (selectedIndex == 0) {
            distance = offsetLeftList[selectedIndex].dx;
          } else {
            distance = offsetLeftList[selectedIndex].dx - 10;
          }
          Offset lc = Offset(distance, 2);
          localPosition = lc;
          calcOffset(offsetLeftList, offsetRightList);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> widgetList = [
      Container(
        decoration: BoxDecoration(
          color: Colors.white,
        ),
        height: widget.height,
        // child: RepaintBoundary(
        child: CustomPaint(
          painter: DoubleLineChartsPainter(
            height: widget.height,
            width: widget.width,
            doubleLineChartDatas: widget.doubleLineChartDatas,
            lineType: widget.lineType,
            onPaintEnd: (List<Offset> _offsetList, int type) {
              if (type == 0) {
                offsetLeftList = _offsetList;
              } else {
                offsetRightList = _offsetList;
              }
            },
          ),
        ),
        // ),
      ),
      GestureDetector(
        behavior: HitTestBehavior.translucent,
        onHorizontalDragStart: (DragStartDetails details) {
          if (details.localPosition.dx > offsetLeftList.first.dx ||
              details.localPosition.dx < offsetLeftList.last.dx) {
            localPosition = details.localPosition;
            calcOffset(offsetLeftList, offsetRightList);
          }
        },
        onHorizontalDragUpdate: (DragUpdateDetails details) {
          if (details.localPosition.dx > offsetLeftList.first.dx ||
              details.localPosition.dx < offsetLeftList.last.dx) {
            localPosition = details.localPosition;
            calcOffset(offsetLeftList, offsetRightList);
          }
        },
        child: Container(
          decoration: BoxDecoration(
            color: Colors.transparent,
          ),
          height: widget.height,
        ),
      ),
      Positioned(
        top: 0,
        left: 0,
        child: Row(
          children: <Widget>[
            Container(
              width: 6,
              height: 6,
              margin: EdgeInsets.only(right: 4.25),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2),
                color: widget.leftYColor,
              ),
            ),
            Text(
              widget.leftYname,
              style: TextStyle(
                fontSize: 12,
                color: Color(0xFF999999),
                fontWeight: FontWeight.w400,
              ),
            ),
            Container(
              width: 6,
              height: 6,
              margin: EdgeInsets.only(left: 10, right: 4.25),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2),
                color: widget.rightYColor,
              ),
            ),
            Text(
              widget.rightYname,
              style: TextStyle(
                fontSize: 12,
                color: Color(0xFF999999),
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
      left != -1
          ? DoubleGridLine(
              left: left > 0 ? left - 1 : left,
              height: widget.height,
              topPadding: topPadding,
              bottomPadding: bottomPadding,
            )
          : SizedBox.shrink(),
      left != -1
          ? DoubleGridPoints(
              left: left,
              pointY: pointLeftY,
              color: widget.leftYColor,
            )
          : SizedBox.shrink(),
      left != -1
          ? DoubleGridPoints(
              left: left,
              pointY: pointRightY,
              color: widget.rightYColor,
            )
          : SizedBox.shrink(),
      Visibility(
        visible: left != -1,
        child: Positioned(
          left: left,
          top: top,
          child: IgnorePointer(
            ignoring: true,
            child: FractionalTranslation(
              translation: left < 100
                  ? Offset(0, 0)
                  : (widget.width - left) < 100
                      ? Offset(-1, 0)
                      : Offset(-0.5, 0),
              child: widget.buildTip(nowFocusItem),
            ),
          ),
        ),
      ),
    ];

    // if (offsetList != null && offsetList.length != 0) {
    //   for (int i = 0; i < offsetList.length; i++) {
    //     Offset offset = offsetList[i];
    //     if (i == 0) {
    //       offset = Offset(offset.dx + 10, offset.dy);
    //     } else if (i == offsetList.length - 1) {
    //       offset = Offset(offset.dx - 10, offset.dy);
    //     }
    //     widgetList.add(_buildBackgroundLine(offsetList[i]));
    //   }
    // }

    return Stack(
      children: widgetList,
    );
  }
}

class DoubleLineChartsPainter extends CustomPainter with DoubleLineChartsModel {
  DoubleLineChartsPainter(
      {this.height,
      this.width,
      this.doubleLineChartDatas,
      this.onPaintEnd,
      this.leftYColor = const Color(0xFF0074FF),
      this.rightYColor = const Color(0xFFFF8C42),
      this.lineType});
  final double height;
  final double width;
  final int lineType;
  final Function onPaintEnd;
  final List<DoubleLineChartData> doubleLineChartDatas;
  final Color leftYColor;
  final Color rightYColor;
  List<Offset> offsetLeftList = [];
  List<Offset> offsetRightList = [];

  Paint _paint;

  final double leftPadding = 30;
  final double rightPadding = 30;
  final double bottomPadding = 24;
  final double topPadding = 24;

  @override
  void paint(Canvas canvas, Size size) {
    // 创建画笔
    _paint = Paint();
    paintXAxis(canvas);
    paintYAxis(canvas);
    comCoordinate(canvas, doubleLineChartDatas.map((e) => e.leftY).toList(),
        leftYColor, 0);
    comCoordinate(canvas, doubleLineChartDatas.map((e) => e.rightY).toList(),
        rightYColor, 1);
  }

  /// 绘制X轴轴线
  @override
  void paintXAxis(Canvas canvas) {
    DrawMethod.paintXAxisChart(canvas, doubleLineChartDatas, width, leftPadding,
        rightPadding, height, bottomPadding, _paint);
  }

  /// 绘制Y轴轴线
  @override
  void paintYAxis(Canvas canvas) {
    DrawMethod.paintYAxisChart(canvas, doubleLineChartDatas, width, leftPadding,
        rightPadding, height, topPadding, bottomPadding, _paint, 0);
    DrawMethod.paintYAxisChart(canvas, doubleLineChartDatas, width, leftPadding,
        rightPadding, height, topPadding, bottomPadding, _paint, 1);
  }

  /// 计算坐标
  /// type 0 左Y轴 1 右Y轴
  void comCoordinate(Canvas canvas, List<num> yValue, Color color, int type) {
    /// 当前坐标轴最大值
    num maxYValues = DrawMethod.findYAxisMaxNum(yValue);

    /// 计算Y轴相对比例
    double percent = (height - topPadding - bottomPadding) / maxYValues;

    /// 每个点的X坐标距离f
    double xStep = (width - leftPadding - rightPadding) / (yValue.length - 1);

    bool isEmpty = false;

    if (type == 0) {
      offsetLeftList.clear();
    } else {
      offsetRightList.clear();
    }
    List<Offset> offsetListTmp = [];
    for (var i = 0; i < yValue.length; i++) {
      Offset offsetDot = Offset(0, 0);
      num offsetRight = yValue[i] <= 0
          ? height - bottomPadding
          : height - yValue[i] * percent - bottomPadding - 2;
      if (!isEmpty && yValue[i] != 0) {
        isEmpty = true;
      }

      if (yValue[i] == null) {
        num prevY =
            (height - (yValue[i - 1] * percent) - bottomPadding - 2) + 0.00001;
        offsetDot = Offset(leftPadding + xStep * (i - 1), prevY);
        offsetListTmp.add(offsetDot);
        break;
      } else {
        if (i == 0) {
          offsetDot = Offset(leftPadding, offsetRight);
        } else if (i == yValue.length - 1) {
          offsetDot = Offset(width - rightPadding, offsetRight);
        } else {
          offsetDot = Offset(leftPadding + xStep * i, offsetRight);
        }
        offsetListTmp.add(offsetDot);
      }
    }

    onPaintEnd(offsetListTmp, type);
    if (type == 0) {
      offsetLeftList = offsetListTmp;
    } else {
      offsetRightList = offsetListTmp;
    }

    if (isEmpty) {
      if (lineType == 0) {
        paintBrokenLine(canvas, color, offsetListTmp);
      } else if (lineType == 1) {
        paintCurveLine(canvas, color, offsetListTmp);
      }
    }
  }

  /// 绘制折线图
  @override
  void paintBrokenLine(Canvas canvas, Color color, List<Offset> offsetList) {
    if (offsetList.length > 0) {
      DrawMethod.paintBrokenLineChart(
          canvas, offsetList, height, bottomPadding, _paint, color);
    }
  }

  /// 绘制曲线图
  @override
  void paintCurveLine(Canvas canvas, Color color, List<Offset> offsetList) {
    if (offsetList.length > 0) {
      DrawMethod.paintCurveLineChart(
        canvas,
        offsetList,
        width,
        height,
        bottomPadding,
        _paint,
        color,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
