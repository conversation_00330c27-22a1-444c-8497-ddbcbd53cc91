import 'package:flutter/cupertino.dart';

class DoubleGridLine extends StatelessWidget {
  const DoubleGridLine(
      {@required this.left,
      @required this.height,
      @required this.topPadding,
      @required this.bottomPadding,
      this.isBackground});
  final num left;
  final num height;
  final num topPadding;
  final num bottomPadding;
  final bool isBackground;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: left,
      child: Container(
        height: height - topPadding - bottomPadding,
        width: 1,
        margin: EdgeInsets.only(top: topPadding),
        color: isBackground == true ? Color(0x1A999999) : Color(0xFF999999),
      ),
    );
  }
}
