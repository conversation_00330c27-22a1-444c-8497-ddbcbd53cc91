import 'package:flutter/cupertino.dart';

class DoubleGridPoints extends StatelessWidget {
  const DoubleGridPoints(
      {@required this.left, @required this.pointY, @required this.color});
  final num left;
  final num pointY;
  final Color color;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: left ?? 0,
      top: pointY,
      child: FractionalTranslation(
        translation: Offset(-0.5, -0.5),
        child: Container(
          width: 10,
          height: 10,
          decoration: BoxDecoration(
            color: color,
            border: Border.all(
              width: 2,
              color: Color(0xFFFFFFFF),
            ),
            borderRadius: BorderRadius.all(
              Radius.circular(5),
            ),
          ),
        ),
      ),
    );
  }
}
