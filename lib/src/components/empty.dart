import 'package:flutter/material.dart';

class Empty extends StatelessWidget {
  Empty({
    this.title = '暂无内容',
    this.margin = const EdgeInsets.all(10),
    this.imgSize = 80,
  });
  final String title;
  final EdgeInsets margin;

  /// 图片大小
  final double imgSize;

  @override
  Widget build(BuildContext context) {
    return Center(
        child: Container(
      margin: margin,
      child: Column(
        children: <Widget>[
          Container(
            margin: EdgeInsets.only(top: 20, bottom: 20),
            child: Image.network(
              // 更换新袋鼠链接
              'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/19e0ae7c1b9e3965/empty_roo_new.png',
              width: imgSize,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              color: Color(0xFF999999),
            ),
          ),
        ],
      ),
    ));
  }
}
