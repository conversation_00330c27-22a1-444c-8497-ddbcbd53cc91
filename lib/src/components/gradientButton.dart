import 'package:flutter/material.dart';

class GradientButton extends StatelessWidget {
  GradientButton(
      {@required this.text,
      this.gradient,
      this.margin,
      this.padding,
      this.width,
      this.height,
      this.borderRadius,
      @required this.onTap});

  /// 按钮文案，字符串 or Text Widget
  final text;

  /// 渐变颜色，默认黄色渐变
  final LinearGradient gradient;

  /// margin
  final EdgeInsets margin;

  /// padding
  final EdgeInsets padding;

  /// width
  final double width;

  /// height
  final double height;

  /// 圆角
  final BorderRadius borderRadius;

  /// 点击事件
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: padding ?? EdgeInsets.symmetric(horizontal: 10),
        margin: margin ?? EdgeInsets.only(top: 12),
        alignment: Alignment.center,
        width: width ?? 72,
        height: height ?? 28,
        decoration: BoxDecoration(
          borderRadius: borderRadius ?? BorderRadius.circular(22),
          gradient: gradient ??
              LinearGradient(colors: [
                Color(0xFFFFE14D),
                Color(0xFFFFC34D),
              ]),
        ),
        child: text is String
            ? Text(
                text,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF222222),
                ),
              )
            : text,
      ),
    );
  }
}
