import 'package:flutter/material.dart';
import 'package:roo_flutter/basic_components/date_picker_adapted/roo_date_picker_adapted.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:roo_flutter/tools/response_system.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/gradient.dart';

/// 最近x月的组件，点击全部按钮返回null，点击月份返回当前月的DateTime对象
class LastMonthSelect extends StatefulWidget {
  /// 最左边button的文案，默认为“全部”
  LastMonthSelect(
      {this.monthCnt = 4, this.onDateChange, this.hasCustom = false});

  final int monthCnt;

  /// 是否有自定义时间选择
  final bool hasCustom;
  final Function(String beginDate, String endDate) onDateChange;

  @override
  LastMonthSelectState createState() => LastMonthSelectState();
}

class LastMonthSelectState extends State<LastMonthSelect> {
  int monthIndex = 0;

  bool isShowCus = false;

  // RangeDatePickerController _rangeController;

  Map<String, String> cusTitle = {
    'all': '全部时间',
    'cus': '自定义',
  };

  Map<String, int> cusIndex = {
    'all': 0,
    'cus': -1,
  };

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    // _rangeController?.dispose();
  }

  // 单一月份的widget
  Widget monthWidget({DateTime date, String buttonType}) {
    Color color = Color(0xFFF5F6FA);
    bool selected =
        (date?.month == null ? cusIndex[buttonType] : date.month) == monthIndex;
    return GestureDetector(
      onTap: () {
        // 改变当前选中index
        if (date == null) {
          monthIndex = cusIndex[buttonType];
        } else {
          monthIndex = date.month;
        }

        // 自定义时间操作
        if (buttonType == 'cus') {
          isShowCus = true;
        } else {
          isShowCus = false;
          // 默认全部时间
          String beginDate = '';
          String endDate = '';
          if (date != null) {
            beginDate = DateFormat.formatYYYYMMDD(date.millisecondsSinceEpoch);
            endDate = DateFormat.formatYYYYMMDD(
                DateTime(date.year, date.month + 1, date.day)
                    .subtract(const Duration(days: 1))
                    .millisecondsSinceEpoch);
          }
          if (widget?.onDateChange != null) {
            widget.onDateChange(beginDate, endDate);
          }
        }
        setState(() {});
      },
      child: Container(
        height: ResponsiveSystem.bothAppPc(
          runApp: 32.0,
          runPc: 36.0,
        ),
        padding: EdgeInsets.symmetric(horizontal: 12),
        margin: EdgeInsets.only(
          right: 8,
          top: 2,
          bottom: 2,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          gradient: selected ? yellowGradient : null,
          color: selected ? null : color,
        ),
        child: Center(
          child: Text(
            '${date == null ? '${cusTitle[buttonType] ?? ""}' : '${date.month}月'}',
            style: TextStyle(
              color: Color(0xFF222222),
              fontWeight: selected ? FontWeight.w500 : FontWeight.w400,
              fontSize: 12,
            ),
          ),
        ),
      ),
    );
  }

  // 月份选择
  monthListWidget(List<DateTime> monthList) {
    List<Widget> widgetList = [];

    // 增加全部
    widgetList.add(monthWidget(buttonType: 'all'));

    // 遍历月份
    monthList.forEach((date) {
      widgetList.add(monthWidget(date: date));
    });

    // 添加自定义
    if (widget.hasCustom == true) {
      widgetList.add(monthWidget(buttonType: 'cus'));
    }

    if (isShowCus) {
      DateTime today = DateTime.now();
      widgetList.add(
        Container(
          width: 215,
          // height: 36,
          child: RooDatePickerAdapted(
            isRange: true,
            rangeFirstLastDate: [
              today.subtract(const Duration(days: 180)),
              today
            ],
            rangeInitialDate: [today, today],
            onRangeChange: (List<DateTime> dtList) {
              if (dtList.length == 2) {
                String beginDate =
                    DateFormat.formatYYYYMMDD(dtList[0].millisecondsSinceEpoch);
                String endDate =
                    DateFormat.formatYYYYMMDD(dtList[1].millisecondsSinceEpoch);
                if (widget?.onDateChange != null) {
                  widget.onDateChange(beginDate, endDate);
                }
              }
              print('onChanged dtList = $dtList');
            },
          ),
        ),
      );
    }
    return Container(
      padding: EdgeInsets.fromLTRB(
          0, ResponsiveSystem.bothAppPc(runApp: 12.0, runPc: 0.0), 4, 0),
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 展示4个月
          Row(
            children: widgetList,
          ),
        ],
      ),
    );
  }

  getLastMonths() {
    DateTime now = DateTime.now();
    List<DateTime> monthList = [];
    for (int i = 0; i < widget.monthCnt; i++) {
      DateTime firstDay = DateTime(now.year, now.month - i, 1);
      monthList.add(firstDay);
    }
    return monthList;
  }

  @override
  Widget build(BuildContext context) {
    return monthListWidget(getLastMonths());
  }
}
