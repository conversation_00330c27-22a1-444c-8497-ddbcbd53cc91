import 'dart:ui';

import 'package:waimai_e_fe_flutter_finance/src/components/line_chart/modal/index.dart';

void paintXAxisChart(
  Canvas canvas,
  List<LinchartData> linChartDatas,
  double width,
  double leftPadding,
  double rightPadding,
  double height,
  double bottomPadding,
  Paint _paint,
) {
  /// x轴每个之间的间距
  double xStep =
      (width - leftPadding - rightPadding) / (linChartDatas.length - 1);

  List<Map<dynamic, dynamic>> xTextList = [];

  bool isEmpty = true;

  linChartDatas.asMap().forEach((int index, LinchartData item) {
    // 当值为负数时，按0来处理
    if (isEmpty && item.value > 0) {
      isEmpty = false;
    }
  });

  /// 修改画笔
  ///
  _paint.color = isEmpty ? Color(0xFF0E75F7) : Color(0xFF999999);
  _paint.strokeWidth = 1.0;

  canvas.drawLine(Offset(leftPadding, height - bottomPadding),
      Offset(width - rightPadding, height - bottomPadding), _paint);

  // X轴最大展示7条数据
  if (linChartDatas.length > 7) {
    List arr = List.from(linChartDatas);
    arr.removeLast();
    arr.removeAt(0);
    xTextList.add({'text': linChartDatas[0].xAxisData.toString(), 'index': 0});

    for (var i = 0; i < arr.length; i++) {
      if (i == (arr.length / 2).ceil() - 1) {
        xTextList.add({'text': arr[i].xAxisData.toString(), 'index': i + 1});
      }
      if (i == (arr.length / 2 / 2).ceil() - 1 ||
          i == (arr.length / 2 / 2).ceil() - 1 + (arr.length / 2).ceil()) {
        xTextList.add({'text': arr[i].xAxisData.toString(), 'index': i + 1});
      }
    }

    xTextList.add({
      'text': linChartDatas[linChartDatas.length - 1].xAxisData.toString(),
      'index': linChartDatas.length - 1
    });
  } else {
    linChartDatas.asMap().forEach((int index, LinchartData item) {
      xTextList.add({'text': item.xAxisData.toString(), 'index': index});
    });
  }

  xTextList.asMap().forEach((int index, Map<dynamic, dynamic> item) {
    if (index == 0) {
      drawCircle(
          canvas, Offset(leftPadding, height - bottomPadding), _paint, isEmpty);
      paintText(
        canvas: canvas,
        text: item['text'],
        offset: Offset(leftPadding, height - bottomPadding + 3),
      );
    } else if (index == xTextList.length - 1) {
      drawCircle(canvas, Offset(width - rightPadding, height - bottomPadding),
          _paint, isEmpty);
      paintText(
        canvas: canvas,
        text: item['text'],
        isLast: true,
        offset: Offset(width - rightPadding, height - bottomPadding + 3),
      );
    } else {
      drawCircle(canvas, Offset(xStep * item['index'], height - bottomPadding),
          _paint, isEmpty);
      paintText(
        canvas: canvas,
        text: item['text'],
        needhorizontalCenter: true,
        offset: Offset(xStep * item['index'], height - bottomPadding + 3),
      );
    }
  });
}
