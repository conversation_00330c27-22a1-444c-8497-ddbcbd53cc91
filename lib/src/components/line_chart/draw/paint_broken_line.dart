import 'dart:ui';

void paintBrokenLineChart(
  Canvas canvas,
  List<Offset> offsetList,
  num height,
  num bottomPadding,
  Paint _paint,
) {
  /// 绘制路径
  Path path = Path();

  // 填充路径
  Path fillPatn = Path();

  num lastX = 0;

  path.moveTo(offsetList[0].dx, offsetList[0].dy);
  fillPatn.moveTo(offsetList[0].dx, height - bottomPadding);

  offsetList.asMap().forEach((int index, Offset item) {
    path.lineTo(item.dx, item.dy);
    fillPatn.lineTo(item.dx, item.dy);
    lastX = item.dx;
  });

  /// 绘制
  _paint.color = Color(0xFF0E75F7);
  _paint.strokeWidth = 2.0;
  _paint.style = PaintingStyle.stroke;
  canvas.drawPath(path, _paint);

  // 填充画笔
  _paint.color = Color.fromRGBO(16, 106, 246, 0.1);
  _paint.style = PaintingStyle.fill;
  fillPatn.lineTo(lastX, height - bottomPadding);

  canvas.drawPath(fillPatn, _paint);
}
