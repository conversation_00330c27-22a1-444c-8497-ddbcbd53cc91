import 'dart:ui';

import 'package:flutter/animation.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/line_chart/modal/index.dart';

void paintCurveLineChart(
  Canvas canvas,
  List<Offset> offsetList,
  num width,
  num height,
  num bottomPadding,
  Paint _paint,
) {
  /// 绘制路径
  Path path = Path();

  // 填充路径
  Path fillPatn = Path();

  while (offsetList.length < 4) {
    num x = offsetList[offsetList.length - 1].dx;
    num y = offsetList[offsetList.length - 1].dy + 0.001;
    offsetList.add(Offset(x, y));
  }

  final CatmullRomSpline catmullRomPath = CatmullRomSpline(
    offsetList,
    startHandle: const Offset(0.099, 0.102),
    tension: 0.0,
  );

  makeCatmullRomCurvePath(
    0,
    1,
    path,
    fillPatn,
    bottomPadding,
    offsetList[0].dx,
    height: height,
    width: width,
    catmullRomPath: catmullRomPath,
  );

  _paint.color = Color(0xFF0E75F7);
  _paint.strokeWidth = 2.0;
  _paint.style = PaintingStyle.stroke;
  canvas.drawPath(path, _paint);

  // 填充画笔
  _paint.color = Color.fromRGBO(16, 106, 246, 0.1);
  _paint.style = PaintingStyle.fill;
  fillPatn.lineTo(offsetList[offsetList.length - 1].dx, height - bottomPadding);

  canvas.drawPath(fillPatn, _paint);
}
