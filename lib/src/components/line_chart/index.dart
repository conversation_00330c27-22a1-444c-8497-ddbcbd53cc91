import 'dart:async';

import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/line_chart/draw/paint_XAxis.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/line_chart/draw/paint_broken_line.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/line_chart/draw/paint_curve_line.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/line_chart/widgets/grid_line.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/line_chart/widgets/grid_points.dart';

import 'modal/chart_model.dart';
import 'modal/index.dart';

/// [paintText]
///  绘制XY轴 [paintXYAxis]
///  绘制XY轴箭头 [paintScale]
///  绘制折线图部分 [paintLines]
final double leftPadding = 0;
final double rightPadding = 0;
final double bottomPadding = 24;
final double topPadding = 24;

class LineCharts extends StatefulWidget {
  LineCharts(
      {this.height = 300,
      this.width,
      this.linChartDatas = const [],
      this.lineType = 0,
      this.buildTip,
      this.inputMonth});

  /// 高度
  final double height;

  /// 宽度
  final double width;

  /// LinchartData
  final List<LinchartData> linChartDatas;

  /// 提示Widget
  final Function buildTip;

  /// 0: 折线图  1：曲线图
  final int lineType;

  // 已选中的月份
  final String inputMonth;

  @override
  _LineChartsState createState() => _LineChartsState();
}

class _LineChartsState extends State<LineCharts> {
  List<Offset> offsetList = [];
  double left = -1;
  Offset localPosition;
  double top;
  double pointY;
  LinchartData nowFocusItem;

  void calcOffset() {
    for (int i = 0; i < offsetList.length; i++) {
      if (localPosition.dx > offsetList[offsetList.length - 1].dx) {
        // print(
        //     'break ${localPosition.dx} - ${offsetList[offsetList.length - 1].dx}');
        break;
      }
      if (i < offsetList.length - 1 &&
          offsetList[i].dx <= localPosition.dx &&
          localPosition.dx < offsetList[i + 1].dx) {
        final Offset nowOffset =
            getMinOffset(offsetList[i], localPosition, offsetList[i + 1], i);
        if (nowOffset.dx != left) {
          // if (i == 0) {
          //   left = nowOffset.dx + 10;
          // } else if (i == offsetList.length - 2) {
          //   left = nowOffset.dx - 10;
          // } else {
          //   left = nowOffset.dx;
          // }
          left = nowOffset.dx;
          // print('i = $i nowOffset.dx ${nowOffset.dx}');
          top = nowOffset.dy - 100;
          pointY = nowOffset.dy;
          if (nowOffset.dy < 100) {
            top = (widget.height - bottomPadding - 100) / 2;
          } else if (nowOffset.dy > widget.height - bottomPadding - 100) {
            top = widget.height - bottomPadding - 100;
          }
          top = 0;
        }
      }
    }
    setState(() {});
  }

  Offset getMinOffset(Offset left, Offset center, Offset right, int nowIndex) {
    if (center.dx - left.dx >= (right.dx - left.dx) / 2) {
      nowFocusItem = widget.linChartDatas[nowIndex + 1];
      return right;
    }

    /// 赋值当前选中的数据项
    if (widget.linChartDatas != null && widget.linChartDatas.length > 0) {
      nowFocusItem = widget.linChartDatas[nowIndex];
    }
    return left;
  }

  // 是否第一次进页面，用于判断是否需要加月份竖线
  bool isFirst = false;

  @override
  void initState() {
    super.initState();
    isFirst = true;
    calcOffset();
  }

  drawSelectedMonthChar() {
    // 只有月度收入才会画竖线
    if (widget.lineType == 1) {
      int selectedIndex = 0;
      if (widget.linChartDatas != null) {
        widget.linChartDatas.asMap().forEach((int index, LinchartData value) {
          if (value.xAxisData == widget.inputMonth) {
            selectedIndex = index;
          }
        });
      }

      if (offsetList != null && offsetList.length > 0) {
        double distance = 0;
        if (offsetList != null &&
            offsetList.length > 0 &&
            selectedIndex < offsetList.length) {
          if (selectedIndex == 0) {
            distance = offsetList[selectedIndex].dx;
          } else {
            distance = offsetList[selectedIndex].dx - 10;
          }
          Offset lc = Offset(distance, 2);
          localPosition = lc;
          calcOffset();
        }
      }
    }
  }

  // _buildBackgroundLine(Offset offset) {
  //   return GridLine(
  //     left: offset.dx,
  //     height: widget.height,
  //     topPadding: topPadding,
  //     bottomPadding: bottomPadding,
  //     isBackground: true,
  //   );
  // }

  @override
  void didUpdateWidget(LineCharts oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.lineType == 1) {
      if (oldWidget.inputMonth != widget.inputMonth || isFirst == true) {
        if (isFirst) {
          Future.delayed(Duration(milliseconds: 500)).then((value) {
            drawSelectedMonthChar();
          });
        } else {
          drawSelectedMonthChar();
        }
        print('inputMonth---- ${widget.inputMonth}');
      }
      isFirst = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    // print('left $left');
    List<Widget> widgetList = [
      Container(
        decoration: BoxDecoration(
          color: Colors.white,
        ),
        height: widget.height,
        // child: RepaintBoundary(
        child: CustomPaint(
          painter: LineChartsPainter(
            height: widget.height,
            width: widget.width,
            linChartDatas: widget.linChartDatas,
            lineType: widget.lineType,
            onPainEnd: (List<Offset> _offsetList) {
              offsetList = _offsetList;
            },
          ),
        ),
        // ),
      ),
      GestureDetector(
        behavior: HitTestBehavior.translucent,
        onHorizontalDragStart: (DragStartDetails details) {
          if (details.localPosition.dx > offsetList.first.dx ||
              details.localPosition.dx < offsetList.last.dx) {
            localPosition = details.localPosition;
            calcOffset();
          }
        },
        onHorizontalDragUpdate: (DragUpdateDetails details) {
          if (details.localPosition.dx > offsetList.first.dx ||
              details.localPosition.dx < offsetList.last.dx) {
            localPosition = details.localPosition;
            calcOffset();
          }
        },
        // onHorizontalDragEnd: (DragEndDetails details) {
        //   if (widget.lineType != 1) {
        //     left = -1;
        //     setState(() {});
        //   }
        // },
        child: Container(
          decoration: BoxDecoration(
            color: Colors.transparent,
          ),
          height: widget.height,
        ),
      ),
      Positioned(
        top: 0,
        left: 0,
        child: Row(
          children: <Widget>[
            Container(
              width: 6,
              height: 6,
              margin: EdgeInsets.only(right: 4.25),
              color: Color(0xFF0071FB),
            ),
            Text(
              '金额',
              style: TextStyle(
                fontSize: 12,
                color: Color(0xFF999999),
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
      left != -1
          ? GridLine(
              left: left > 0 ? left - 1 : left,
              height: widget.height,
              topPadding: topPadding,
              bottomPadding: bottomPadding,
            )
          : SizedBox.shrink(),
      left != -1
          ? GridPoints(
              left: left,
              pointY: pointY,
            )
          : SizedBox.shrink(),
      Visibility(
        visible: left != -1,
        child: Positioned(
          left: left,
          top: top,
          child: IgnorePointer(
            ignoring: true,
            child: FractionalTranslation(
              translation: left < 100
                  ? Offset(0, 0)
                  : (widget.width - left) < 100
                      ? Offset(-1, 0)
                      : Offset(-0.5, 0),
              child: widget.buildTip(nowFocusItem),
            ),
          ),
        ),
      ),
    ];

    return Stack(
      children: widgetList,
    );
  }
}

class LineChartsPainter extends CustomPainter with ChartsModel {
  LineChartsPainter(
      {this.height,
      this.width,
      this.linChartDatas,
      this.onPainEnd,
      this.lineType});
  final double height;
  final double width;
  final int lineType;
  final ValueChanged<List<Offset>> onPainEnd;
  final List<LinchartData> linChartDatas;

  ///每个坐标点的位置
  List<Offset> offsetList = [];

  Paint _paint;

  @override
  void paint(Canvas canvas, Size size) {
    // 创建画笔
    _paint = Paint();
    paintXAxis(canvas);
    comCoordinate(canvas);
  }

  /// 绘制X轴轴线
  @override
  void paintXAxis(Canvas canvas) {
    paintXAxisChart(canvas, linChartDatas, width, leftPadding, rightPadding,
        height, bottomPadding, _paint);
  }

  /// 计算坐标
  void comCoordinate(Canvas canvas) {
    /// 当前数据最大值
    num maxYValues = findMaxNum(linChartDatas);

    /// 计算Y轴相对比例
    double percent = (height - topPadding - bottomPadding) / maxYValues;

    /// 每个点的X坐标距离
    double xStep =
        (width - leftPadding - rightPadding) / (linChartDatas.length - 1);

    bool isEmpty = false;

    offsetList.clear();
    for (var i = 0; i < linChartDatas.length; i++) {
      Offset offsetDot = Offset(0, 0);
      num offsetRight = linChartDatas[i].value <= 0
          ? height - bottomPadding
          : height - linChartDatas[i].value * percent - bottomPadding - 2;
      if (!isEmpty && linChartDatas[i].value != 0) {
        isEmpty = true;
      }

      if (linChartDatas[i].value == null) {
        num prevY = (height -
                (linChartDatas[i - 1].value * percent) -
                bottomPadding -
                2) +
            0.00001;
        offsetDot = Offset(xStep * (i - 1), prevY);
        offsetList.add(offsetDot);
        break;
      } else {
        if (i == 0) {
          offsetDot = Offset(leftPadding, offsetRight);
        } else if (i == linChartDatas.length - 1) {
          offsetDot = Offset(width, offsetRight);
        } else {
          offsetDot = Offset(xStep * i, offsetRight);
        }
        offsetList.add(offsetDot);
      }
    }

    onPainEnd(offsetList);

    if (isEmpty) {
      if (lineType == 0) {
        paintBrokenLine(canvas);
      } else if (lineType == 1) {
        paintCurveLine(canvas);
      }
    }
  }

  /// 绘制折线图
  @override
  void paintBrokenLine(Canvas canvas) {
    if (offsetList.length > 0) {
      paintBrokenLineChart(canvas, offsetList, height, bottomPadding, _paint);
    }
  }

  /// 绘制曲线图
  @override
  void paintCurveLine(Canvas canvas) {
    if (offsetList.length > 0) {
      paintCurveLineChart(
        canvas,
        offsetList,
        width,
        height,
        bottomPadding,
        _paint,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
