import 'dart:math';

import 'package:flutter/cupertino.dart';

/// 计算Y轴最大值
num findMaxNum(List<LinchartData> arr) {
  num maxNum = 0;
  arr.asMap().forEach((int index, LinchartData item) {
    if (item.value != null) {
      if (item.value > maxNum) {
        maxNum = item.value;
      }
    }
  });
  return maxNum;
}

/// 绘制X轴坐标点
void paintText(
    {Canvas canvas,
    String text,
    Offset offset,
    TextAlign textAlign = TextAlign.center,

    /// 是否需要垂直居中
    bool needVerticalCenter = false,
    bool needhorizontalCenter = false,

    /// 是否为最后一个坐标
    bool isLast = false,
    double minWidth = 0}) {
  TextSpan span = TextSpan(
    text: text,
    style: TextStyle(color: Color(0xFF999999), fontSize: 10),
  );
  TextPainter tp = TextPainter(
    textDirection: TextDirection.ltr,
    text: span,
    textAlign: textAlign,
  );
  Offset _offset = offset;

  tp.layout(minWidth: minWidth);
  if (needVerticalCenter == true) {
    _offset = Offset(offset.dx, offset.dy - (tp?.height ?? 0) / 2);
  }
  if (needhorizontalCenter) {
    _offset = Offset(offset.dx - (tp.width / 2), offset.dy);
  }
  if (isLast) {
    _offset = Offset(offset.dx - tp.width, offset.dy);
  }
  tp.paint(canvas, _offset);
}

/// 获得曲线样本并生成曲线路径对象
/// start: 0
/// end: 1
/// path: 画笔Path
/// fillPatn: 填充Path
/// bottomPadding: 画布padding,用于计算
/// fillStartX: 用于设置填充起始位置
void makeCatmullRomCurvePath(double start, double end, Path path, Path fillPatn,
    num bottomPadding, num fillStartX,
    {double width, double height, CatmullRomSpline catmullRomPath}) {
  path.reset();
  final Iterable<Curve2DSample> cmSplines =
      catmullRomPath.generateSamples(start: start, end: end);

  final List<Point<double>> points = <Point<double>>[];
  for (final Curve2DSample c2dSample in cmSplines) {
    if (c2dSample.value.dy < height - bottomPadding - 2) {
      points.add(Point<double>(c2dSample.value.dx, c2dSample.value.dy));
    }
  }
  // 有负值时，可能会报错
  if (points.length > 0) {
    //路径对象设置
    fillPatn.moveTo(fillStartX, height - bottomPadding);
    fillPatn.lineTo(points[0].x - 1, points[0].y);
    path.moveTo(points[0].x, points[0].y);
    for (int i = 1; i < points.length; i++) {
      path.lineTo(points[i].x, points[i].y);
      fillPatn.lineTo(points[i].x + 1, points[i].y);
    }
  }
}

void drawCircle(Canvas canvas, Offset offset, Paint _paint, bool isEmpty) {
  if (isEmpty) {
    canvas.drawCircle(
        offset,
        3.0,
        _paint
          ..color = Color(0xFF0E75F7)
          ..style = PaintingStyle.fill //绘画风格改为stroke
        );
  }
}

class LinchartData {
  LinchartData({this.xAxisData, this.value, this.tm});
  dynamic xAxisData;
  num value;
  num tm;
}
