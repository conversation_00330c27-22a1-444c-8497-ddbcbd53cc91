import 'package:flutter/cupertino.dart';

class GridPoints extends StatelessWidget {
  const GridPoints({
    @required this.left,
    @required this.pointY,
  });
  final num left;
  final num pointY;

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: left ?? 0,
      top: pointY,
      child: FractionalTranslation(
        translation: Offset(-0.5, -0.5),
        child: Container(
          width: 10,
          height: 10,
          decoration: BoxDecoration(
            color: Color(0xFF0E75F7),
            border: Border.all(
              width: 2,
              color: Color(0xFFFFFFFF),
            ),
            borderRadius: BorderRadius.all(
              Radius.circular(5),
            ),
          ),
        ),
      ),
    );
  }
}
