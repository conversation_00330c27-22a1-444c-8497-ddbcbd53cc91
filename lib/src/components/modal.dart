import 'package:flutter/material.dart';
import 'package:waimai_e_fe_ffw_utils/platform_tool.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/uiTools.dart';

class Modal {
  static openDialogPCAndPageApp(
    BuildContext context,

    /// flutter路由
    String route,

    /// 弹窗页面内容
    Widget modalContent, {

    /// 弹窗高度
    double height,

    /// 弹窗宽度
    double width,

    /// 弹窗标题
    String title,
    String channel,

    /// 内容区域背景颜色
    Color backgroundColor = Colors.white,
    Map<String, dynamic> params,
  }) {
    if (UITools.isPc) {
      Modal.showModalIframe(
        context,
        modalContent,
        title ?? '',
        width: width,
        height: height,
        backgroundColor: backgroundColor,
      );
    } else {
      RouterTools.flutterPageUrl(
        context,
        route,
        channel: channel,
        params: params,
      );
    }
  }

  /// 完全自定义的弹窗 Iframe形式
  static Future<bool> showModalIframe(
      BuildContext context, Widget child, String title,
      {double width, double height, Color backgroundColor}) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: true,
      builder: (_) => DefaultTextStyle(
          style: TextStyle(
            decoration: TextDecoration.none,
          ),
          child: GestureDetector(
            behavior: HitTestBehavior.opaque,
            child: Container(
              alignment: Alignment.center,
              child: GestureDetector(
                child: Container(
                  width: width ?? 420,
                  padding: EdgeInsets.only(bottom: 20),
                  decoration: BoxDecoration(
                    color: backgroundColor,
                    borderRadius: BorderRadius.all(
                      Radius.circular(8.0),
                    ),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(8.0),
                            topRight: Radius.circular(8.0),
                          ),
                        ),
                        padding: EdgeInsets.fromLTRB(20, 20, 20, 24),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              title,
                              style: TextStyle(
                                color: Color(0xFF222222),
                                fontSize: 18,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            GestureDetector(
                              child: Icon(
                                Icons.close,
                                size: 18,
                                color: Color(0xFF666666),
                              ),
                              onTap: () {
                                Navigator.pop(context);
                              },
                            ),
                          ],
                        ),
                      ),
                      // scaffold默认高度撑满全部屏幕
                      // Expanded(child: child),
                      Container(
                        child: child,
                        // 必须给高度
                        height: height != null ? height - 66 : 240,
                      )
                    ],
                  ),
                ),
                onTap: () {
                  return;
                },
              ),
            ),
            onTap: () {
              Navigator.pop(context);
            },
          )),
    );
  }

  // 弹出对话框
  static Future<bool> showModalDialog(BuildContext context,
      {String title, Widget child}) {
    return showDialog<bool>(
      context: context,
      builder: (context) {
        return PlatformTools.isPC
            ? UnconstrainedBox(
                constrainedAxis: Axis.vertical,
                child: _getInnerChild(context, title: title, child: child))
            : _getInnerChild(context, title: title, child: child);
      },
    );
  }

  static Widget _getInnerChild(BuildContext context,
      {String title, Widget child}) {
    return DefaultTextStyle(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Container(
            width: PlatformTools.isPC ? 420 : double.infinity,
            margin: EdgeInsets.symmetric(horizontal: 35),
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(10)),
            child: Column(
              crossAxisAlignment: PlatformTools.isPC
                  ? CrossAxisAlignment.center
                  : CrossAxisAlignment.stretch,
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Text(
                  title ?? '',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF222222),
                  ),
                ),
                SizedBox(height: 11.5),
                child ?? SizedBox.shrink(),
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    margin: EdgeInsets.only(top: 12),
                    alignment: Alignment.center,
                    width: 120,
                    height: 40,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(22),
                      gradient: LinearGradient(colors: [
                        Color(0xFFFFE14D),
                        Color(0xFFFFC34D),
                      ]),
                    ),
                    child: Text(
                      '我知道了',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF222222),
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
        ],
      ),
      style: TextStyle(
        fontSize: 12,
        color: Colors.black,
        decoration: TextDecoration.none,
      ),
    );
  }
}
