import 'package:flutter/material.dart';

enum Size { big, middle, small }

class Money extends StatelessWidget {
  Money(
      {@required this.size,
      @required this.text,
      this.alignment = CrossAxisAlignment.center});

  final Size size;
  final String text;
  final CrossAxisAlignment alignment;

  @override
  Widget build(BuildContext context) {
    return FittedBox(
      child: Row(
        crossAxisAlignment: alignment ?? CrossAxisAlignment.center,
        children: <Widget>[
          Text(
            '¥',
            style: TextStyle(
              color: Color(0xFF222222),
              fontWeight: FontWeight.w500,
              fontSize: 12,
            ),
          ),
          Text(
            '$text',
            style: TextStyle(
              color: Color(0xFF222222),
              fontWeight: size == Size.big ? FontWeight.w600 : FontWeight.w500,
              fontSize: size == Size.big ? 18 : 16,
            ),
          )
        ],
      ),
    );
  }
}
