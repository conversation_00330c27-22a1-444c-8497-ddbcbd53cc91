import 'package:flutter/material.dart';
import 'package:waimai_e_fe_ffw_utils/platform_tool.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/gradient.dart';

class ButtonNew extends StatelessWidget {
  const ButtonNew({
    this.key,
    @required this.text,
    this.width,
    @required this.onClick,
    this.disabled = false,
    this.hasColor = false,
  }) : super(key: key);

  final Key key;

  /// 没有宽度时候，默认加padding
  final double width;

  /// 是否有颜色, 默认false
  /// true - PC上是黑色按钮，App上是黄色是渐变
  /// false - 没有颜色就是白色带边框的样式
  final bool hasColor;

  /// 是否是禁止点击状态
  final bool disabled;

  /// 点击事件回调
  final Function onClick;

  /// 按钮文本内容
  final String text;

  @override
  Widget build(BuildContext context) {
    final bool isPC = PlatformTools.isPC;
    double radiusSize = isPC ? 2 : 20;
    Border bgBorder = Border.all(
      color: Color(0xFF999999),
    );
    Color bgColor = Colors.white;
    Color textColor = commonTextColor;
    Gradient bgGradient = null;
    if (disabled) {
      bgColor = disabledBgColor;
      bgGradient = null;
      bgBorder = null;
      textColor = disabledTextColor;
    } else {
      if (hasColor) {
        bgBorder = null;
        if (isPC) {
          bgColor = blackBgColor;
          bgGradient = null;
          textColor = Colors.white;
        } else {
          bgColor = null;
          bgGradient = yellowGradient;
        }
      }
    }
    return GestureDetector(
      onTap: () {
        if (disabled == false && onClick != null) {
          onClick();
        }
      },
      child: Container(
        height: isPC ? 36 : 40,
        width: width ?? null,
        padding: width == null
            ? EdgeInsets.symmetric(
                horizontal: 20,
                vertical: 8,
              )
            : null,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: bgColor,
          gradient: bgGradient,
          borderRadius: BorderRadius.circular(radiusSize),
          border: bgBorder,
        ),
        child: Text(
          text,
          style: TextStyle(
            color: textColor,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
