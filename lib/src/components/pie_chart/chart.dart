// import 'dart:math';

import 'package:flutter/material.dart';

import 'chart_data_model.dart';
import 'params_model.dart';

class Pie<PERSON>hart extends StatefulWidget {
  PieChart({this.params});
  final PieChartParams params;
  @override
  _PieChartState createState() => _PieChartState();
}

class _PieChartState extends State<PieChart> with TickerProviderStateMixin {
  PieChartParams _params;

  @override
  void initState() {
    super.initState();
    _params = widget.params;
  }

  @override
  void didUpdateWidget(covariant PieChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.params.pieData.toString() !=
        oldWidget.params.pieData.toString()) {
      _params = widget.params;
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_params.pieData.every((element) => element.data == 0)) {
      return SizedBox.shrink();
    }
    return Container(
        padding: EdgeInsets.all(widget.params.piePadding),
        child: Container(
          width: widget.params.pieRadius * 2,
          height: widget.params.pieRadius * 2,
          child: RepaintBoundary(
            child: CustomPaint(
              painter: PeiChartPainter(params: _params),
            ),
          ),
        ));
  }
}

class PeiChartPainter extends CustomPainter {
  PeiChartPainter({
    this.params,
  });
  final PieChartParams params;
  double get radius => params.pieRadius;
  final double _pi = 3.1415926535897932;
  List<_PieChartCanvasModel> pieChartCanvasModelList = [];
  void calcPieChartRenderModel() {
    pieChartCanvasModelList.clear();
    double total = 0;
    params.pieData.forEach((element) {
      total += element.data;
    });
    double _startAngle = 1.5 * _pi;
    params.pieData.forEach((element) {
      double _angle = (element.data / total) * _pi * 2;
      pieChartCanvasModelList.add(_PieChartCanvasModel(
          color: element.color ?? ChartDataModel.getRandomColor(),
          startAngle: _startAngle,
          sweepAngle: _angle,
          legend: element?.legend));
      _startAngle += _angle;
    });
  }

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromCenter(
      center: Offset(radius, radius),
      width: radius * 2,
      height: radius * 2,
    );
    final originRect = Rect.fromCenter(
      center: Offset(radius, radius),
      width: radius,
      height: radius,
    );
    Paint paint = Paint();
    paint.style = PaintingStyle.fill;
    calcPieChartRenderModel();
    pieChartCanvasModelList.forEach((element) {
      paint.color = element.color;
      canvas.drawArc(rect, element.startAngle, element.sweepAngle, true, paint);
    });
    paint.color = Colors.white;
    canvas.drawArc(originRect, 0.0, _pi * 2, true, paint);
  }

  @override
  bool shouldRepaint(PeiChartPainter oldDelegate) =>
      params.pieData.toString() != oldDelegate.params.pieData.toString();
}

class _PieChartCanvasModel {
  _PieChartCanvasModel({
    this.color,
    this.startAngle,
    this.sweepAngle,
    this.legend,
  });
  Color color;

  String legend;

  /// 开始角
  double startAngle;

  /// 结束角
  double sweepAngle;
}
