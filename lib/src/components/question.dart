import 'package:flutter/material.dart';

class Question extends StatelessWidget {
  Question({this.onTap, this.onEnter, this.onExit, this.isDark});
  // Native：点击事件回调
  final Function onTap;

  /// Web：onEnter 事件回调
  final Function onEnter;

  /// Web：exit 事件回调
  final Function onExit;

  /// 深色的问号
  final bool isDark;

  @override
  Widget build(BuildContext context) {
    String prex =
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager';
    return MouseRegion(
      cursor: SystemMouseCursors.click,
      onEnter: (event) {
        if (this.onEnter != null) {
          this.onEnter();
        }
      },
      onExit: (event) {
        if (this.onExit != null) {
          this.onExit();
        }
      },
      child: GestureDetector(
        child: Image.network(
          isDark == true
              ? '$prex/16c344dc69d4f36d61ad2458443d508b/help_outline.png'
              : '$prex/8bf980fc1036bba7d8d076274c604210/question.png',
          width: 14,
          height: 14,
        ),
        onTap: () {
          if (this.onTap != null) {
            this.onTap();
          }
        },
      ),
    );
  }
}
