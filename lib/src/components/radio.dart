import 'package:flutter/material.dart';

class CustomRadio extends StatelessWidget {
  CustomRadio({@required this.checked});
  final bool checked;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 16,
      height: 16,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: checked ? Color(0xFFFFCC33) : Colors.white,
        border: checked
            ? Border()
            : Border.all(
                width: 1,
                color: Color(0xFF666666),
              ),
      ),
      child: checked
          ? Image.network(
              'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/4c47935f8737bd11549de9836c2dcfdf/selected.png',
              width: 16,
              height: 16,
            )
          : null,
    );
  }
}
