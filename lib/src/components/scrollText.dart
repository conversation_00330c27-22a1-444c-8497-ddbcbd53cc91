import 'dart:async';

import 'package:flutter/material.dart';

class ScrollText extends StatefulWidget {
  ScrollText({@required this.child});
  final Widget child;

  @override
  ScrollTextState createState() => ScrollTextState();
}

class ScrollTextState extends State<ScrollText> {
  ScrollController _controller;
  double _offset = 0.0;
  Timer timer;

  @override
  void initState() {
    super.initState();
    const duration = Duration(milliseconds: 500);
    _controller = ScrollController(initialScrollOffset: _offset);
    timer = Timer.periodic(duration, (timer) {
      if (_controller == null) return;
      double newOffset = _controller.offset + 10;
      if (newOffset != _offset) {
        _offset = newOffset;
        _controller.animateTo(
          _offset,
          duration: duration,
          curve: Curves.linear,
        );
      }
    });
  }

  @override
  void dispose() {
    timer?.cancel();
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 16,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        controller: _controller,
        itemBuilder: (context, index) {
          return widget.child;
        },
      ),
    );
  }
}
