import 'package:flutter/material.dart';
import 'package:waimai_e_fe_ffw_utils/platform_tool.dart';

typedef SearchById<String> = void Function(String);

class SearchBar extends StatefulWidget {
  const SearchBar({this.onchangeValue, this.onEditingComplete});
  final ValueChanged<String> onchangeValue;
  final VoidCallback onEditingComplete;

  @override
  SearchBarState createState() => SearchBarState();
}

class SearchBarState extends State<SearchBar> {
  ///编辑控制器
  final TextEditingController _controller = TextEditingController();
  final bool isPC = PlatformTools.isPC;
  final InputBorder pcBorder = OutlineInputBorder(
    borderSide: BorderSide(
      color: Color(0xFFCCCCCC),
      width: 1.0,
      style: BorderStyle.solid,
    ),
    borderRadius: BorderRadius.circular(2),
  );
  final InputBorder activepcBorder = OutlineInputBorder(
    borderSide: BorderSide(
      color: Color(0xFF999999),
      width: 1.0,
      style: BorderStyle.solid,
    ),
    borderRadius: BorderRadius.circular(2),
  );

  ///是否显示删除按钮
  bool _hasDeleteIcon = false;

  /// 输入框的值
  String value;

  // 搜索框输入
  Widget _buildTextField() {
    return TextField(
      controller: _controller,
      textInputAction: TextInputAction.unspecified,
      keyboardType: TextInputType.text,
      // 限制只能输入数字（包括负号-）
      // inputFormatters: <TextInputFormatter>[
      //   WhitelistingTextInputFormatter(RegExp(r'(^\-?\d*)$')),
      // ],
      autofocus: true,
      maxLines: 1,
      decoration: InputDecoration(
        //输入框decoration属性
        contentPadding: isPC
            ? const EdgeInsets.symmetric(vertical: 8.0, horizontal: 12.0)
            : const EdgeInsets.symmetric(vertical: 10.0, horizontal: 1.0),
        //设置搜索图片
        prefixIcon: !isPC
            ? Padding(
                padding:
                    EdgeInsets.only(left: 0.0, top: 6, right: 6, bottom: 6),
                child: Image.network(
                  'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/97ef0f33e2513071808a0138bfdd5b33/search.png',
                ),
              )
            : null,
        prefixIconConstraints: !isPC
            ? BoxConstraints(
                minWidth: 16,
                minHeight: 16,
              )
            : null,
        enabledBorder: isPC ? pcBorder : InputBorder.none,
        focusedBorder: isPC ? activepcBorder : InputBorder.none,
        hintText: " 请输入订单展示ID",
        hintStyle: TextStyle(
            fontSize: 14, color: isPC ? Color(0xFF999999) : Color(0xFF666666)),
        //设置清除按钮
        suffixIcon: Container(
          padding: EdgeInsetsDirectional.only(
            start: 2.0,
            end: _hasDeleteIcon ? 0.0 : 0,
          ),
          child: _hasDeleteIcon
              ? InkWell(
                  onTap: (() {
                    setState(() {
                      // 清空输入
                      _controller?.clear();
                      _hasDeleteIcon = false;
                    });
                  }),
                  child: Icon(
                    Icons.cancel,
                    size: 16.0,
                    color: Colors.grey,
                  ),
                )
              : SizedBox.shrink(),
        ),
      ),
      onChanged: (String curValue) {
        if (curValue != "") {
          setState(() {
            value = curValue;
            _hasDeleteIcon = true;
          });
        }
      },
      onSubmitted: (String curValue) {
        _search(curValue);
      },
      style: TextStyle(fontSize: 14, color: Colors.black),
    );
  }

  void _search(String curValue) {
    setState(() {
      _hasDeleteIcon = curValue != null && !curValue.isEmpty;
      value = curValue;
      if (_hasDeleteIcon) {
        widget?.onchangeValue(_controller?.text ?? '');
      }
      // 收起键盘
      FocusScope.of(context).unfocus();
    });
  }

  @override
  Widget build(BuildContext context) {
    final EdgeInsets pcPadding = EdgeInsets.all(30);
    final EdgeInsets appPadding = EdgeInsets.fromLTRB(12, 0, 12, 12);
    Widget searchButton = Text(
      '查询',
      style: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        color: isPC ? Colors.white : Color(0xFF222222),
      ),
    );
    return Container(
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(
            isPC ? 8 : 0,
          ))),
      padding: isPC ? pcPadding : appPadding,
      child: Row(
        children: <Widget>[
          isPC
              ? Row(
                  children: [
                    Padding(
                      padding: EdgeInsets.only(right: 14),
                      child: Text(
                        '订单查询',
                        style: TextStyle(
                            fontSize: 18, fontWeight: FontWeight.w500),
                      ),
                    ),
                    Container(
                      child: _buildTextField(),
                      width: 300,
                      height: 36,
                    ),
                  ],
                )
              : Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Color(0xFFF5F6FA),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    height: 32,
                    padding: EdgeInsets.only(left: 10.0),
                    child: _buildTextField(),
                  ),
                ),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              _search(this.value);
            },
            child: isPC
                ? Container(
                    margin: EdgeInsets.only(
                      left: 16,
                    ),
                    width: 60,
                    height: 36,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(2)),
                      color: Colors.black,
                    ),
                    child: searchButton,
                  )
                : Container(
                    padding: EdgeInsets.only(left: 12, right: 2),
                    child: searchButton,
                  ),
          )
        ],
      ),
    );
  }
}
