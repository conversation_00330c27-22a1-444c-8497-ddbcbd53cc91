import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/uiTools.dart';

class SelectBottomDialog {
  SelectBottomDialog({
    this.items,
    this.onClickItem,
  });

  /// 可选项
  List<SelectItemProvider> items;

  /// 是否正在展示
  bool _isShow = false;

  bool get isShow => _isShow;

  /// 选项默认高60
  double itemHeight = 60.0;

  /// 可选项点击回调
  ItemClickCallback onClickItem;

  /// 显示Dialog
  void show(BuildContext context) {
    _isShow = true;

    showBottomPop(context);
  }

  void dismiss(BuildContext context) {
    if (!_isShow) {
      return;
    }

    Navigator.pop(context);
    _isShow = false;
  }

  showBottomPop(BuildContext context) {
    return showModalBottomSheet(
        context: context,
        isScrollControlled: true, //可滚动 解除showModalBottomSheet最大显示屏幕一半的限制
        shape: RoundedRectangleBorder(
          //圆角
          borderRadius: BorderRadius.vertical(top: Radius.circular(30)),
        ),
        builder: (BuildContext context) {
          return Container(
              child: Column(mainAxisSize: MainAxisSize.min, children: <Widget>[
            Padding(
                padding: EdgeInsets.fromLTRB(10, 0, 0, 0),
                child: Stack(alignment: Alignment.center, children: <Widget>[
                  Container(
                    child: Text("交易类型",
                        style: TextStyle(
                            fontSize: 18,
                            color: Color(0xff333333),
                            fontWeight: FontWeight.w500)),
                  ),
                  Align(
                    alignment: Alignment.topRight,
                    child: Container(
                        width: 60,
                        height: 50,
                        child: IconButton(
                            icon: Icon(
                              Icons.close,
                              size: 22,
                            ),
                            onPressed: () {
                              Navigator.pop(context);
                            })),
                  )
                ])),
            Divider(
              height: 1,
              color: Colors.black45,
            ),
            AnimatedPadding(
              padding: MediaQuery.of(context).viewInsets, //边距（必要）
              duration: const Duration(milliseconds: 100), //时长 （必要）
              child: Container(
                constraints: BoxConstraints(
                  minHeight: 90, //设置最小高度（必要）
                  maxHeight:
                      UITools.getScreenHeight(context) / 1.5, //设置最大高度（必要）
                ),
                child: _createTiles(context),
              ),
            ),
          ]));
        });
  }

  /// 创建选项列表项
  ListView _createTiles(BuildContext context) {
    int _row = this.items.length;
    List<ListTile> tiles = [];
    for (int i = 0; i < _row; i++) {
      SelectItemFinance item = items[i];
      tiles.add(ListTile(
        title: Text(item.itemTitle),
        trailing: item.isItemSelected ? Icon(Icons.check) : SizedBox.shrink(),
        onTap: () {
          if (this.onClickItem != null) {
            this.onClickItem(item);
          }
          this.dismiss(context);
        },
        selected: item.isItemSelected,
      ));
    }
    Iterable<Widget> iter =
        ListTile.divideTiles(context: context, tiles: tiles);
    return ListView(
      shrinkWrap: true, //防止状态溢出 自适应大小
      children: iter?.toList() ?? [],
    );
  }
}

abstract class SelectItemProvider {
  String get id;

  String get itemTitle;

  bool get isItemSelected; // 是否被选中，true-选中，false-未选中
}

class SelectItemFinance extends SelectItemProvider {
  SelectItemFinance({this.id, this.title, this.isSelected});

  /// 选项唯一值
  String id;

  /// 选项文案
  String title;

  /// 是否被选中
  int isSelected;

  // @override
  String get itemId => id;

  @override
  String get itemTitle => title;

  @override
  bool get isItemSelected => isSelected == 1 ? true : false;
}

/// menu 点击事件回调
typedef ItemClickCallback = Function(SelectItemProvider item);

class _SelectItemListViewWidget extends StatefulWidget {
  _SelectItemListViewWidget({this.item, this.clickCallback});

  final SelectItemProvider item;

  final Function(SelectItemProvider item) clickCallback;

  @override
  State<StatefulWidget> createState() {
    return _SelectItemListViewState();
  }
}

class _SelectItemListViewState extends State<_SelectItemListViewWidget> {
  var highlightColor;
  var color;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: Text(widget.item.itemTitle),
      trailing:
          widget.item.isItemSelected ? Icon(Icons.check) : SizedBox.shrink(),
      onTap: () {
        if (widget.clickCallback != null) {
          widget.clickCallback(widget.item);
        }
      },
      selected: widget.item.isItemSelected,
    );
  }
}
