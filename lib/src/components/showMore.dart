import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/arrow.dart';

class ShowMoreWidget extends StatefulWidget {
  /// 是否折叠
  final bool fold;

  final Function cb;

  final bool isFromOrderDetail;

  ShowMoreWidget(
      {@required this.fold, @required this.cb, this.isFromOrderDetail});
  @override
  ShowMoreWidgetState createState() => ShowMoreWidgetState();
}

class ShowMoreWidgetState extends State<ShowMoreWidget> {
  bool fold = true;

  @override
  void initState() {
    super.initState();
    fold = widget.fold;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        widget?.cb();
        setState(() {
          fold = !fold;
        });
      },
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        child: Container(
          padding: EdgeInsets.fromLTRB(12, 12, 12, 0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Text(
                fold == true
                    ? '${widget.isFromOrderDetail == true ? '展开商品信息' : '展开更多'}'
                    : '收起',
                style: TextStyle(
                  color: Color(0xFF666666),
                  fontWeight: FontWeight.w500,
                  fontSize: 12,
                ),
              ),
              ArrowIcon(
                direction: fold == true ? DirectionEnum.down : DirectionEnum.up,
              )
            ],
          ),
        ),
      ),
    );
  }
}
