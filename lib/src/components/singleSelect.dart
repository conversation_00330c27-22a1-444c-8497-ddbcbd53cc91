import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/gradient.dart';

typedef void SingleSelectCallback(SingleOption option);

class SingleOption {
  SingleOption({this.id, this.text});
  int id;
  String text;
}

/// 单选组件
class SingleSelect extends StatefulWidget {
  SingleSelect({this.options, this.cb});
  final List<SingleOption> options;
  final SingleSelectCallback cb;

  @override
  SingleSelectState createState() => SingleSelectState();
}

class SingleSelectState extends State<SingleSelect> {
  int selectedId = 0;

  // 单一Option的widget
  Widget optionWidget(SingleOption option) {
    Color color = Color(0xFFF5F6FA);
    bool selected = (option?.id ?? 0) == selectedId;
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedId = option.id ?? 0;
        });
        widget?.cb(option);
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        margin: EdgeInsets.only(right: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          gradient: selected ? yellowGradient : null,
          color: selected ? null : color,
        ),
        child: Center(
          child: Text(
            '${option.text}',
            style: TextStyle(
              color: Color(0xFF222222),
              fontWeight: selected ? FontWeight.w500 : FontWeight.w400,
              fontSize: 12,
            ),
          ),
        ),
      ),
    );
  }

  // 可选项列表
  optionListWidget(List<SingleOption> monthList) {
    List<Widget> widgetList = [];

    // 遍历可选项
    monthList.forEach((options) {
      widgetList.add(optionWidget(options));
    });
    return Container(
      padding: EdgeInsets.fromLTRB(0, 12, 4, 0),
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 展示可选项
          Row(
            children: widgetList,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return optionListWidget(widget.options);
  }
}
