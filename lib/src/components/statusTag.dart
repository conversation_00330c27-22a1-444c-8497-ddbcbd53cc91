import 'package:flutter/material.dart';

enum StatusTagTypeEnum {
  /// 进行中
  ongoing,

  /// 失败
  fail,
}

class StatusTag extends StatelessWidget {
  StatusTag({@required this.text, this.type});

  final StatusTagTypeEnum type;
  final String text;

  final Color ongoingBgColor = Color(0xFFEEF6FF);
  final Color failBgColor = Color(0xFFFFF2F3);

  final Color ongoingTxtColor = Color(0xFF198CFF);
  final Color failTxtColor = Color(0xFFFF192D);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(4, 2, 4, 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(1.5),
        color: type == StatusTagTypeEnum.ongoing ? ongoingBgColor : failBgColor,
      ),
      child: Center(
        child: Text(
          '$text',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 12,
            color: type == StatusTagTypeEnum.ongoing
                ? ongoingTxtColor
                : failTxtColor,
          ),
        ),
      ),
    );
  }
}
