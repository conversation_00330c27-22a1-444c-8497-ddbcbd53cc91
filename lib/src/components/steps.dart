import 'package:flutter/material.dart';
import 'package:waimai_e_fe_ffw_utils/platform_tool.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/withdrawDetail.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';

// 类型：成功，处理中，失败
enum CustomStepTypeEnum {
  success,
  processing,
  fail,
}

CustomStepTypeEnum _getFlowType(int status) {
  switch (status) {
    case 0:
    case 1:
    case 2:
      return CustomStepTypeEnum.success;
    case 3:
      return CustomStepTypeEnum.fail;
    case -1:
    default:
      return CustomStepTypeEnum.processing;
  }
}

class CustomStep extends StatelessWidget with ExportDetailMixin {
  CustomStep({@required this.index, this.withdrawDetail});

  ///
  final WithdrawDetailModel withdrawDetail;

  /// 索引
  final int index;

  /// 是否最后一个
  // final bool isLast;

  // 判断是否是 PC 环境
  final bool _isPc = PlatformTools.isPC;

  String _getTypeIconUrl() {
    String imageUrl;
    WithdrawFlows flow = (withdrawDetail?.withdrawFlows ?? [])[index];
    CustomStepTypeEnum type = _getFlowType(flow?.withdrawStatus);

    switch (type) {
      case CustomStepTypeEnum.success:
        imageUrl =
            'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/07412e5fd9bf74a494819959929c3548/done.png';
        break;
      case CustomStepTypeEnum.fail:
        imageUrl =
            'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/858a4e05e7657217f23166e666ba2b70/fail.png';
        break;
      default:
        imageUrl = '';
    }
    return imageUrl;
  }

  Widget _getIcon() {
    String imageUrl = _getTypeIconUrl();
    // 正常索引 ICON
    if (imageUrl == '') {
      return Container(
          width: 18,
          height: 18,
          margin: EdgeInsets.all(3),
          decoration: BoxDecoration(
              color: Color(0xFFCCCCCC),
              borderRadius: BorderRadius.circular(12)),
          child: Center(
              child: Text(
            '${index}',
            style: TextStyle(color: Color(0xFFFFFFFF), fontSize: 12),
          )));
    } else {
      return Image(
          width: 24, height: 24, image: NetworkImage(_getTypeIconUrl()));
    }
  }

  // 若为成功转入钱包，则添加跳转钱包的逻辑
  Widget _getWallet() {
    Widget wallet = SizedBox.shrink();
    WithdrawFlows flow = (withdrawDetail?.withdrawFlows ?? [])[index];
    // 若为成功转入钱包，则添加跳转钱包的逻辑
    if (this.withdrawDetail?.payBindType == 2 && flow?.withdrawStatus == 2) {
      wallet = GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          // 跳转钱包流水
          ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_t863y3hp_mc');
          RouterTools.openWebPageUrl(this.withdrawDetail?.walletUrl ?? '');
        },
        child: MouseRegion(
          cursor: SystemMouseCursors.click,
          child: Text(
            '钱包',
            style: TextStyle(
                color: Color(0xFFFF6A00),
                fontSize: 16,
                fontWeight: FontWeight.w400),
          ),
        ),
      );
    }
    return wallet;
  }

  Widget _getTitle() {
    WithdrawFlows flow = (withdrawDetail?.withdrawFlows ?? [])[index];
    return Row(children: <Widget>[
      _getIcon(),
      SizedBox(width: 8),
      Text(
        '${flow?.displayMsg}',
        style: TextStyle(
            fontSize: 16,
            color: Color(0xFF222222),
            fontWeight: FontWeight.w400),
      ),
      _getWallet(),
    ]);
  }

  Widget _getTitlePC() {
    WithdrawFlows flow = (withdrawDetail?.withdrawFlows ?? [])[index];
    return Row(children: <Widget>[
      Text(
        '${flow?.displayMsg}',
        style: TextStyle(
            fontSize: 16,
            color: Color(0xFF222222),
            fontWeight: FontWeight.w400),
      ),
      _getWallet(),
    ]);
  }

  Widget _getDescription() {
    bool isLast = index == (withdrawDetail?.withdrawFlows ?? []).length - 1;
    return Container(
      constraints: BoxConstraints(minHeight: isLast ? 12 : 47),
      margin: EdgeInsets.only(left: 12, top: 4),
      padding: EdgeInsets.only(left: 20, bottom: 12),
      decoration: BoxDecoration(
          border: isLast
              ? null
              : Border(left: BorderSide(width: 1, color: Color(0xFFEEEEEE)))),
      child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            _buildDescription(),
            _buildErrorHintInfo(),
          ]),
    );
  }

  Widget _buildDescription() {
    WithdrawFlows flow = (withdrawDetail?.withdrawFlows ?? [])[index];
    String desc = DateFormat.formatFull(flow?.withdrawTimestamp ?? 0);
    if (flow?.withdrawStatus == 3) {
      desc = '此笔提现金额已退回到账户余额中 ' + desc;
    }
    return Text('$desc',
        style: TextStyle(
          fontSize: 12,
          color: Color(0xFF666666),
        ));
  }

  Widget _buildErrorHintInfo() {
    // withdrawStatus == 3 表示失败
    WithdrawFlows flow = (withdrawDetail?.withdrawFlows ?? [])[index];
    Widget hintInfo = SizedBox.shrink();
    if (flow?.withdrawStatus == 3) {
      // 若失败，则提示信息
      TextStyle textStyle = TextStyle(fontSize: 12, color: Color(0xFFFF192D));
      hintInfo = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text('收款账户信息错误，请联系业务经理协助处理', style: textStyle),
          Text('你的业务经理： ${this.withdrawDetail?.mobile ?? ""}',
              style: textStyle),
        ],
      );
    }
    return hintInfo;
  }

  Widget _getDescriptionPC() {
    List<WithdrawFlows> flows = withdrawDetail?.withdrawFlows ?? [];
    bool isLast = index == flows.length - 1;
    return Container(
      constraints: BoxConstraints(minWidth: 60),
      margin: EdgeInsets.only(top: 12),
      padding: EdgeInsets.only(left: 6, top: 12, right: 12),
      decoration: BoxDecoration(
          border: Border(
              top: BorderSide(
                  width: 1,
                  color: isLast ? Color(0xFFFFFFFF) : Color(0xFFEEEEEE)))),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          _getTitlePC(),
          SizedBox(
            height: 8,
          ),
          _buildDescription(),
          _buildErrorHintInfo(),
          // flow?.withdrawStatus == 3
          //     ? Text('收款账户信息错误，请联系业务经理协助处理', style: errorTextStyle)
          //     : SizedBox.shrink(),
          // flow?.withdrawStatus == 3
          //     ? Text('你的业务经理： ${this.withdrawDetail?.mobile ?? ""}',
          //         style: errorTextStyle)
          //     : SizedBox.shrink(),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: _isPc
          ? Stack(
              children: <Widget>[
                // 线条 + Title + 描述
                _getDescriptionPC(),
                // ICON
                _getIcon(),
              ],
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                // ICON + Title
                _getTitle(),
                // 线条 + 描述
                _getDescription(),
              ],
            ),
    );
  }
}
