import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/waimai_e_fe_flutter_finance.dart';

enum TypeEnum {
  /// 填充按钮
  full,

  /// 镂空按钮
  empty,
}

class Tag extends StatelessWidget {
  Tag({@required this.text, this.type});

  final TypeEnum type;
  final String text;

  final Color fullBgColor = Color(0xFFFF6A00);
  final Color emptyBgColor = Colors.transparent;

  final Color fullTxtColor = Color(0xFFFFFFFF);
  final Color emptyTxtColor = Color(0xFFFF192D);

  final Color borderColor = Color(0x80FF6A00);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(4, 2, 4, 2),
      decoration: BoxDecoration(
        border: Border.all(
          width: 0.5,
          color: borderColor,
        ),
        borderRadius: BorderRadius.circular(1.5),
        color: type == TypeEnum.full ? fullBgColor : emptyBgColor,
      ),
      child: Center(
        child: Text(
          '$text',
          textAlign: TextAlign.center,
          strutStyle: PlatformTool.isWeb
              ? null
              : StrutStyle(
                  forceStrutHeight: true,
                  height: 1,
                ),
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: 12,
            color: type == TypeEnum.full ? fullTxtColor : emptyTxtColor,
          ),
        ),
      ),
    );
  }
}
