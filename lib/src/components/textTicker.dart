import 'dart:async';

import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';

class TextTicker extends StatefulWidget {
  TextTicker({@required this.child});
  final List<TipList> child;

  @override
  TextTickerState createState() => TextTickerState();
}

class TextTickerState extends State<TextTicker> {
  ScrollController _controller;
  double _offset = 0.0;
  Timer timer;

  @override
  void initState() {
    super.initState();
    const duration = Duration(milliseconds: 500);
    _controller = ScrollController(initialScrollOffset: _offset);
    Future.delayed(Duration(milliseconds: 500), () {
      timer = Timer.periodic(duration, (timer) {
        if (_controller == null) return;
        if (_controller.hasClients) {
          double newOffset = _controller.offset + 10;
          if (newOffset != _offset) {
            _offset = newOffset;
            _controller.animateTo(
              _offset,
              duration: duration,
              curve: Curves.linear,
            );
          }
        }
      });
    });
  }

  @override
  void dispose() {
    timer?.cancel();
    _controller?.dispose();
    super.dispose();
  }

  Widget build(BuildContext context) {
    return Container(
      height: 16,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        controller: _controller,
        itemBuilder: (context, index) {
          return Text(
              widget.child[index % widget.child.length].tipContent + '     ',
              style: TextStyle(
                color: Color(0xFF222222),
                fontWeight: FontWeight.w400,
                fontSize: 12,
              ));
        },
      ),
    );
  }
}
