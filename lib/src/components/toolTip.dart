import 'dart:math';

import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/uiTools.dart';

class ToolTipNew extends StatelessWidget {
  const ToolTipNew({
    this.key,
    @required this.target,
    @required this.tip,
    this.width,
    this.height,
    this.onClick,
    this.lineWordCount = 30,
  }) : super(key: key);

  final Key key;
  final Widget target;
  final String tip;
  final double width;
  final double height;
  final Function onClick;

  /// 每一行字符数量
  final int lineWordCount;

  String generateMessage(String str) {
    String newLabel = '';
    int totalLen = str.length;
    if (totalLen <= lineWordCount) {
      newLabel = str;
    } else {
      int lines = totalLen ~/ lineWordCount;
      for (int i = 0; i <= lines; i++) {
        newLabel += (str.substring(
                lineWordCount * i, min(lineWordCount * (i + 1), totalLen)) +
            ((i == lines) ? '' : '\n'));
      }
    }
    return newLabel;
  }

  @override
  Widget build(BuildContext context) {
    return UITools.isPc
        ? Material(
            child: Container(
              color: Colors.white,
              width: width ?? 100,
              height: height ?? 36,
              child: Center(
                child: Tooltip(
                  verticalOffset: 10,
                  message: generateMessage(tip),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                      color: Color(0xFF3f4156),
                      borderRadius: BorderRadius.circular(2)),
                  textStyle: TextStyle(
                    decoration: TextDecoration.none,
                    fontSize: 14,
                    color: Colors.white,
                  ),
                  child: target,
                ),
              ),
            ),
          )
        : GestureDetector(
            child: target,
            onTap: onClick ?? () {},
          );
  }
}
