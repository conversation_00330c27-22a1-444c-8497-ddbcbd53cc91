import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:mt_flutter_web_utils/mt_flutter_web_utils.dart';
import 'package:waimai_e_fe_flutter_finance/src/common/flap.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/customWillPopScope.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/modal.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/newButton.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/accountDetails.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/accountDetails.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/uiTools.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/util/index.dart';
import 'package:waimai_e_fe_flutter_finance/waimai_e_fe_flutter_finance.dart';
import 'package:waimai_e_native_business/waimai_e_native_business.dart';

@Flap('finance')
class AccountDetailsPage extends StatelessWidget {
  const AccountDetailsPage({this.params, this.pageName});
  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  Widget build(BuildContext context) {
    return FlutterEasyLoading(
      child: AccountDetails(
        pageName: pageName,
        params: params,
      ),
    );
  }
}

class AccountDetails extends StatefulWidget {
  AccountDetails({String pageName, this.params})
      : this.pageName =
            pageName ?? (params != null ? params['mtf_page'].toString() : '');
  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  _AccountDetailsState createState() => _AccountDetailsState();
}

class _AccountDetailsState extends State<AccountDetails>
    with AccountDetailsMixin {
  final TapGestureRecognizer recognizer = TapGestureRecognizer();

  final TextStyle leftTextStyle = TextStyle(
    color: Color(0xFF999999),
    fontSize: 14,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.2,
  );
  final TextStyle rightTextStyle = TextStyle(
    color: Color(0xFF222222),
    fontSize: 14,
    fontWeight: FontWeight.w400,
  );
  // 账户结算详情
  AccountInfoModal accountInfo;

  final bool isPC = PlatformTools.isPC;
  String wmPoiId;

  /// 是否是校园账号
  bool isSchoolAccount = false;

  String schoolId;

  get text => null;

  get margin => null;

  get tip => null;

  _fetchData() {
    EasyLoading.show(status: '加载中');
    // 根据账户类型选择不同的 API
    final Future<AccountInfoModal> apiCall = isSchoolAccount
        ? fetchSchoolAccountDetails({'schoolId': schoolId})
        : fetchAccountDetails();

    apiCall
        .then((AccountInfoModal response) {
          setState(() {
            accountInfo = response;
          });
        })
        .catchError((error) {})
        .whenComplete(() {
          EasyLoading.dismiss();
        });
  }

  /// 详情item
  Widget _buildDetail({
    String label,
    String msg,
    String value,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: 8.0,
      ),
      child: Flex(
        direction: Axis.horizontal,
        children: <Widget>[
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  '$label',
                  style: leftTextStyle,
                  // textAlign:
                  //     isPC ? TextAlign.left : TextAlign.right,
                ),
                msg != null
                    ? Text(
                        '$msg',
                        style: TextStyle(
                          color: Color(0xFF999999),
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                        ),
                      )
                    : SizedBox.shrink()
              ],
            ),
          ),
          Expanded(
            flex: isPC ? 3 : 1,
            child: Text(
              value,
              textAlign: isPC ? TextAlign.left : TextAlign.right,
              style: rightTextStyle,
            ),
          )
        ],
      ),
    );
  }

  // 标题 Row
  Widget _buildTitle({String title, int action = 0}) {
    if (title.isEmpty) {
      return SizedBox.shrink();
    }
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8.0),
      margin: EdgeInsets.only(top: action != 0 ? 16.0 : 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Text(
            '$title',
            style: TextStyle(
              color: Color(0xFF222222),
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          action != 0 && !isPC ? _buildChangesPrompt() : SizedBox.shrink(),
        ],
      ),
    );
  }

  // 变更账户银行信息
  Widget _buildChangesPrompt() {
    ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_cit4tzii_mv');
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        Modal.showModalDialog(
          context,
          title: "变更账户银行信息",
          child: Container(
            // width: 200,
            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: DefaultTextStyle(
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF666666),
                fontWeight: FontWeight.w400,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text('1、联系业务经理，递交申请材料并签订变更合同，申请材料包含：身份证复印件、银行卡复印件'),
                  Text('2、业务经理在系统提交变更材料，你将收到系统发的审核提示短信'),
                  Text('3、审核通过后，你将收到系统发的银行账户信息变更成功提示短信'),
                ],
              ),
            ),
          ),
        );
        ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_cit4tzii_mc');
      },
      child: MouseRegion(
        cursor: SystemMouseCursors.click,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Text(
              '变更账户银行信息',
              strutStyle: PlatformTool.isWeb
                  ? null
                  : StrutStyle(
                      forceStrutHeight: true,
                      height: 1,
                    ),
              style: TextStyle(
                fontSize: 12,
                color: Color(0xFF999999),
                fontWeight: FontWeight.w400,
              ),
            ),
            Container(
              margin: EdgeInsets.only(left: 2, top: PlatformTools.isPC ? 2 : 0),
              child: Image.network(
                "https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/16c344dc69d4f36d61ad2458443d508b/help_outline.png",
                height: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 查看钱包流水
  Widget _buildJumpWallet() {
    ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_lnthtyoy_mv');
    return GestureDetector(
      onTap: () {
        ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_lnthtyoy_mc');

        if (isPC) {
          // PC下利用壳子跳转，返回时路由栈丢失，通过刷新页面解决
          MTFlutterWebUtils.bridgeJump(accountInfo?.walletFowUrl);
        } else {
          RouterTools.openWebPageUrl(accountInfo?.walletFowUrl);
        }
      },
      child: Container(
        color: Colors.transparent,
        padding: EdgeInsets.symmetric(vertical: 8.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            MouseRegion(
              cursor: SystemMouseCursors.click,
              child: Text(
                '查看钱包流水',
                style: isPC
                    ? TextStyle(
                        color: Color(0xFFFF6A00),
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        letterSpacing: 0.2,
                      )
                    : leftTextStyle,
              ),
            ),
            isPC
                ? SizedBox.shrink()
                : Image.network(
                    'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/27aa028dad636936701ddc3f46bf9c6f/arrow_right_grey.png',
                    height: 12,
                  )
          ],
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    isSchoolAccount =
        (widget.params != null && widget.params['isSchoolAccount'] == '1');
    schoolId = widget.params != null ? widget.params['schoolId'] : '';
    _fetchData();
    Util.getPoiId().then((value) {
      wmPoiId = value;
    });
    ReportLX.pv(pageKeyInfo, cid);
  }

  _addAccountMessage(key) {
    Map<String, dynamic> param = {
      'acctId': Util.getUrlParam('acctId') ?? Util.getCookie('acctId'),
      'BSID': Util.getUrlParam('token') ?? Util.getCookie('token'),
      'source': 16,
      'page': 'daojiaSelfSettleEdit',
      'type': 'PC',
      'verifyStrategy': 'waimaiSelfSettleEdit',
      'entryFlowId': key == 'edit' ? accountInfo?.settleSettingId : '',
      'iphPayMerchantNo':
          Util.getHost().indexOf('test') >= 0 ? ************** : **************,
      'bizType': 12001,
      'poiList': accountInfo?.accType == 200
          ? Util.getUrlParam('wmPoiId') ?? Util.getCookie('wmPoiId').toString()
          : '',
      'poi': Util.getUrlParam('wmPoiId') ?? Util.getCookie('wmPoiId'),
      'cbType': 5,
      'sourceName': 'waimai'
    };
    List<String> list = [];
    param.forEach((key, value) {
      list.add('$key=$value');
    });
    String paramStr = list.join('&');
    ReportLX.mc(pageKeyInfo, 'c_waimai_e_n2ptf4p1',
        key == 'edit' ? 'b_waimai_e_1z7lx4lo_mc' : 'b_waimai_e_uyc0td0d_mc');
    MTFlutterWebUtils.bridgeJump(
        '${Util.getHost()}/merchant/front/common/merchant-entry-login-verify?$paramStr');
  }

  _addAccountMessageApp(key) async {
    final envInfo = await WaimaiENativeBusiness.getEnvironmentInfo();
    final userInfo = await WaimaiENativeBusiness.getUserInfo();

    String apiEnv = "RELEASE";
    String host = "https://paymp.meituan.com";
    if (envInfo != null) {
      if (envInfo["hostType"] == "TEST" || envInfo["hostType"] == "QA") {
        apiEnv = "TEST";
        host = "http://paymp.pay.test.sankuai.com";
      }
      if (envInfo["hostType"] == "STAGE") {
        apiEnv = "STAGE";
        host = "https://paymp.pay.st.sankuai.com";
      }
    }

    Map<String, dynamic> param = {
      'acctId': userInfo?.acctId ?? "",
      'BSID': userInfo?.accessToken ?? "",
      'source': 16,
      'page': 'daojiaSelfSettleEdit',
      'type': 'i',
      'verifyStrategy': 'waimaiSelfSettleEdit',
      'entryFlowId': key == 'edit' ? accountInfo?.settleSettingId : '',
      'iphPayMerchantNo': apiEnv == "TEST" ? ************** : **************,
      'bizType': 12001,
      'poiList': accountInfo?.accType == 200 ? widget.params["wmPoiId"] : '',
      'poiId': widget.params["wmPoiId"],
      'poi': widget.params["wmPoiId"],
      'cbType': 1,
      'sourceName': 'waimai'
    };
    List<String> list = [];
    param.forEach((key, value) {
      list.add('$key=$value');
    });
    String paramStr = list.join('&');
    String finalUrl =
        '${host}/merchant/front/common/merchant-entry-login-verify?$paramStr';
    RouteUtils.open(finalUrl);
  }

  _getOperationType() {
    if (accountInfo?.showSettleSettingAddButton == true &&
        accountInfo?.showSettleSettingButton == true) {
      return "新增/修改";
    } else if (accountInfo?.showSettleSettingAddButton == true) {
      return "新增";
    } else if (accountInfo?.showSettleSettingButton == true) {
      return "修改";
    } else {
      return "";
    }
  }

  Widget _buildButton() {
    bool hasButton = accountInfo?.showSettleSettingAddButton == true ||
        accountInfo?.showSettleSettingButton == true;
    return Container(
      margin: hasButton
          ? EdgeInsets.only(
              top: 30,
              bottom: 10,
            )
          : EdgeInsets.all(0),
      child: Row(children: <Widget>[
        accountInfo?.showSettleSettingAddButton == true
            ? Tooltip(
                padding: EdgeInsets.only(
                  top: 10,
                  bottom: 10,
                  right: 110,
                  left: 10,
                ),
                child: ButtonNew(
                  text: '新增结算信息',
                  width: 118,
                  onClick: () => _addAccountMessage('add'),
                ),
                message:
                    '1、如果需要修改结算账户类型（个人/对公账户）、开户人姓名&证件号或公司名称&统一社会信用代码，可点击“新增结算”按钮；\n2、如果需要更换A门店绑定的结算ID，可以在已有的结算ID的关联门店中添加A门店，或新建一个结算ID绑定A门店；')
            : SizedBox.shrink(),
        SizedBox(width: 20),
        accountInfo?.showSettleSettingButton == true
            ? Tooltip(
                padding: EdgeInsets.only(
                  top: 10,
                  bottom: 10,
                  right: 110,
                  left: 10,
                ),
                margin: EdgeInsets.only(
                  left: 140,
                ),
                child: ButtonNew(
                  text: '修改结算信息',
                  width: 118,
                  onClick: () => _addAccountMessage('edit'),
                ),
                message:
                    '1、如果需要修改开户行、银行卡号、开户人手机号、结算账户绑定门店，可点击“修改”按钮；\n2、如果需要更换A门店绑定的结算ID，可以在已有的结算ID的关联门店中添加A门店，或新建一个结算ID绑定A门店。')
            : SizedBox.shrink(),
      ]),
    );
  }

  Widget _buildAppButton() {
    if (accountInfo?.showSettleSettingAddButton == true ||
        accountInfo?.showSettleSettingButton == true) {
      return Container(
          color: Color(0xFFFFFFFF),
          child: Column(children: [
            GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  Modal.showModalDialog(context,
                      title: "${_getOperationType()}结算信息",
                      child: Container(
                          padding:
                              EdgeInsets.symmetric(horizontal: 24, vertical: 0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              accountInfo?.showSettleSettingAddButton == true &&
                                      accountInfo?.showSettleSettingButton ==
                                          true
                                  ? Text("新增结算信息",
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Color(0xFF666666),
                                        fontWeight: FontWeight.w400,
                                      ))
                                  : SizedBox.shrink(),
                              accountInfo?.showSettleSettingAddButton == true
                                  ? Container(
                                      padding: EdgeInsets.all(12.0),
                                      margin:
                                          EdgeInsets.symmetric(vertical: 8.0),
                                      color: Color(0xFFF5F6FA),
                                      child: Text(
                                          "如果需要修改结算账户类型（个人/对公账户）、开户人姓名&证件号或公司名称&统一社会信用代码，可点击“新增结算”按钮；",
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: Color(0xFF666666),
                                            fontWeight: FontWeight.w400,
                                          )),
                                    )
                                  : SizedBox.shrink(),
                              accountInfo?.showSettleSettingAddButton == true &&
                                      accountInfo?.showSettleSettingButton ==
                                          true
                                  ? Text("修改结算信息",
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Color(0xFF666666),
                                        fontWeight: FontWeight.w400,
                                      ))
                                  : SizedBox.shrink(),
                              accountInfo?.showSettleSettingButton == true
                                  ? Container(
                                      padding: EdgeInsets.all(12.0),
                                      margin:
                                          EdgeInsets.symmetric(vertical: 8.0),
                                      color: Color(0xFFF5F6FA),
                                      child: Text(
                                          "1. 如果需要修改开户行、银行卡号、开户人手机号，可点击“修改”按钮；\n2. 如果需要修改结算账户绑定的门店，需要在电脑端进行修改。",
                                          style: TextStyle(
                                            fontSize: 14,
                                            color: Color(0xFF666666),
                                            fontWeight: FontWeight.w400,
                                          )),
                                    )
                                  : SizedBox.shrink(),
                              Text(
                                  "如果需要更换A门店绑定的结算ID，可以在已有的结算ID的关联门店中添加A门店，或新建一个结算ID绑定A门店。",
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Color(0xFF666666),
                                    fontWeight: FontWeight.w400,
                                  ))
                            ],
                          )));
                },
                child: Container(
                    height: 36.0,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "${_getOperationType()}结算信息说明",
                          style: TextStyle(
                            color: Color(0xFFFF6A00),
                            fontSize: 14,
                            fontWeight: FontWeight.w400,
                          ),
                        )
                      ],
                    ))),
            Container(
                margin: EdgeInsets.all(12.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    accountInfo?.showSettleSettingAddButton == true
                        ? Expanded(
                            child: ButtonNew(
                            text: '新增结算信息',
                            onClick: () => _addAccountMessageApp('add'),
                          ))
                        : SizedBox.shrink(),
                    accountInfo?.showSettleSettingAddButton == true &&
                            accountInfo?.showSettleSettingButton == true
                        ? SizedBox(width: 8)
                        : SizedBox.shrink(),
                    accountInfo?.showSettleSettingButton == true
                        ? Expanded(
                            child: ButtonNew(
                            text: '修改结算信息',
                            onClick: () => _addAccountMessageApp('edit'),
                          ))
                        : SizedBox.shrink(),
                  ],
                ))
          ]));
    } else {
      return SizedBox.shrink();
    }
  }

  @override
  Widget build(BuildContext context) {
    String routeFrom = widget?.params != null ? widget?.params['from'] : null;
    return CustomWillPopScope(
      child: Scaffold(
        appBar: UITools.renderNavbar(
          context: context,
          title: '账户结算信息',
        ),
        body: SafeArea(
            child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: <Widget>[
                    Container(
                      decoration: BoxDecoration(
                        color: Color(0xFFFFFFFF),
                        borderRadius: BorderRadius.circular(
                          isPC ? 10.5 : 21,
                        ),
                      ),
                      margin: isPC
                          ? EdgeInsets.only(
                              bottom: 12,
                            )
                          : EdgeInsets.all(12.0),
                      padding: EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8.5,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: <Widget>[
                          _buildTitle(title: '结算信息'),
                          _buildDetail(
                              label: '结算ID',
                              value: accountInfo?.settleSettingId?.toString() ??
                                  ''),
                          _buildDetail(
                              label:
                                  accountInfo?.isDelay == 1 ? '结算账期' : '结算周期',
                              value: accountInfo?.settlePeriodName ?? ''),
                          _buildDetail(
                              label: '结算方式',
                              value: accountInfo?.settleTypeName ?? ''),
                          _buildDetail(
                            label: '最低转出余额',
                            value:
                                '¥${MoneyTool.formatMoneyNoPrex(accountInfo?.minWithdrawAmount ?? 0)}',
                            msg: '达到最低转出金额后可转出',
                          ),
                          _buildTitle(
                              title: accountInfo?.payBindType == 1
                                  ? '银行账户'
                                  : (isPC && !isSchoolAccount)
                                      ? '美团商家钱包'
                                      : '',
                              action: 1),
                          accountInfo?.payBindType == 2 &&
                                  (!isPC || !isSchoolAccount)
                              ? _buildJumpWallet()
                              : SizedBox.shrink(),
                          accountInfo?.payBindType == 2 &&
                                  (!isPC || !isSchoolAccount)
                              ? _buildDetail(
                                  label: '钱包ID',
                                  value:
                                      accountInfo?.walletId?.toString() ?? '')
                              : SizedBox.shrink(),
                          _buildDetail(
                              label: '户名',
                              value: accountInfo?.bankAccountName ?? ''),
                          _buildDetail(
                              label: '账户',
                              value: accountInfo?.cardNumber != '' &&
                                      accountInfo?.cardNumber != null
                                  ? '**** **** **** ${accountInfo?.cardNumber}'
                                  : ''),
                          _buildDetail(
                              label: '开户行', value: accountInfo?.bankName ?? ''),
                          isPC ? _buildButton() : SizedBox.shrink(),
                        ],
                      ),
                    ),
                    isPC ? _buildChangesPrompt() : SizedBox.shrink(),
                  ],
                ),
              ),
            ),
            isPC ? SizedBox.shrink() : _buildAppButton(),
          ],
        )),
      ),
      from: routeFrom,
    );
  }
}
