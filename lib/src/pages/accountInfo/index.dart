import 'package:flutter/material.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:mt_flutter_web_utils/mt_flutter_web_utils.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:roo_flutter/tools/response_system.dart';
import 'package:waimai_e_fe_flutter_finance/src/common/flap.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/arrow.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/card.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/modal.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/question.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/accountInfo/components/util.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/balanceFlow/index.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/aigcPanel.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/accountInfo.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/home.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/aigcAnalysis.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/assetAccountInfo.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/platformTool.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/uiTools.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/util/index.dart';

@Flap('finance')
class AccountInfoPage extends StatelessWidget {
  AccountInfoPage({this.params, this.pageName});
  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  Widget build(BuildContext context) {
    return LoadingPage(
      child: AccountInfoWidget(
        pageName: pageName,
        params: params,
      ),
    );
  }
}

class AmountOfMoneyModel {
  // 构造函数
  AmountOfMoneyModel(this.title, this.money);
  String title;
  int money;
}

class AccountInfoWidget extends StatefulWidget {
  AccountInfoWidget({String pageName, this.params})
      : this.pageName =
            pageName ?? (params != null ? params['mtf_page'].toString() : '');
  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  _AccountInfoWidgetState createState() => _AccountInfoWidgetState();
}

class _AccountInfoWidgetState extends State<AccountInfoWidget>
    with AccountInfoMixin, RouteLifecycleStateMixin {
  /// 账户信息
  AssetAccountInfoModel accountInfo;

  /// 当前选中的账户类型
  int selectAcctType = AccountTypeMap.balanceType;

  // 推广费/保证金模块是否展示
  bool showSpecialItem = true;

  double tabCradHeight = 130;
  double tabCradWidth = 330;

  /// 以下内容为URL中字段

  /// 门店数量
  int poiCount = 1;

  /// 结算账户ID
  String accountId;

  /// 来源
  String linkSource;

  /// 是否展示AIGC面板
  bool showAigcPanel = false;

  /// 是否展示新AIGC面板
  bool showNewAigcPanel = false;

  bool isSchoolAccount = false;

  String schoolId;

  String schoolName;

  @override
  void initState() {
    super.initState();
    if (widget?.params != null) {
      // 通过壳子传递过来的参数
      poiCount = int.tryParse('${widget.params['poiCount']}') ?? 1;
      accountId = '${widget.params['accountId']}';
      linkSource = widget.params['linkSource'];
      selectAcctType = int.tryParse(widget.params['acctType'] ?? '') ??
          AccountTypeMap.balanceType;
      isSchoolAccount = widget.params['isSchoolAccount'] == '1';
      schoolId = widget.params['schoolId'];
      schoolName = widget.params['schoolName'];
    }
    jumpCheck();
    _fetchData();
    fetchShowSpecialItem().then((show) {
      showSpecialItem = show;
    }).whenComplete(() {
      this.setState(() {});
    });

    // 只有非校园账户才展示 AIGC 面板
    if (!isSchoolAccount) {
      fetchAigcGray(AigcSceneType.balanceFlow).then((value) {
        if (mounted) {
          setState(() {
            showAigcPanel = value;
          });
        }
      });
      fetchAigcNewGray().then((value) {
        if (mounted) {
          setState(() {
            showNewAigcPanel = value;
            tabCradWidth = value ? 430 : 330;
          });
        }
      });
    }

    ReportLX.pv(pageKeyInfo, cid);
    ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_4fk6m6d5_mv');
  }

  jumpCheck() {
    if (PlatformTool.isWeb) {
      Util.isMultiple().then((value) {
        // 由壳子切换成全部门店的情况，
        // 在全部门店下刷新
        if (value == true) {
          // 当前是单店页面，跳转多店
          if (poiCount == 1 && !isSchoolAccount) {
            print('MTFlutterWebUtils.bridgeJump /finance/pc/accountManager');
            MTFlutterWebUtils.bridgeJump('/finance/pc/accountManager');
          }
        } else {
          // 切换成单店的时候，需要把多店下壳子里的Url上的参数去掉
          // 否则内部路由切换，不更新壳子地址栏链接，再次切回多店的时候，地址栏的参数还是带着上次poiCount，就无法切换回预期的多店列表页面
          if (poiCount > 1) {
            if (isSchoolAccount) {
              MTFlutterWebUtils.bridgeJump(
                  '/gw/static_resource/finance#/finance/accountInfo?isSchoolAccount=1&schoolId=${schoolId}');
            } else {
              MTFlutterWebUtils.bridgeJump(
                  '/gw/static_resource/finance#/finance/accountInfo');
            }
          }
        }
      });
    }
  }

  // 是否是多店下，账号绑定全部门店
  bool isMultiPoi() {
    return poiCount > 1;
  }

  _fetchData() {
    Loading.showLoading();
    // 获取账户信息
    // 根据账户类型选择不同的 API
    final Future<AssetAccountInfoModel> apiCall = isSchoolAccount
        ? fetchSchoolAccountInfo({
            "schoolId": schoolId,
          })
        : fetchAccountInfo({
            "financeAccountId": accountId,
          });
    return apiCall.then((AssetAccountInfoModel acinfo) {
      this.accountInfo = acinfo;
    }).whenComplete(() {
      this.setState(() {});
      Loading.dismissLoading();
    });
  }

  _changeSelectAcctType(int acctType) {
    if (!PlatformTools.isPC) return;
    selectAcctType = acctType;
    this.setState(() {});
  }

  Widget _buildToolTip(String tip, Widget target) {
    return RooTooltipPC(
      verticalOffset: 10,
      message: tip,
      padding: EdgeInsets.all(12),
      triangleColor: Color(0xFF3f4156),
      decoration: BoxDecoration(
          color: Color(0xFF3f4156), borderRadius: BorderRadius.circular(2)),
      textStyle: TextStyle(
        decoration: TextDecoration.none,
        fontSize: 14,
        color: Colors.white,
      ),
      child: target,
    );
  }

  // 余额流水区域，PC上特有的
  Widget _buildMainAccount() {
    bool isSelected = selectAcctType == AccountTypeMap.balanceType;
    return RadiusCard(
        isActive: isSelected,
        onClick: () => _changeSelectAcctType(AccountTypeMap.balanceType),
        height: tabCradHeight,
        width: tabCradWidth,
        margin: EdgeInsets.only(top: 12),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildTitle('余额账户',
                'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/6df8e0f8742c6ae2e833862035c29f05/redpacket.png'),
            _buildMoneyItem(
              [AmountOfMoneyModel('余额（元）', accountInfo?.mainBalanceCent)],
              AccountTypeMap.balanceType,
              isAllowRecharge: accountInfo?.showMainAccountRechargeFlag == 1,
              isActive: isSelected,
            )
          ],
        ));
  }

  // 账户信息区域
  Widget _buildAccount() {
    bool isMuli = isMultiPoi();
    String titleDesc = isMuli
        ? '此账户关联门店 $poiCount 个'
        : '${accountInfo?.bankAccountName ?? ""}';
    String backupImage =
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/8f980dd2b3361eeaa629abbc32d13bcf/default_avatar.png';
    // 账户抬头部分
    Widget accountInfoTitle = Row(
      children: <Widget>[
        CircleAvatar(
          radius: 20,
          backgroundColor: Color(0xFFD8D8D8),
          backgroundImage:
              NetworkImage('${accountInfo?.picUrl ?? backupImage}'),
        ),
        Padding(
            padding: EdgeInsets.only(left: 10),
            child: Text(titleDesc,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500)))
      ],
    );
    Widget accountDetailJump = _outerLink(
        '账户结算信息', '/accountDetails', 'b_waimai_e_r9b58og9_mc',
        params: {
          "isSchoolAccount": isSchoolAccount ? '1' : '0',
          "schoolId": isSchoolAccount ? schoolId : '',
        });
    Widget appTitleCard = RadiusCard(
        child: Column(
      children: <Widget>[
        accountInfoTitle,
        SizedBox(height: 12),
        Divider(),
        SizedBox(height: 12),
        Row(
          children: <Widget>[
            Expanded(
                flex: 1,
                child: Container(
                    decoration: BoxDecoration(
                        border: Border(
                            right: BorderSide(
                      color: Color(0xFFEEEEEE),
                      width: 0.5,
                    ))),
                    child: accountDetailJump)),
            Expanded(
                flex: 1,
                child: _outerLink(
                    '余额充值缴费', '/balanceRecharge', 'b_waimai_e_2o9aaju8_mc',
                    params: {
                      "acctType": AccountTypeMap.balanceType,
                    })),
          ],
        ),
        SizedBox(height: 8),
      ],
    ));
    Widget pcTitleCard = RadiusCard(
        padding: ResponsiveSystem.only4PC(
          EdgeInsets.all(30),
        ),
        child: isMuli
            ? accountInfoTitle
            : Row(
                children: [
                  accountInfoTitle,
                  SizedBox(
                    width: 20,
                  ),
                  accountDetailJump,
                ],
              ));
    return ResponsiveSystem(app: appTitleCard, pc: pcTitleCard);
  }

  // 外部链接
  Widget _outerLink(String text, String link, String bid,
      {Map<String, dynamic> params}) {
    return GestureDetector(
        onTap: () {
          ReportLX.mc(pageKeyInfo, cid, bid);
          if (link == '/accountDetails') {
            RouterTools.flutterPageUrl(context, link, params: params);
          } else if (params == null) {
            // 其他页面正常处理
            RouterTools.flutterPageUrl(context, '$link', params: {
              'from': 'flutter',
            });
          } else {
            RouterTools.flutterPageUrl(
              context,
              '/balanceRecharge',
              params: params,
            );
            // RouteUtils.open(
            //     'https://waimaieapp.meituan.com/finance/fe/balanceRecharge?acctType=${params['acctType']}');
          }
        },
        child:
            Row(mainAxisAlignment: MainAxisAlignment.center, children: <Widget>[
          Text(
            '$text',
            style: TextStyle(fontSize: 14, color: Color(0xFF666666)),
          ),
          SizedBox(
            width: 4,
          ),
          ArrowIcon(color: ArrowIconColorEnum.grey),
        ]));
  }

  // 推广费账户区域
  Widget _buildPromotion() {
    Widget title = Align(
      child: _buildTitle('推广费账户',
          'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/80e72765bf9d9e25a744748fa6d43b5f/promotion.png'),
    );
    Widget autoRecharge = Align(
        child: accountInfo?.showAutoTransferFlag == 1
            ? GestureDetector(
                child: Row(children: <Widget>[
                  Text(
                    '自动充值',
                    style: TextStyle(color: Color(0xFF666666), fontSize: 14),
                  ),
                  ArrowIcon(color: ArrowIconColorEnum.grey),
                ]),
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_bbjguxyd_mc');
                  if (PlatformTools.isPC) {
                    RouterTools.openWebPageUrl(
                        '/ad/v1/pc?skipWaimaie#/account');
                  } else {
                    RouteUtils.open(
                        'itakeawaybiz://waimaieapi.meituan.com/mrn?mrn_biz=waimaibiz&mrn_entry=ad-commonpage&mrn_component=ad-commonpage&entry_page=pages/account/detail/index');
                  }
                },
              )
            : SizedBox.shrink());
    bool isSelected = selectAcctType == AccountTypeMap.promotionType;
    return accountInfo?.showAdAccount == 1
        ? RadiusCard(
            isActive: isSelected,
            onClick: () => _changeSelectAcctType(AccountTypeMap.promotionType),
            height: ResponsiveSystem.bothAppPc(runPc: tabCradHeight),
            width: ResponsiveSystem.bothAppPc(runPc: tabCradWidth),
            margin: EdgeInsets.only(top: 12),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    title,
                    autoRecharge,
                  ],
                ),
                _buildMoneyItem(
                    [AmountOfMoneyModel('余额（元）', accountInfo?.adBalanceCent)],
                    AccountTypeMap.promotionType,
                    isAllowRecharge:
                        accountInfo?.showAdAccountRechargeFlag == 1,
                    rechargeBid: 'b_waimai_e_chy6cs96_mc',
                    flowBid: 'b_waimai_e_kx1fyisq_mc',
                    isActive: isSelected,
                    type: 'promotion'),
              ],
            ),
          )
        : SizedBox.shrink();
  }

  // 推广红包账户区域
  Widget _buildRedPacket() {
    Widget title = _buildTitle('推广红包账户',
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/6df8e0f8742c6ae2e833862035c29f05/redpacket.png');

    int highB = accountInfo?.highCommissionReturnBalanceCent ?? 0;
    int tradeB = accountInfo?.tradeToPromotionBalanceCent ?? 0;
    int intellB = accountInfo?.intelligentActivityBalanceCent ?? 0;
    int discountB = accountInfo?.discountRedPacketBalanceCent ?? 0;
    // 赠送红包
    int giftRedMoney = accountInfo?.giftRedPacketBalanceCent ?? 0;

    int totalMoney = accountInfo?.adRedPacketBalanceCent ?? 0;

    bool isSelected = selectAcctType == AccountTypeMap.redPacketType;

    Widget detailTips = _buildMoneyItem(
      [
        AmountOfMoneyModel('余额（元）', totalMoney),
      ],
      AccountTypeMap.redPacketType,
      flowBid: 'b_waimai_e_ahgykfjr_mc',
      isActive: isSelected,
    );

    Widget detailAppWidegt = _buildMoneyItem(
      [
        AmountOfMoneyModel('高佣返红包（元）', highB),
        AmountOfMoneyModel('交易额转入推广费（元）', tradeB),
        AmountOfMoneyModel('智能账户（元）', intellB),
        AmountOfMoneyModel('赠送红包（元）', giftRedMoney),
        AmountOfMoneyModel('优惠红包账户（元）', discountB),
      ],
      AccountTypeMap.redPacketType,
      flowBid: 'b_waimai_e_ahgykfjr_mc',
      isActive: isSelected,
    );

    Widget detailPCWidget = _buildToolTip(
        '高佣返红包（元）${MoneyTool.formatMoney(highB)} \n\n交易额转入推广费（元）${MoneyTool.formatMoney(tradeB)} \n\n智能账户（元）${MoneyTool.formatMoney(intellB)} \n\n赠送红包（元）${MoneyTool.formatMoney(giftRedMoney)} \n\n优惠红包账户（元）${MoneyTool.formatMoney(discountB)}',
        detailTips);

    return accountInfo?.showAdRedPacketRechargeFlag == 1 && showSpecialItem
        ? RadiusCard(
            isActive: isSelected,
            onClick: () => _changeSelectAcctType(AccountTypeMap.redPacketType),
            height: ResponsiveSystem.bothAppPc(runPc: tabCradHeight),
            width: ResponsiveSystem.bothAppPc(runPc: tabCradWidth),
            margin: EdgeInsets.only(top: 12, left: 12),
            child: Container(
              color: Colors.transparent,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  title,
                  // 推广红包账户充值入口关闭
                  ResponsiveSystem(
                    app: detailAppWidegt,
                    pc: detailPCWidget,
                  )
                ],
              ),
            ))
        : SizedBox.shrink();
  }

  // 履约保证金账户区域
  Widget _buildSecurityDeposit() {
    if (!showSpecialItem) {
      return SizedBox.shrink();
    }
    bool isSelected = selectAcctType == AccountTypeMap.depositType;
    Widget title = _buildTitle(
      '履约保证金账户',
      'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/7381c32fa895f8f3f002ba74302f938b/securityDeposit.png',
      isDeposite: true,
    );
    Widget targetContent = _buildMoneyItem(
        [AmountOfMoneyModel('余额（元）', accountInfo?.depositBalanceCent)],
        AccountTypeMap.depositType,
        isAllowWithdraw: accountInfo?.supportDepositAccountSelfWithdraw == 1,
        rechargeBid: 'b_waimai_e_hnk3evsu_mc',
        flowBid: 'b_waimai_e_izfanrm5_mc',
        isActive: isSelected);
    Widget tipWidget = _buildToolTip(
      '您当前有 ${accountInfo?.bussinessCount ?? "--"} 笔${accountInfo?.bussinessName ?? "--"}正在审核中，\n\n金额 ${accountInfo?.bussinessAmount ?? "--"} 元。整体流程预计\n\n7～15个工作日内完成，请你耐心等待。\n\n更多详细信息请查看相关提现流水。',
      targetContent,
    );
    return accountInfo?.showDepositAccount == 1 && showSpecialItem
        ? RadiusCard(
            isActive: isSelected,
            onClick: () => _changeSelectAcctType(AccountTypeMap.depositType),
            height: ResponsiveSystem.bothAppPc(runPc: tabCradHeight),
            width: ResponsiveSystem.bothAppPc(runPc: tabCradWidth),
            margin: EdgeInsets.only(top: 12),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                title,
                // 履约保证金账户充值入口关闭
                tipWidget,
                // bussinessCount
                ResponsiveSystem(
                  app: accountInfo?.bussinessCount != null
                      ? Container(
                          padding: EdgeInsets.all(8),
                          margin: EdgeInsets.only(top: 4),
                          decoration: BoxDecoration(
                            color: Color(0xFFF5F6FA),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Flex(
                            direction: Axis.horizontal,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              Image.network(
                                'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/622f3e41409c7b85bdc694247a27b93c/message.png',
                                width: 16,
                                height: 16,
                              ),
                              SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                    '您当前有 ${accountInfo?.bussinessCount ?? "--"} 笔${accountInfo?.bussinessName ?? "--"}正在审核中，金额 ${accountInfo?.bussinessAmount ?? "--"} 元。整体流程预计7～15个工作日内完成，请你耐心等待。更多详细信息请查看相关提现流水。',
                                    style: TextStyle(
                                        color: Color(0xFF222222),
                                        fontSize: 12)),
                                flex: 1,
                              ),
                            ],
                          ))
                      : SizedBox.shrink(),
                ),
              ],
            ),
          )
        : SizedBox.shrink();
  }

  Widget _buildDepositeStatus() {
    int progressStatus = accountInfo?.depositRefundProcessStatus;
    // 0和3代表没有进行中的退还流程
    if (progressStatus == 0 || progressStatus == 3) {
      if (accountInfo?.depositBalanceCent != null &&
          accountInfo.depositBalanceCent <= 0) {
        return SizedBox.shrink();
      }
      if (accountInfo?.showDepositRefundFlag == 1) {
        return Expanded(
            flex: 1,
            child: GestureDetector(
              child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: <Widget>[
                    Text(
                      '申请退还',
                      style: TextStyle(color: Color(0xFF666666), fontSize: 14),
                    ),
                    ArrowIcon(color: ArrowIconColorEnum.grey),
                  ]),
              behavior: HitTestBehavior.opaque,
              onTap: () {
                RouterTools.openWebPageUrl(
                    '/finance/static/html/marginRefundApply.html?money=${accountInfo.depositBalanceCent}');
              },
            ));
      }
      return Question(
        isDark: true,
        onTap: _showDepositDialog,
      );
    } else if (progressStatus == 1 ||
        progressStatus == 2 ||
        progressStatus == 4) {
      // 1 2 和 4 代表有进行中的退还流程
      // 默认赋值 1 的状态
      String statusText = "创建中";
      Color textColor = Color(0xFF666666);
      if (progressStatus == 2) {
        statusText = "审核中";
        textColor = Color(0xFFFFA735);
      } else if (progressStatus == 4) {
        statusText = "审核驳回";
        textColor = Color(0xFFFF5A5A);
      }
      return Expanded(
          flex: 1,
          child: GestureDetector(
            child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: <Widget>[
                  Text(
                    '$statusText',
                    style: TextStyle(color: textColor, fontSize: 14),
                  ),
                  ArrowIcon(color: ArrowIconColorEnum.grey),
                ]),
            behavior: HitTestBehavior.opaque,
            onTap: () {
              RouterTools.openWebPageUrl(
                  '/finance/static/html/marginRefundApply.html?money=${accountInfo.depositBalanceCent}');
            },
          ));
    }
    return SizedBox.shrink();
  }

  // 售后红包账户
  Widget _buildFoodSafety() {
    bool isSelected = selectAcctType == AccountTypeMap.foodSafetyRedPacketType;
    return accountInfo?.showFoodSafetyRedPacketAccount == 1
        ? RadiusCard(
            isActive: isSelected,
            onClick: () =>
                _changeSelectAcctType(AccountTypeMap.foodSafetyRedPacketType),
            height: ResponsiveSystem.bothAppPc(runPc: tabCradHeight),
            width: ResponsiveSystem.bothAppPc(runPc: tabCradWidth),
            margin: EdgeInsets.only(top: 12),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                _buildTitle('售后红包账户',
                    'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/6df8e0f8742c6ae2e833862035c29f05/redpacket.png'),
                _buildMoneyItem(
                  [
                    AmountOfMoneyModel(
                        '余额（元）', accountInfo?.foodSafetyRedPacketBalanceCent)
                  ],
                  AccountTypeMap.foodSafetyRedPacketType,
                  isActive: isSelected,
                ),
              ],
            ),
          )
        : SizedBox.shrink();
  }

  // 通用头部
  Widget _buildTitle(
    String title,
    String imgUrl, {
    bool isDeposite = false,
  }) {
    return Row(
      children: <Widget>[
        Image.network(
          imgUrl,
          width: 16,
          height: 16,
        ),
        Padding(
            padding: EdgeInsets.only(
              left: 4,
              right: 4,
            ),
            child: Text(title,
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500))),
        isDeposite ? _buildDepositeStatus() : SizedBox.shrink(),
      ],
    );
  }

  // 通用金额项，isAllowRecharge 是否允许充值，isAllowWithdraw 是否允许提现
  Widget _buildMoneyItem(
    List<AmountOfMoneyModel> moneyItems,
    int acctType, {
    String rechargeBid, // 充值埋点 Bid
    String withdrawBid, // 提现埋点 Bid
    String flowBid, // 流水埋点 Bid
    bool isAllowRecharge = false,
    bool isAllowWithdraw = false,
    String type = '', // 判断是推广费账户

    /// 是否是选中状态
    bool isActive = false,
  }) {
    Widget widthdrawButton = Padding(
      padding: EdgeInsets.only(right: 8),
      child: ClipRRect(
        child: RooButtonAdapted(
          onClick: () {
            ReportLX.mc(pageKeyInfo, cid, withdrawBid);
            RouterTools.flutterPageUrl(
              context,
              '/balanceWithdraw',
              params: {
                'acctType': acctType,
              },
            );
            // PlatformTools.isPC
            //     ? MTFlutterWebUtils.bridgeJump(
            //         '/finance/web/balanceWithdraw?acctType=${acctType}')
            //     : RouteUtils.open(
            //         'https://waimaieapp.meituan.com/finance/fe/balanceWithdraw?acctType=${acctType}');
          },
          text: '提现',
          bdColor: isActive
              ? Border.all(
                  color: Colors.white,
                )
              : null,
          bgColor: isActive ? Colors.white : null,
          type: RooButtonAdaptedType.weaker,
          fontSize: 12,
          radiusSize: 25,
          height: 28,
          width: 64,
        ),
        borderRadius: BorderRadius.circular(
          25,
        ),
      ),
    );
    Widget rechargeButton = Padding(
      child: ClipRRect(
        child: RooButtonAdapted(
            text: '充值',
            type: RooButtonAdaptedType.weaker,
            bdColor: isActive
                ? Border.all(
                    color: Colors.white,
                  )
                : null,
            bgColor: isActive ? Colors.white : null,
            fontSize: 12,
            radiusSize: 25,
            height: 28,
            width: 64,
            onClick: () {
              ReportLX.mc(pageKeyInfo, cid, rechargeBid);
              if (type == 'promotion') {
                RouterTools.openWebPageUrl(PlatformTools.isPC
                    ? '/ad/v1/rpc#/subapp/isomor_recharge/pages/reCharge/index?bizad_source=financial_reconciliation'
                    : '/ad/v1/rmobile#/subapp/isomor_recharge/pages/reCharge/index?bizad_source=financial_reconciliation');
              } else {
                RouterTools.flutterPageUrl(context, '/balanceRecharge',
                    params: {
                      'acctType': acctType,
                      'linkSource': linkSource,
                    });
                // MTFlutterWebUtils.bridgeJump(
                //     '/finance/web/balanceRecharge?acctType=${acctType}&linkSource=${linkSource}');
              }
            }),
        borderRadius: BorderRadius.circular(25),
      ),
      padding: EdgeInsets.only(right: 8),
    );

    Widget buttonW = Expanded(
      flex: 1,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: <Widget>[
          isAllowRecharge
              ? rechargeButton
              : (isAllowWithdraw ? widthdrawButton : SizedBox(width: 72)),
          ResponsiveSystem(
            app: SizedBox(
              width: 72,
              height: 28,
              child: GestureDetector(
                onTap: () {
                  ReportLX.mc(pageKeyInfo, cid, flowBid ?? '');
                  RouterTools.flutterPageUrl(context, '/balanceFlow', params: {
                    "flowType": acctType,
                  });
                },
                child: Container(
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(14),
                      border: Border.all(
                        width: 1,
                        color: Color(0xFF999999),
                      )),
                  child: Center(
                    child: Text('流水',
                        strutStyle: PlatformTool.isWeb
                            ? null
                            : StrutStyle(
                                forceStrutHeight: true,
                                height: 1,
                              ),
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF222222),
                          fontWeight: FontWeight.w500,
                        )),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
    List<Widget> widgetList = [];
    moneyItems.asMap().forEach((index, item) {
      widgetList.add(
        Text(
          '${item.title ?? ""}',
          style: TextStyle(fontSize: 12, color: Color(0xFF666666)),
        ),
      );
      widgetList.add(
        Text(
          '${MoneyTool.formatMoney(item.money)}',
          style: TextStyle(
            color: Color(0xFF222222),
            fontWeight: FontWeight.w600,
            fontSize: ResponsiveSystem.bothAppPc(
              runApp: 20.0,
              runPc: 27.0,
            ),
          ),
        ),
      );
      widgetList.add(SizedBox(height: 8));
    });
    return Container(
      margin: EdgeInsets.only(top: 12),
      child: Row(
        children: [
          Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: widgetList),
          buttonW,
        ],
      ),
    );
  }

  // 履约保证金账户 问号点击弹窗显示
  void _showDepositDialog() {
    ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_ernrqy03_mc');
    Modal.showModalDialog(
      context,
      title: '提示',
      child: Text(
          "如商家在优惠申请书期限内无违约情形且到期20天内未续签的，系统将在第21天自动退还保证金。其他特殊情形，请联系您的业务经理处理。"),
    );
  }

  Widget paddingWidget = SizedBox(
    width: 10,
  );

  @override
  void didAppear() {
    if (!PlatformTools.isPC) {
      _fetchData();
    }
  }

  @override
  void didDisappear() {}

  Widget _buildApp() {
    return SingleChildScrollView(
      child: Container(
        padding: EdgeInsets.all(12),
        child: Column(
          children: <Widget>[
            _buildAccount(),
            _buildPromotion(), //推广账户
            _buildFoodSafety(), //售后红包账户
            _buildRedPacket(), //推广红包账户区域
            _buildSecurityDeposit(), //履约保证金账户区域
          ],
        ),
      ),
    );
  }

  Widget _buildPC() {
    Widget mainContent = Container(
        width: showNewAigcPanel ? 1314 : 1014,
        child: CustomScrollView(
          scrollDirection: Axis.vertical,
          controller: ScrollController(),
          slivers: [
            SliverToBoxAdapter(
              child: Column(
                children: [
                  _buildAccount(),
                  Container(
                    width: MediaQuery.of(context).size.width,
                    height: tabCradHeight,
                    child: ListView(
                      scrollDirection: Axis.horizontal,
                      children: [
                        _buildMainAccount(),
                        paddingWidget,
                        _buildPromotion(),
                        paddingWidget,
                        _buildFoodSafety(),
                        _buildRedPacket(),
                        paddingWidget,
                        _buildSecurityDeposit(),
                      ],
                    ),
                  ),
                  BalanceFlowWidget(
                    key: GlobalKey(debugLabel: selectAcctType.toString()),
                    acctFlowType: selectAcctType,
                    financeAccountId: accountId,
                    isMultiPoi: isMultiPoi() == true,
                    isSchoolAccount: isSchoolAccount,
                    schoolId: schoolId,
                    schoolName: schoolName,
                  ),
                ],
              ),
            ),
          ],
        ));

    if (showAigcPanel) {
      return CustomScrollView(
        scrollDirection: Axis.horizontal,
        controller: ScrollController(),
        slivers: [
          SliverToBoxAdapter(
            child: mainContent,
          ),
          SliverToBoxAdapter(
            child: showNewAigcPanel
                ? Container()
                : AigcPanel(
                    AigcSceneType.balanceFlow,
                    DateFormat.formatXfYYYYMMDD(
                        DateTime.now().millisecondsSinceEpoch)),
          )
        ],
      );
    } else {
      return mainContent;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: UITools.renderNavbar(context: context, title: '账户'),
        body:
            ResponsiveSystem.bothAppPc(runApp: _buildApp(), runPc: _buildPC()));
  }
}
