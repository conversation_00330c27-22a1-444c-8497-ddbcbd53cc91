import 'package:flutter/material.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:roo_flutter/tools/response_system.dart';
import 'package:waimai_e_fe_ffw_utils/platform_tool.dart';
import 'package:waimai_e_fe_flutter_finance/src/common/flap.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/card.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/ad_flow_detail.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/ad_flow_details.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/uiTools.dart';

@Flap('finance')
class AdFlowDetailPage extends StatelessWidget {
  AdFlowDetailPage({this.params, this.key});
  final Map<dynamic, dynamic> params;
  final Key key;

  @override
  Widget build(BuildContext context) {
    return LoadingPage(
      child: Scaffold(
        appBar: UITools.renderNavbar(context: context, title: '流水明细'),
        body: AdFlowDetail(
          key: key,
          params: params,
        ),
      ),
    );
  }
}

/// 智能满减活动流水明细
class AdFlowDetail extends StatefulWidget {
  const AdFlowDetail({
    this.key,
    this.params,
  }) : super(key: key);

  final Key key;
  final Map<dynamic, dynamic> params;

  @override
  State<AdFlowDetail> createState() => _AdFlowDetailState();
}

class _AdFlowDetailState extends State<AdFlowDetail> {
  bool isLoading = false;
  bool noMore = false;
  int pageNo = 1;
  int pageSize = 8;
  int totalCount = 1;
  // 总计金额
  int poiRealIncomeTotalAmount;
  // 结算账期
  String settleBillComment;

  List<AdFlowList> orders;
  // 以下内容从URL获取
  // 备注内容
  String comment = '';
  // 创建时间
  String cTime = '';
  // 流水单号
  String flowNo = '';
  TextStyle headerTextStyle = TextStyle(color: Colors.black);

  TextStyle commonTextStyle = TextStyle(
      fontSize: 14, color: Color(0xFF222222), fontWeight: FontWeight.w500);
  TextStyle greyTextStyle = TextStyle(color: Color(0xFF666666), fontSize: 12);
  TextStyle greyTextStyleBigger =
      TextStyle(color: Color(0xFF666666), fontSize: 14);

  @override
  void initState() {
    super.initState();
    if (widget?.params != null) {
      flowNo = widget.params['flowNo'] ?? '';
      comment = widget.params['comment'] ?? '';
      cTime = widget.params['cTime'] ?? '';
    }
    _fetchData();
  }

  @override
  void dispose() {
    super.dispose();
  }

  _fetchData({int pageParmsNo}) {
    if (isLoading == true) {
      return;
    }
    Loading.showLoading();
    isLoading = true;
    this.setState(() {});
    Map<String, int> params = {
      'pageNo': pageParmsNo ?? pageNo,
      'pageSize': pageSize,
      'flowNo': int.tryParse(flowNo) ?? -1,
    };
    // 获取数据
    fetchAdFlowDetailList(params)
        .then((data) {
          if (data != null) {
            poiRealIncomeTotalAmount = data.poiIncomeTotalAmount;
            settleBillComment = data.settleBillComment;
            if (data?.orders != null) {
              if (orders?.isNotEmpty == true && !PlatformTools.isPC) {
                orders.addAll(data.orders);
              } else {
                orders = data.orders;
              }
            }
            totalCount = ((data?.pageCount ?? 0) / pageSize).ceil();
            if (totalCount > pageNo) {
              noMore = false;
            } else {
              noMore = true;
            }
          }
          pageNo = pageParmsNo ?? pageNo;
        })
        .catchError((e) {})
        .whenComplete(() {
          isLoading = false;
          Loading.dismissLoading();
          this.setState(() {});
        });
  }

  Widget _buildTotalArea() {
    return RadiusCard(
      margin: EdgeInsets.symmetric(
        vertical: 8,
        horizontal: 12,
      ),
      padding: EdgeInsets.symmetric(
        vertical: 24,
        horizontal: 16,
      ),
      child: Container(
        child: Column(children: [
          Padding(
            padding: EdgeInsets.only(
              bottom: 8,
            ),
            child: Text(
              '智能满减活动转入',
              style: greyTextStyleBigger,
            ),
          ),
          Padding(
            padding: EdgeInsets.only(bottom: 8),
            child: Text(
              '${MoneyTool.formatMoneyWithPrex(poiRealIncomeTotalAmount)}',
              style: TextStyle(
                color: Color(0xFF222222),
                fontSize: 28,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            comment,
            style: greyTextStyle,
          ),
        ]),
        width: MediaQuery.of(context).size.width,
      ),
    );
  }

  Widget _buildOneRow(String first, String second) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: ResponsiveSystem.bothAppPc(
            runApp: MainAxisAlignment.spaceBetween,
            runPc: MainAxisAlignment.start),
        children: [
          Text(
            first ?? '',
            style: commonTextStyle,
          ),
          Padding(
            padding: EdgeInsets.only(left: 20),
            child: Text(
              second ?? '',
              style: commonTextStyle,
            ),
          )
        ],
      ),
    );
  }

  Widget _buildType() {
    return RadiusCard(
      margin: EdgeInsets.only(
        bottom: 8,
        left: 12,
        right: 12,
      ),
      padding: EdgeInsets.symmetric(
        vertical: 8,
        horizontal: 16,
      ),
      child: Column(
        children: [
          _buildOneRow('结算账期', settleBillComment),
          _buildOneRow('创建时间', cTime),
          _buildOneRow('流水单号', flowNo),
        ],
      ),
    );
  }

  // 平台代付 加标
  Widget _buildIsPaltform(int type, int value) {
    // 正向订单，赚取金额为负；退款订单，转圈金额为正时。这类数据是平台代付
    if ((type == 1 && value < 0) || (type == 2 && value > 0)) {
      return Text(
        '平台代付',
        style: greyTextStyle,
      );
    }
    return SizedBox.shrink();
  }

  Widget _buildTableContentRow(AdFlowList element) {
    return _buildTableRow(
      Container(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
                '${DateFormat.formatYYYYMMDD(element?.date)} #${element?.bizId ?? ''}'),
            Text(
              element?.type == 2 ? '退款订单' : '外卖订单',
              style: greyTextStyle,
            )
          ],
        ),
      ),
      Text('${MoneyTool.formatMoneyWithPrex(element?.configActivityAmount)}'),
      Text('${MoneyTool.formatMoneyWithPrex(element?.realActivityAmount)}'),
      Column(
        children: [
          Text(
            '${MoneyTool.formatMoneyWithPrex(element?.poiIncomeAmount)}',
            style: TextStyle(
                color: element?.poiIncomeAmount != null &&
                        element.poiIncomeAmount > 0
                    ? Color(0xFF00bf7f)
                    : Color(0xFFff192d)),
          ),
          _buildIsPaltform(element?.type, element?.poiIncomeAmount),
        ],
      ),
      commonTextStyle,
    );
  }

  Widget _buildTableRow(Widget first, Widget second, Widget third, Widget fouth,
      TextStyle defaultTextStyle) {
    double colWidth = 83;
    return DefaultTextStyle(
      style: defaultTextStyle,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Container(
              alignment: Alignment.centerLeft,
              child: first,
            ),
          ),
          Container(
            width: colWidth,
            child: second,
            alignment: Alignment.centerRight,
          ),
          Container(
            width: colWidth,
            child: third,
            alignment: Alignment.centerRight,
          ),
          Container(
            width: colWidth,
            child: fouth,
            alignment: Alignment.centerRight,
          ),
        ],
      ),
    );
  }

  Widget _buildTable() {
    Widget header = Padding(
      padding: EdgeInsets.symmetric(
        vertical: 8,
      ),
      child: _buildTableRow(
          Text(
            '日期&单号',
          ),
          Text('预算优惠'),
          Text('-  实际优惠'),
          Text('=  赚取金额'),
          greyTextStyleBigger),
    );

    Widget con = Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        ResponsiveSystem(
          app: header,
        ),
        ResponsiveSystem(
          app: orders != null
              ? Expanded(
                  child: ListView.builder(
                    itemCount: orders.length,
                    itemBuilder: (BuildContext context, int index) {
                      return _buildTableContentRow(orders[index]);
                    },
                  ),
                )
              : SizedBox.shrink(),
        ),
        ResponsiveSystem(
          pc: SingleChildScrollView(
            child: _buildTablePC(),
          ),
        ),
        ResponsiveSystem(
          app: noMore == false
              ? GestureDetector(
                  child: Container(
                    alignment: Alignment.center,
                    width: double.infinity,
                    child: Text(
                      '点击加载更多',
                      style: greyTextStyle,
                    ),
                  ),
                  onTap: () {
                    _fetchData(pageParmsNo: pageNo + 1);
                  },
                )
              : SizedBox.shrink(),
          pc: Padding(
            child: RooPagination(
              onPageChanged: (int currentPage) async {
                _fetchData(pageParmsNo: currentPage);
              },
              currentPage: pageNo,
              totalPage: totalCount,
            ),
            padding: EdgeInsets.only(
              top: 15,
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.only(
            top: 20,
            bottom: 8,
          ),
          child: Text(
            '总计 ${MoneyTool.formatMoneyWithPrex(poiRealIncomeTotalAmount)}',
            style: TextStyle(
                fontSize: 18,
                color: Color(0xFF222222),
                fontWeight: FontWeight.w700),
          ),
        ),
        Text(
          ' *赚取金额分为节省金额和平台代付金额',
          style: greyTextStyle,
        ),
      ],
    );

    return con;
  }

  Widget _buildMoneyWithColor(int money) {
    return Center(
      child: Text(
        '${MoneyTool.formatMoneyWithPrex(money)}',
        style: TextStyle(
            color: money != null && money > 0
                ? Color(0xFF00bf7f)
                : Color(0xFFff192d)),
      ),
    );
  }

  Widget _buildTablePC() {
    return Column(
      children: [
        RooTable<AdFlowList>(
          dataRowHeight: 40,
          headingRowHeight: 40,
          decoration: BoxDecoration(
            color: Color(0x66EEEEEE),
            border: Border.all(color: Color(0x4DCCCCCC)),
            borderRadius: BorderRadius.all(
              Radius.circular(8),
            ),
          ),
          rowColorSelector: (int index) {
            return Colors.white;
          },
          emptyProperty: RooTableEmptyProperty(
              image: Image.network(
            'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/19e0ae7c1b9e3965/empty_roo_new.png',
            height: 126,
            width: 126,
          )),
          dataSource: orders,
          appRender: null,
          columns: [
            RooTableColumn<AdFlowList>(
                label: '日期&订单编号',
                headingTextStyle: headerTextStyle,
                cellRender: (AdFlowList item) {
                  return Center(
                    child: Text(
                        '${DateFormat.formatYYYYMMDD(item?.date)} #${item?.bizId ?? ''}'),
                  );
                }),
            RooTableColumn<AdFlowList>(
                label: '交易类型',
                headingTextStyle: headerTextStyle,
                cellRender: (AdFlowList item) {
                  return Center(
                      child: Text(
                    item?.type == 2 ? '退款订单' : '外卖订单',
                  ));
                }),
            RooTableColumn<AdFlowList>(
                label: '预算金额',
                headingTextStyle: headerTextStyle,
                cellRender: (AdFlowList item) {
                  return Center(
                    child: Text(
                        '${MoneyTool.formatMoneyWithPrex(item?.configActivityAmount)}'),
                  );
                }),
            RooTableColumn<AdFlowList>(
                label: '实际优惠',
                headingTextStyle: headerTextStyle,
                cellRender: (AdFlowList item) {
                  return Center(
                    child: Text(
                        '${MoneyTool.formatMoneyWithPrex(item?.realActivityAmount)}'),
                  );
                }),
            RooTableColumn<AdFlowList>(
                label: '商家节省金额',
                headingTextStyle: headerTextStyle,
                cellRender: (AdFlowList item) {
                  return _buildMoneyWithColor(item?.poiIncomeAmount);
                }),
            RooTableColumn<AdFlowList>(
                label: '平台代付金额',
                headingTextStyle: headerTextStyle,
                cellRender: (AdFlowList item) {
                  return _buildMoneyWithColor(item?.platformPayAmount);
                }),
          ],
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.max,
      children: [
        _buildTotalArea(),
        _buildType(),
        Expanded(
          child: RadiusCard(
            margin: EdgeInsets.only(
              bottom: 16,
              left: 12,
              right: 12,
            ),
            padding: EdgeInsets.all(16),
            child: _buildTable(),
          ),
        ),
      ],
    );
  }
}
