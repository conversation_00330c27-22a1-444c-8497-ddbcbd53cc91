import 'dart:async';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:mt_flutter_web_utils/mt_flutter_web_utils.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:roo_flutter/tools/response_system.dart';
import 'package:waimai_e_fe_flutter_finance/src/common/flap.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/arrow.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/empty.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/lastMonthSelect.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/singleSelect.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/statusTag.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/accountInfo/components/util.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/aigcCard.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/noMoreDate.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/historyFlow.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/home.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/aigcAnalysis.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/historyFlows.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/uiTools.dart';

@Flap('finance')
class BalanceFlowPage extends StatelessWidget with HomeMixin {
  BalanceFlowPage({this.params, this.pageName, this.key});
  final Map<dynamic, dynamic> params;
  final String pageName;
  final Key key;

  @override
  Widget build(BuildContext context) {
    int flowType;
    // 默认是查询余额流水
    if (params != null) {
      flowType =
          int.tryParse('${params["flowType"]}') ?? AccountTypeMap.balanceType;
    } else {
      flowType = AccountTypeMap.balanceType;
    }
    ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_vh5j1p87_mv');
    return LoadingPage(
      child: Scaffold(
        appBar: UITools.renderNavbar(
            context: context,
            title: '${AccountTypeMap.flowTitleDescMap[flowType]}流水'),
        body: BalanceFlowWidget(
          key: key,
          pageName: pageName,
          acctFlowType: flowType,
          params: params,
          isMultiPoi: false,
        ),
      ),
    );
  }
}

class BalanceFlowWidget extends StatefulWidget {
  BalanceFlowWidget(
      {String pageName,
      this.params,
      this.key,
      this.acctFlowType = AccountTypeMap.balanceType,
      this.isMultiPoi = false,
      this.isSchoolAccount = false,
      this.schoolName,
      this.schoolId,
      this.financeAccountId})
      : this.pageName =
            pageName ?? (params != null ? params['mtf_page'].toString() : '');

  final Map<dynamic, dynamic> params;
  final bool isMultiPoi;
  final String pageName;
  final String financeAccountId;
  final bool isSchoolAccount;
  final String schoolId;
  final String schoolName;
  final Key key;

  /// 账户流水类型
  final int acctFlowType;

  @override
  _BalanceFlowWidgetState createState() => _BalanceFlowWidgetState();
}

class _BalanceFlowWidgetState extends State<BalanceFlowWidget>
    with BalanceFlowMixin {
  /// 余额流水列表
  List<HistoryFlowsModel> historyFlowsList = [];
  // 1提现中，2提现成功，3提现失败
  Map<int, String> withdrawStatusText = {
    1: '交易中',
    2: '交易成功',
    3: '交易失败',
    5: '交易中',
  };

  TextStyle tableTextStyle = TextStyle(fontSize: 14);

  /// 查询条件：时间，-1 表示查询全部时间
  int month;
  String beginDate; //非必须，可不传，格式:"2020-11-12"
  String endDate;

  // 分页
  int pageNo = 1;
  final int pageSize = 20;

  /// 是否还有下一页
  bool _hasMore = false;

  /// 总页数
  int totalPageCount = 1;

  /// 查询条件：类型，非必须，可不传,默认查询所有类型 1:转入 -1:转出
  int type;

  // 滚动到顶部 相关
  ScrollController _scrollController;
  bool isLoadingHistoryData = false;
  int preNow = 0;
  Timer timer;
  // 是否展示滚动后的回到顶部按钮
  bool showToTop = false;

  bool isLoading = false;
  bool isPC = PlatformTools.isPC;

  // 当前的流水列表类型
  int flowType = AccountTypeMap.balanceType;

  /// 是否展示AIGC卡片
  bool showAigcPanel = false;

  /// 是否展示新AIGC面板
  bool showNewAigcPanel = false;

  @override
  void initState() {
    super.initState();
    _initData();
    if (!isPC) {
      // App下的列表滚动能力
      _scrollController = ScrollController();
      _scrollController.addListener(() async {
        int now = DateTime.now().millisecondsSinceEpoch;
        if (now - preNow > 100) {
          showToTop = _scrollController.offset >= 400;
          setData();
          preNow = now;
        }
      });
      if (flowType == AccountTypeMap.balanceType) {
        fetchAigcGray(AigcSceneType.balanceFlow).then((value) {
          if (mounted) {
            setState(() {
              showAigcPanel = value;
            });
          }
        });

        fetchAigcNewGray().then((value) {
          if (mounted) {
            setState(() {
              showNewAigcPanel = value;
            });
          }
        });
      }
    }
    ReportLX.pv(pageKeyInfo, cid);
  }

  void setData() {
    if (mounted) {
      this.setState(() {});
    }
  }

  _initData() {
    if (widget?.acctFlowType != null) {
      flowType = widget.acctFlowType ?? AccountTypeMap.balanceType;
    }
    // 获取历史流水列表，默认第一页数据
    _fetchData(pageNoParam: 1, acctType: flowType);
  }

  @override
  void didUpdateWidget(BalanceFlowWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.acctFlowType != null &&
        oldWidget.acctFlowType != widget.acctFlowType) {
      _initData();
    }
  }

  @override
  void dispose() {
    _scrollController?.dispose();
    super.dispose();
  }

  /// 获取历史流水列表
  _fetchData(
      {String beginDateParam,
      String endDateParam,
      int selectedTypeParam,
      int pageNoParam,
      int acctType}) {
    if (isLoading) {
      return;
    }
    isLoading = true;
    if (pageNoParam == 1 && !PlatformTools.isPC) {
      Loading.showLoading();
    }
    // PC页面切换都有Loading
    ResponsiveSystem.only4PC(() => Loading.showLoading());
    // 根据 isSchoolAccount 决定调用哪个 API
    final Future<AccountFlowsList> apiCall = widget.isSchoolAccount
        ? fetchSchoolAccountFlows({
            'beginDate':
                DateFormat.changeSplitChar(beginDateParam ?? beginDate),
            'endDate': DateFormat.changeSplitChar(endDateParam ?? endDate),
            'direction': selectedTypeParam ?? type,
            'pageNo': pageNoParam ?? pageNo,
            'pageSize': pageSize,
            "acctType": acctType ?? flowType,
            "financeAccountId": widget.financeAccountId ?? -1,
            "partnerType": "538", // 新增参数
            "schoolId": widget.schoolId,
          })
        : fetchAccountFlows({
            'beginDate':
                DateFormat.changeSplitChar(beginDateParam ?? beginDate),
            'endDate': DateFormat.changeSplitChar(endDateParam ?? endDate),
            'direction': selectedTypeParam ?? type,
            'pageNo': pageNoParam ?? pageNo,
            'pageSize': pageSize,
            "acctType": acctType ?? flowType,
            "financeAccountId": widget.financeAccountId ?? -1,
          });
    return apiCall
        .then((AccountFlowsList data) {
          // App上区分首屏，列表内容是累加的
          // PC上只保存当前页面内容
          if (data?.flowList != null) {
            if (isPC) {
              historyFlowsList = data.flowList ?? [];
            } else {
              if (pageNoParam == 1) {
                if (historyFlowsList.length > 0) {
                  _scrollToTop();
                }
                historyFlowsList = data.flowList ?? [];
              } else {
                historyFlowsList.addAll(data.flowList);
              }
            }
          }
          totalPageCount = ((data?.totalCount ?? 0) / pageSize).ceil();
          // 判断是否还有下一页内容
          if (totalPageCount > pageNo) {
            _hasMore = true;
          } else {
            _hasMore = false;
          }
          // 更新时间，页码操作
          beginDate = beginDateParam ?? beginDate;
          endDate = endDateParam ?? endDate;
          type = selectedTypeParam ?? type;
          pageNo = pageNoParam ?? pageNo;
          flowType = acctType ?? flowType;
        })
        .catchError((error) {})
        .whenComplete(() {
          isLoading = false;
          setData();
          Loading.dismissLoading();
        });
  }

  String getFlowStatusText(int code) {
    if (withdrawStatusText[code] != null) {
      return withdrawStatusText[code];
    } else {
      return '交易成功';
    }
  }

  bool hasDownLoad() {
    return PlatformTools.isPC &&
        (flowType == AccountTypeMap.balanceType ||
            flowType == AccountTypeMap.promotionType ||
            flowType == AccountTypeMap.redPacketType);
  }

  // 查询条件区域
  Widget _buildQuery() {
    return Container(
      margin: EdgeInsets.only(
          bottom: ResponsiveSystem.bothAppPc(
        runPc: 20.0,
        runApp: 0.0,
      )),
      color: Colors.white,
      padding: EdgeInsets.symmetric(
          horizontal: ResponsiveSystem.bothAppPc(runApp: 12.0, runPc: 0)),
      child: Column(
        children: <Widget>[
          Padding(
              padding: EdgeInsets.only(
                bottom: flowType == AccountTypeMap.balanceType ? 0 : 10,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  LastMonthSelect(
                    monthCnt: 4,
                    hasCustom: isPC,
                    onDateChange: (startTime, endTime) {
                      beginDate = startTime;
                      endDate = endTime;
                      // 重新获取第一页数据
                      _fetchData(
                          beginDateParam: beginDate,
                          endDateParam: endDate,
                          pageNoParam: 1);
                    },
                  ),
                  hasDownLoad() == true && !widget.isSchoolAccount
                      ? GestureDetector(
                          child: Container(
                            child: Text(
                              '下载账户流水',
                              style: TextStyle(color: Color(0xFFFF6A00)),
                            ),
                            alignment: Alignment.center,
                          ),
                          onTap: () {
                            RouteUtils.open(
                                '/finance/pc/download?type=account-running');
                          },
                        )
                      : SizedBox.shrink()
                ],
              )),
          flowType == AccountTypeMap.balanceType
              ? Padding(
                  child: SingleSelect(
                    options: [
                      SingleOption(text: '全部类型'),
                      SingleOption(id: 1, text: '转入'),
                      SingleOption(id: -1, text: '转出'),
                    ],
                    cb: (SingleOption option) {
                      type = option.id;
                      // 重新获取第一页数据
                      _fetchData(selectedTypeParam: option.id, pageNoParam: 1);
                    },
                  ),
                  padding: EdgeInsets.only(
                      bottom: ResponsiveSystem.bothAppPc(
                    runApp: 15.0,
                    runPc: 0.0,
                  )),
                )
              : SizedBox.shrink(),
        ],
      ),
    );
  }

  // 余额流水列表区域
  Widget _buildFlowList() {
    return Expanded(
      child: Container(
        color: Colors.white,
        padding: EdgeInsets.symmetric(horizontal: 12.0),
        child: historyFlowsList.length <= 0
            ? Empty(title: '暂无数据')
            : _buildFlowItems(),
      ),
    );
  }

  // 订单-活动奖励 / 红标
  /// 1117000243 (加价购补贴充值)，1110103901（商家推广红包账户优惠红包转入）1101391001 服务费转入 1101393001 红包赠送
  Widget _buildFlowTypeName(HistoryFlowsModel item) {
    return ResponsiveSystem(
      app: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Container(
                padding: EdgeInsets.only(right: 6.0),
                child: Text('${item.flowTypeName}', style: tableTextStyle)),
            item.flowType == 1110103901 ||
                    item.flowType == 1117000243 ||
                    item.flowType == 1101391001 ||
                    item.flowType == 1101393001 &&
                        !PlatformTools.isPC &&
                        item.comment != null
                ? Text('【${item.comment}】', style: greyTextStyle)
                : SizedBox.shrink(),
            item.withdrawStatus == 1 && !PlatformTools.isPC
                ? StatusTag(text: '提现中', type: StatusTagTypeEnum.ongoing)
                : SizedBox.shrink(),
            item.withdrawStatus == 3 && !PlatformTools.isPC
                ? StatusTag(text: '失败', type: StatusTagTypeEnum.fail)
                : SizedBox.shrink(),
          ]),
      pc: Container(
        child: Text('${item.flowTypeName}', style: tableTextStyle),
        alignment: Alignment.centerLeft,
      ),
    );
  }

  TextStyle greenTextStyle = ResponsiveSystem.bothAppPc(
      runApp: TextStyle(fontSize: 12.0, color: Color(0xFF00BF7F)),
      runPc: TextStyle(fontSize: 14.0, color: Color(0xFF00BF7F)));
  TextStyle greyTextStyle = TextStyle(fontSize: 12.0, color: Color(0xFF999999));

  Widget _buildMoney(HistoryFlowsModel item) {
    const TextStyle normalTextStyle = TextStyle(fontSize: 12);
    bool isMinus = item.moneyStr.indexOf('-', 0) > -1;
    return Row(
        mainAxisAlignment: ResponsiveSystem.bothAppPc(
          runApp: MainAxisAlignment.spaceBetween,
          runPc: MainAxisAlignment.end,
        ),
        children: <Widget>[
          Text(isMinus ? '-￥' : '+￥',
              style: isMinus
                  ? ResponsiveSystem.bothAppPc(
                      runApp: normalTextStyle, runPc: tableTextStyle)
                  : greenTextStyle),
          Text(
            '${MoneyTool.formatMoney(item.moneyCent > 0 ? item.moneyCent : -item.moneyCent)}',
            style: TextStyle(
                color: isMinus ? Color(0xFF222222) : Color(0xFF00BF7F),
                fontSize: ResponsiveSystem.bothAppPc(
                  runApp: 16.0,
                  runPc: 14.0,
                )),
          )
        ]);
  }

  Widget _buildTime(HistoryFlowsModel item) {
    return ResponsiveSystem(
        app: Text(
          item?.flowTime ?? '',
          style: greyTextStyle,
        ),
        pc: Container(
          alignment: Alignment.centerLeft,
          padding: EdgeInsets.only(
            left: 20,
          ),
          child: Text(
            item?.flowTime ?? '',
            style: tableTextStyle,
          ),
        ));
  }

  Widget _buildLeftMoney(HistoryFlowsModel item) {
    String leftMoney = item?.balanceStr ?? "";
    return ResponsiveSystem(
      app: Text(
        '余额 ￥$leftMoney',
        style: greenTextStyle,
      ),
      pc: Container(
        child: Text(
          leftMoney,
          style: tableTextStyle,
        ),
        alignment: Alignment.centerRight,
      ),
    );
  }

  /// flowType需要使用e_api的类型，前端会根据2-提现、6-账单、-10合并入账、4-合并提现、11-扫码购账单这五种类型跳转下一级页面，其他类型不跳转
  ///  subFlowType只有两种含义, 1, "智能满减" ;2, "智能折扣菜"
  _handleClickDetail(HistoryFlowsModel item) {
    if (item == null) return;

    int fType = item.flowType;
    int sfType = item.subFlowType;

    // 账单结算
    if (fType == 6) {
      // 6-结算 金额大于0为进钱，属于账单结算
      ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_knb54h19_mc');
      RouterTools.flutterPageUrl(context, '/remitDetail', params: {
        'settleBillId': item.settleBillId,
        'wmPoiId': item.wmPoiId,
      });
    }
    // 提现
    if (fType == 2) {
      ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_xjlk7xwk_mc');
      // 2-提现 金额小于0为出钱，属于提现
      RouterTools.flutterPageUrl(context, '/exportDetail', params: {
        'outId': item.outId,
        'from': 'flutter', // 区分 web 页面跳转，解决浏览器回退的路由跳转问题
      });
      // PlatformTools.isPC
      //     ? MTFlutterWebUtils.bridgeJump(
      //         '/finance/web/exportDetail?outId=${item.outId}')
      //     : RouteUtils.open(
      //         'https://waimaieapp.meituan.com/finance/fe/exportDetail?outId=${item.outId}');
    }
    // 推广费红包账户下才有
    // 智能满减活动转入的流水详情
    if (sfType == 1) {
      PlatformTools.isPC
          ? MTFlutterWebUtils.bridgeJump(
              '/finance/web/adFlowDateil?flowNo=${item?.outId}&comment=${item?.comment}&cTime=${item?.flowTime}')
          : RouteUtils.open(
              'https://waimaieapp.meituan.com/finance/fe/adFlowDateil?flowNo=${item?.outId}&comment=${item?.comment}&cTime=${item?.flowTime}');
    }
    // PC多店下才有的入口
    // 合并入账
    if (fType == -10) {
      MTFlutterWebUtils.bridgeJump(
          '/finance/static/html_pc/billReconciliation.html#/mutil-bill-detail?outId=${item.outId}');
    }
    // 扫码购账单
    if (fType == 11) {
      MTFlutterWebUtils.bridgeJump(
          '/finance/static/html_pc/billReconciliation.html#/scan-qr-code-bill?dailyBillId=${item.settleBillId}');
    }
  }

  Widget _buildFlowItems() {
    return ListView.builder(
        controller: _scrollController,
        // 底部塞一个提示信息
        itemCount:
            _hasMore ? historyFlowsList.length : historyFlowsList.length + 1,
        shrinkWrap: true,
        itemBuilder: (BuildContext context, int index) {
          if (index == historyFlowsList.length - 1 && _hasMore) {
            _fetchData(pageNoParam: pageNo + 1);
            return CupertinoActivityIndicator();
          } else if (index == historyFlowsList.length && !_hasMore) {
            return NoMoreDataWidget();
          }
          HistoryFlowsModel item = historyFlowsList[index];

          return GestureDetector(
              // 扩大点击范围，让自身整个区域都响应点击事件
              behavior: HitTestBehavior.opaque,
              onTap: () {
                _handleClickDetail(item);
              },
              child: Flex(
                  direction: Axis.horizontal,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Expanded(
                      child: Container(
                        height: 68.0,
                        padding: EdgeInsets.only(top: 12.0, right: 6.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                _buildFlowTypeName(item),
                                SizedBox(height: 4),
                                _buildTime(item),
                              ],
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: <Widget>[
                                _buildMoney(item),
                                SizedBox(height: 4),
                                _buildLeftMoney(item),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    hasDetailEntry(item)
                        ? ArrowIcon(
                            color: ArrowIconColorEnum.grey,
                          )
                        : SizedBox(
                            width: 12,
                          ),
                  ]));
        });
  }

  Widget toTop() {
    return Positioned(
      right: 12,
      bottom: 26,
      child: GestureDetector(
        onTap: _scrollToTop,
        child: Image.network(
          'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/cdc0bf1b82f8232afad8cdafdfd3458e/top.png',
          width: 40,
          height: 40,
        ),
      ),
    );
  }

  _scrollToTop() {
    if (_scrollController != null) {
      _scrollController.animateTo(
        .0,
        duration: Duration(milliseconds: 200),
        curve: Curves.ease,
      );
      showToTop = false;
      setData();
    }
  }

  /// flowType需要使用e_api的类型，前端会根据2-提现、6-账单、-10合并入账、4-合并提现、11-扫码购账单这五种类型跳转下一级页面，其他类型不跳转
  ///  subFlowType只有两种含义, 1, "智能满减" ;2, "智能折扣菜"
  bool hasDetailEntry(HistoryFlowsModel flowItem) {
    if (flowItem == null) return false;
    int fType = flowItem.flowType;
    int sfType = flowItem.subFlowType;
    if (PlatformTools.isPC) {
      return [2, 6, -10, 11].contains(fType) || sfType == 1;
    } else {
      return [2, 6].contains(fType) || sfType == 1;
    }
  }

  /// PC上渲染table区域
  Widget _buildTableList() {
    List<RooTableColumn<HistoryFlowsModel>> columns = [
      RooTableColumn<HistoryFlowsModel>(
        label: '日期',
        headerRender: (String label) {
          return Padding(
            padding: EdgeInsets.only(left: 20),
            child: Text(
              label,
              style: tableTextStyle,
            ),
          );
        },
        headerAlign: Alignment.centerLeft,
        cellRender: _buildTime,
      ),
      RooTableColumn<HistoryFlowsModel>(
        label: '类型',
        headingTextStyle: tableTextStyle,
        headerAlign: Alignment.centerLeft,
        cellRender: _buildFlowTypeName,
      ),
      RooTableColumn<HistoryFlowsModel>(
        label: '金额(元)',
        headingTextStyle: tableTextStyle,
        headerAlign: Alignment.centerRight,
        cellRender: _buildMoney,
      ),
      RooTableColumn<HistoryFlowsModel>(
        label: '现有余额',
        headingTextStyle: tableTextStyle,
        headerAlign: Alignment.centerRight,
        cellRender: _buildLeftMoney,
      ),
      RooTableColumn<HistoryFlowsModel>(
        label: '状态',
        headingTextStyle: tableTextStyle,
        headerAlign: Alignment.centerRight,
        cellRender: (HistoryFlowsModel flowItem) {
          return Container(
            child: Text(
              widget.isMultiPoi == true
                  ? flowItem?.flowStateName
                  : getFlowStatusText(flowItem?.withdrawStatus ?? 1),
              style: tableTextStyle,
            ),
            alignment: Alignment.centerRight,
          );
        },
      ),
    ];
    RooTableColumn<HistoryFlowsModel> actionWidget =
        RooTableColumn<HistoryFlowsModel>(
      label: '操作',
      headingTextStyle: tableTextStyle,
      headerAlign: Alignment.center,
      cellRender: (HistoryFlowsModel flowItem) {
        return (!widget.isSchoolAccount && hasDetailEntry(flowItem))
            ? GestureDetector(
                onTap: () {
                  _handleClickDetail(flowItem);
                },
                child: Container(
                  alignment: Alignment.center,
                  child: Text(
                    '详情',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: Color(0xFFFF6A00),
                    ),
                  ),
                ),
              )
            : SizedBox.shrink();
      },
    );
    // 备注列内容
    RooTableColumn<HistoryFlowsModel> commentWidget =
        RooTableColumn<HistoryFlowsModel>(
            label: '备注',
            headingTextStyle: tableTextStyle,
            headerAlign: Alignment.centerRight,
            cellRender: (HistoryFlowsModel flowItem) {
              return Container(
                alignment: Alignment.centerRight,
                child: Text(
                  flowItem?.comment ?? '',
                  style: tableTextStyle,
                ),
              );
            });
    // 交易号列内容
    RooTableColumn<HistoryFlowsModel> chargeNo =
        RooTableColumn<HistoryFlowsModel>(
            label: '交易号',
            headingTextStyle: tableTextStyle,
            headerAlign: Alignment.center,
            cellRender: (HistoryFlowsModel flowItem) {
              return Container(
                  alignment: Alignment.center,
                  child: Text(
                    flowItem?.outId ?? '',
                    style: tableTextStyle,
                  ));
            });
    // 有备注列
    if (flowType == AccountTypeMap.redPacketType) {
      columns.addAll([commentWidget]);
    }
    // 有交易号列
    if (flowType != AccountTypeMap.balanceType) {
      columns.add(chargeNo);
    }
    // 有操作列（必须放在最后一列）
    if (flowType == AccountTypeMap.redPacketType ||
        flowType == AccountTypeMap.balanceType) {
      columns.addAll([actionWidget]);
    }
    return RooTable<HistoryFlowsModel>(
      dataRowHeight: 40,
      headingRowHeight: 40,
      decoration: BoxDecoration(
        color: Color(0x66EEEEEE),
        border: Border.all(color: Color(0x4DCCCCCC)),
        borderRadius: BorderRadius.all(
          Radius.circular(8),
        ),
      ),
      rowColorSelector: (int index) {
        return Colors.white;
      },
      emptyProperty: RooTableEmptyProperty(
          image: Image.network(
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/19e0ae7c1b9e3965/empty_roo_new.png',
        height: 126,
        width: 126,
      )),
      dataSource: historyFlowsList,
      appRender: null,
      columns: columns,
    );
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveSystem(
      app: Stack(children: <Widget>[
        Container(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: <Widget>[
              _buildQuery(),
              SizedBox(height: 8),
              Visibility(
                visible: showAigcPanel && !showNewAigcPanel,
                child: Container(
                  margin: EdgeInsets.fromLTRB(12, 4, 12, 12),
                  child: AigcCard(
                      AigcSceneType.balanceFlow,
                      DateFormat.formatXfYYYYMMDD(
                          DateTime.now().millisecondsSinceEpoch)),
                ),
              ),
              _buildFlowList(),
            ],
          ),
        ),
        showToTop ? toTop() : SizedBox(),
      ]),
      pc: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.white,
        ),
        padding: EdgeInsets.symmetric(
          vertical: 30,
          horizontal: 20,
        ),
        child: Column(
          children: [
            _buildQuery(),
            _buildTableList(),
            Padding(
              padding: EdgeInsets.only(
                top: 20,
              ),
              child: RooPagination(
                onPageChanged: (int currentPage) async {
                  _fetchData(pageNoParam: currentPage);
                },
                currentPage: pageNo,
                totalPage: totalPageCount,
              ),
            ),
          ],
        ),
        margin: EdgeInsets.only(
          top: 12,
        ),
      ),
    );
  }
}
