import 'package:flutter/material.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:roo_flutter/basic_components/radio/roo_radio.dart';
import 'package:roo_flutter/basic_components/radio_adapted/roo_radio_adapted.dart';
import 'package:toast_widget_flutter/toast_widget_flutter.dart';
import 'package:waimai_e_fe_flutter_finance/src/common/flap.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/back.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/newButton.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/balanceRecharge.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/balanceRecharge.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/platformTool.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/uiTools.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/util/index.dart';

@Flap('finance')
class BalanceRechargePage extends StatelessWidget {
  const BalanceRechargePage({this.params, this.pageName});
  final Map<dynamic, dynamic> params;
  final String pageName;

  // ad例子
  // http://e.platform.proxy.b.waimai.test.sankuai.com/finance/static/html_pc/billReconciliation.html#/account/recharge?acctType=1028&wmPoiId=-1&linkSource=adSys&bizad_source=&bizad_recommend_money=0&activityId=-1&acctId=********
  // 参数：?acctType=1028&wmPoiId=-1&linkSource=adSys&bizad_source=&bizad_recommend_money=0&activityId=-1&acctId=********

  @override
  Widget build(BuildContext context) {
    return LoadingPage(
      child: BalanceRecharge(
        pageName: pageName,
        params: params,
      ),
    );
  }
}

class BalanceRecharge extends StatefulWidget {
  BalanceRecharge({String pageName, this.params})
      : this.pageName =
            pageName ?? (params != null ? params['mtf_page'].toString() : '');
  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  _BalanceRechargesState createState() => _BalanceRechargesState();
}

class _BalanceRechargesState extends State<BalanceRecharge>
    with RechargeMixin, RouteLifecycleStateMixin {
  final TextEditingController _amountController = TextEditingController();
  static const Map<int, String> acctTypeMap = {
    999: "配送",
    1008: "余额",
    1009: "保证金",
    1028: "推广费",
    1038: "服务商保证金",
    1039: "推广红包",
  };

  RechargeInfoModal rechargeInfo;

  String errorText = '';
  String tipText = '';
  bool buttonEnable = false;

  int radioDefaultValue = 0;

  String wmPoiId;
  int acctType;
  String acctId;
  // 建议充值金额
  // ignore: non_constant_identifier_names
  num bizad_recommend_money;
  // ignore: non_constant_identifier_names
  String bizad_source;
  String activityId;
  String linkSource;
  String token;

  // PC上选中的金额
  RooRadioOptionsItem checkedValue;

  @override
  void initState() {
    super.initState();
    // 处理URL上的参数，主要是外部在使用
    getUrlParam();
    // 更新HTML的title
    updatePageTitle();
    // 查询账户充值信息, 避免App会请求两次接口
    if (PlatformTool.isWeb) {
      _fetchRechargeInfo();
    }

    // 埋点
    ReportLX.pv(pageKeyInfo, cid);
  }

  String get inputMoneyStr {
    String str = _amountController?.text ?? '';
    return str.trim();
  }

  num get inputMoney => toNum(inputMoneyStr);

  // 为了节省获取参数的成本
  String getParamByKey(String key) {
    dynamic pValue = widget?.params[key];
    if (pValue != null) {
      return '$pValue';
    }
    String res = pValue ?? Util.getUrlParam(key) ?? Util.getCookie(key);
    return res;
  }

  // 把非num类型转成num
  num toNum(String value) {
    num returnVal = 0;
    if (value != null && value.trim() != '') {
      if (value.runtimeType.toString() == 'String') {
        // 转成int，用于使用map来取值
        returnVal = num.tryParse(value);
      }
    }
    return returnVal;
  }

  /// 获取URL参数，兼容PC
  getUrlParam() {
    wmPoiId = getParamByKey('wmPoiId');
    String acctTypeParam = getParamByKey('acctType');
    acctType = toNum(acctTypeParam);
    print('acctType $acctType');
    bizad_recommend_money = toNum(getParamByKey('bizad_recommend_money'));
    bizad_source = getParamByKey('bizad_source');
    activityId = getParamByKey('activityId');
    linkSource = getParamByKey('linkSource');
    acctId = getParamByKey('acctId');
    token = getParamByKey('token');
    // ?acctType=1028&wmPoiId=-1&linkSource=adSys&bizad_source=&bizad_recommend_money=0&activityId=-1&acctId=********
    // wmPoiId -1 acctType 1028 bizad_recommend_money 0 bizad_source  activityId -1 linkSource adSys ********
    print(
        'token=$token wmPoiId=$wmPoiId acctType=$acctType bizad_recommend_money=$bizad_recommend_money bizad_source=$bizad_source activityId=$activityId linkSource=$linkSource acctId=$acctId');
  }

  /// 更新HTML的title
  updatePageTitle() {
    if (acctType == null || acctType == '') {
      return;
    }
    String title = '${acctTypeMap[acctType]}充值' ?? '账户充值';
    Util.setHtmlTitle(title);
  }

  /// 去掉可重置余额 '-'
  num formatAccountBalance() {
    if (rechargeInfo?.accountBalance != null) {
      return rechargeInfo?.accountBalance?.abs();
    } else {
      return 0;
    }
  }

  /// 当前余额
  num getCurentAccountBalance() {
    if (rechargeInfo?.accountBalance != null) {
      return rechargeInfo?.accountBalance;
    } else {
      return 0;
    }
  }

  /// 查询账户信息
  void _fetchRechargeInfo() {
    Loading.showLoading();
    fetchRechargeInfo({
      'wmPoiId': this.wmPoiId,
      'activityId': this.activityId,
      'isAdKA': this.linkSource == 'adSys',
      'acctId': this.acctId,
      'acctType': this.acctType,
      'bizad_source': this.bizad_source,
      // 'token': this.token,
    }).then((RechargeInfoModal response) {
      setState(() {
        rechargeInfo = response;
        setDefaultValue(rechargeInfo);
        //监听输入改变
        _amountController.addListener(_handleAmountChange);
        // 校验
        _handleAmountChange();
      });
      Loading.dismissLoading();
    }).catchError((error) {
      Loading.dismissLoading();
      throw error;
    });
  }

  // 设置默认值
  void setDefaultValue(RechargeInfoModal rechargeInfo) {
    if (rechargeInfo?.acctType == 1009) {
      num value = rechargeInfo?.depositRechargeAmount;
      if (value != null) {
        setInputValue('$value');
      }
    }
    num minAmount = rechargeInfo?.minAmount ?? 0;
    if (bizad_recommend_money != null && bizad_recommend_money > 0) {
      // 推广费账户采用 推荐金额和最小金额进行判断
      // 推荐金额大于最小金额,则默认填充推荐金额
      if (bizad_recommend_money > minAmount) {
        setInputValue('${bizad_recommend_money / 100}');
      } else {
        setInputValue('${minAmount / 100}');
      }
    }
  }

  // 设置值，主要是处理光标的位置
  void setInputValue(String value) {
    _amountController.value = TextEditingValue(
      text: value,
      selection: TextSelection.fromPosition(
        TextPosition(affinity: TextAffinity.downstream, offset: value.length),
      ),
    );
  }

  // 常规金额校验
  void moneyNormalCheck() {
    if (isBalanceRecharge()) {
      return;
    }
    if (inputMoneyStr == '') {
      errorText = '';
    } else if (!MoneyTool.isMoneyFormat(inputMoneyStr)) {
      errorText = '请填写正确的金额格式';
    } else if (inputMoney > 21474836.47) {
      errorText = '充值金额过大';
    } else if (inputMoney < 0) {
      errorText = '充值金额需要大于0';
    } else if (bizad_recommend_money != null && bizad_recommend_money != 0) {
      num minAmount = rechargeInfo?.minAmount ?? 0;
      if (inputMoney * 100 < minAmount) {
        errorText = '推广费最低充值${minAmount / 100}元';
      }
    } else {
      errorText = '';
      tipText = '';
      buttonEnable = true;
    }
    setState(() {
      tipText = tipText;
      errorText = errorText;
      buttonEnable = buttonEnable;
    });
  }

  // 余额账户限制
  money1008Check() {
    if (!isBalanceRecharge()) {
      return;
    }

    /// 金额转换保留两位小数
    String accBalance = (formatAccountBalance() / 100).toStringAsFixed(2);
    // accountBalance的正数部分
    num balance = formatAccountBalance() / 100;
    // 输入金额
    bool isInBalance =
        inputMoney != null && inputMoney > 0 && inputMoney <= balance;

    if (rechargeInfo?.accountBalance != null &&
        rechargeInfo.accountBalance > 0) {
      errorText = '不能充值';
    } else if (inputMoneyStr == '') {
      tipText = '可充值余额$accBalance';
    } else if (!isInBalance) {
      if (inputMoney > 0) {
        /// 限制充值金额 <= 账户余额
        errorText = '充值金额不可大于$accBalance元';
      } else {
        errorText = '充值金额需要大于0';
      }
    } else {
      tipText = '可充值余额$accBalance';
      errorText = '';
      buttonEnable = true;
    }
    setState(() {
      errorText = errorText;
      tipText = tipText;
      buttonEnable = buttonEnable;
    });
  }

  // 是否余额充值
  isBalanceRecharge() {
    return acctType == 1008;
  }

  /// 充值金额验证
  void _handleAmountChange() {
    errorText = '';
    tipText = '';
    buttonEnable = false;
    // 常规金额校验
    moneyNormalCheck();
    // 1008类型校验
    money1008Check();

    // 保持radio与输入同步联动
    if (inputMoneyStr != '' && PlatformTool.isPC) {
      RooRadioOptionsItem rooRadioOptionsItem = RooRadioOptionsItem(
        value: inputMoney,
        label: MoneyTool.formatMoneyWithYuan(inputMoney),
      );
      setState(() {
        checkedValue = rooRadioOptionsItem;
      });
    }
  }

  getOutNo(String successUrl) {
    String outNo = '';
    List<String> list = successUrl?.split('outNo=');
    if (list != null && list.length > 0) {
      outNo = list[1];
    }
    return outNo;
  }

  getH5SuccessUrl(String successUrl) {
    return Uri.encodeComponent(
        '/finance/fe/rechargeResult?outNo=${getOutNo(successUrl)}&acctType=$acctType');
  }

  getWebSuccessUrl(String successUrl) {
    return Uri.encodeComponent(
        '/finance/web/rechargeResult?outNo=${getOutNo(successUrl)}&acctType=$acctType');
  }

// 旧版收银台的前缀
  String oldCashierUrlPrefix;

  /// 确认充值操作
  void _fetchRechargePreOrder() async {
    Loading.showLoading(title: '正在跳转...');
    if (buttonEnable) {
      Map params = {
        "wmPoiId": wmPoiId,
        "acctId": acctId,
        "acctType": acctType,
        "amount": inputMoneyStr,
        "rechargeAmount": inputMoneyStr,
        // "rechargeAmountLong": inputMoneyStr,
        "bizad_source": bizad_source,
        "isAdKA": false,
        "giftAmount": 0,
        'token': this.token,
        'rechargeSource': PlatformTool.isPC ? 1 : 2
      };
      if (linkSource == 'adSys') {
        params.putIfAbsent('accountType', () => 20);
        params.putIfAbsent('isAdKA', () => true);
        params.putIfAbsent('source', () => 'pc_ad_main');
        fetchAdRechargePreOrder(params)
            .then((response) => dealAdPreOrder(response));
      } else {
        fetchRechargePreOrder(params)
            .then((response) => dealPreOrder(response));
      }
    }
  }

  dealAdPreOrder(RechargePreAdOrderModal rechargePreAdOrderModal) {
    // 旧版收银台的前缀
    oldCashierUrlPrefix = rechargePreAdOrderModal?.url;
    dealPreOrder(rechargePreAdOrderModal.rechargePreOrderModal);
  }

  dealPreOrder(RechargePreOrderModal response) {
    Loading.dismissLoading();
    if (response == null) {
      return;
    }

    String tradeno = response.tradeno;
    String payToken = response.pay_token;
    int loginType = response.loginType;
    String epassportToken = response.epassportToken;

    // pay_cashier_sdk_source分配表https://km.sankuai.com/page/**********
    Map cashierParams = {
      "tradeno": tradeno,
      "pay_token": payToken,
      "loginType": loginType,
      "epassportToken": epassportToken,
      "pay_cashier_sdk_source": PlatformTool.isPC ? 16 : 17,
    };

    // jumpNew === 0时跳旧版收银台，非收银台1.x，而是更旧的收银台
    if (response.jumpNew == 0) {
      cashierParams['biz_token'] = epassportToken;
      cashierParams['bizad_source'] = bizad_source;
      cashierParams['pay_success_url'] = response.pay_success_url;
      cashierParams['redr_url'] = response.redrUrl;
      // 历史逻辑需要保留
      if (acctType == 1028) {
        cashierParams['limit'] = 0;
      }
      RouterTools.openWebPageUrl(
        '$oldCashierUrlPrefix/m/cashier/show/index',
        params: cashierParams,
      );
      return;
    }

    String successUrl = response.pay_success_url;
    if (PlatformTool.isWeb) {
      cashierParams.putIfAbsent(
          'pc_pay_success_url', () => getWebSuccessUrl(successUrl));
    } else {
      // 目前收银台app页面如果没有flap链接会报错，这里加上但实际没使用
      cashierParams.putIfAbsent('flap_id', () => 'finance');
      cashierParams.putIfAbsent('flap_entry', () => 'RechargeResultPage');
      cashierParams.putIfAbsent(
          'moduleName', () => 'waimai_e_fe_flutter_finance');
      // 跳转H5链接需要使用is_force_http_url参数，否则收银台中间页面会走flap路径的逻辑
      cashierParams.putIfAbsent('is_force_http_url', () => '1');

      cashierParams.putIfAbsent(
          'pc_pay_success_url', () => getH5SuccessUrl(successUrl));
    }
    print('cashierParams=$cashierParams');
    RouterTools.openWebPageUrl(
      '/gw/static_resource/cashier/recharge',
      params: cashierParams,
    );
  }

  // PC上其他金额区域
  _buildPcOtherMoney() {
    List list = [
      {
        "value": 0,
        "label": '其他',
      },
      {
        "value": 500,
        "label": '500元',
      },
      {
        "value": 1000,
        "label": '1,000元',
      },
      {
        "value": 2000,
        "label": '2,000元',
      },
      {
        "value": 5000,
        "label": '5,000元',
      }
    ];
    if (linkSource == 'adSys') {
      list = [
        {
          "value": 0,
          "label": '其他',
        },
        {
          "value": 50000,
          "label": '50,000元',
        },
        {
          "value": 100000,
          "label": '100,000元',
        },
        {
          "value": 200000,
          "label": '200,000元',
        }
      ];
    }

    List<RooRadioOptionsItem> items = [];

    list.forEach((ele) {
      items.add(RooRadioOptionsItem(value: ele['value'], label: ele['label']));
    });

    return PlatformTool.isPC
        ? RooRadioAdapted(
            options: items,
            checkedValue: checkedValue,
            onChanged: (item) {
              setState(() {
                checkedValue = item;
              });
              if (checkedValue.value == 0) {
                _amountController.text = '';
              } else {
                _amountController.text = '${checkedValue.value}';
              }
            },
            iconPosition: PlatformTool.isPC
                ? RooRadioIconAlign.left
                : RooRadioIconAlign.right,
          )
        : SizedBox.shrink();
  }

  // 表单
  Widget _buildAmountInput() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12),
      child: Row(
        children: <Widget>[
          Text(
            '￥',
            style: TextStyle(
              color: Color(0xFF222222),
              fontSize: 32,
              fontWeight: FontWeight.w600,
            ),
          ),
          Expanded(
            child: TextField(
              autofocus: true,
              controller: _amountController,
              keyboardType: TextInputType.numberWithOptions(decimal: true),
              style: TextStyle(
                color: Color(0xFF222222),
                fontSize: 32,
                fontWeight: FontWeight.w600,
              ),
              decoration: InputDecoration(
                border: InputBorder.none,
              ),
              cursorColor: Color(0xFFFFCC33),
            ),
          )
        ],
      ),
    );
  }

  // 充值账户
  Widget _buildAccountInfo() {
    return Container(
      height: 44.0,
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Color(0xFFEEEEEE), width: 0.5),
        ),
      ),
      child: Row(
        children: <Widget>[
          Text(
            '${acctTypeMap[acctType] ?? ''}账户',
            style: TextStyle(
              color: Color(0xFF222222),
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          Expanded(
            child: Text(
              rechargeInfo?.wmPoiName ?? '',
              textAlign: TextAlign.right,
              style: TextStyle(
                color: Color(0xFF666666),
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  // 广告文案提示
  _buildAdTip() {
    num accountBalance = rechargeInfo?.accountBalance;
    num minAmount = rechargeInfo?.minAmount ?? 0;
    if (accountBalance == null || bizad_recommend_money == null) {
      return SizedBox.shrink();
    }
    if (bizad_recommend_money > minAmount &&
        inputMoney < bizad_recommend_money / 100) {
      return Text('为保证您的顺利推广，建议您最少充值${bizad_recommend_money / 100}}元');
    }
    return SizedBox.shrink();
  }

  // 优惠活动
  _buildActivityComment() {
    String activityComment = rechargeInfo?.activityComment ?? '';
    if (activityComment != '') {
      return Text('活动提示：${activityComment}');
    }
    return SizedBox.shrink();
  }

  // 全部充值按钮的逻辑
  Widget _buildAllIn() {
    return (isBalanceRecharge() && errorText == '')
        ? GestureDetector(
            onTap: () {
              setState(() {
                _amountController.text = '${formatAccountBalance() / 100}';
              });
            },
            child: Text(
              '全部充值',
              style: TextStyle(
                color: Color(0xFFFF6A00),
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
            ),
          )
        : SizedBox.shrink();
  }

  // 充值金额
  Widget _buildRechargeAmount() {
    return Container(
      height: 44.0,
      margin: EdgeInsets.only(top: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              Text(
                '充值金额',
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF222222),
                  fontWeight: FontWeight.w500,
                ),
              ),
              Container(
                margin: EdgeInsets.only(left: 6),
                child: Text(
                  tipText == '' ? errorText : tipText,
                  style: TextStyle(
                    fontSize: 11,
                    color:
                        tipText == '' ? Color(0xFFFF5A5A) : Color(0xFF999999),
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ],
          ),
          _buildAllIn(),
        ],
      ),
    );
  }

  // 底部按钮
  Widget _buildButton() {
    return Padding(
      padding: EdgeInsets.only(top: 10),
      child: PlatformTool.isPC
          ? Row(
              children: [
                ButtonNew(
                  onClick: _fetchRechargePreOrder,
                  text: '确认充值',
                  hasColor: true,
                  disabled: this.buttonEnable == false,
                ),
                SizedBox(width: 10),
                ButtonNew(
                  onClick: () {
                    Util.back2Steps(context);
                  },
                  text: '取消',
                )
              ],
            )
          : ButtonNew(
              onClick: _fetchRechargePreOrder,
              text: '确认充值',
              hasColor: true,
              disabled: this.buttonEnable == false,
            ),
    );
  }

  // 当前余额
  Widget _buildCurrentBalance() {
    return Container(
      padding: EdgeInsets.only(bottom: 12),
      child: Text(
        '当前余额: ${MoneyTool.formatMoney(getCurentAccountBalance())}元',
      ),
    );
  }

  // 充值区域
  Widget _buildRechargeView() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(10.5),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          _buildAccountInfo(),
          _buildRechargeAmount(),
          _buildAmountInput(),
          acctType != 1008 ? _buildCurrentBalance() : SizedBox.shrink(),
          // _buildCurrentBalance(),
        ],
      ),
    );
  }

  @override
  void didAppear() {
    // 查询账户充值信息
    _fetchRechargeInfo();
  }

  @override
  void didDisappear() {
    print('didDisappear');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: UITools.renderNavbar(
        context: context,
        title: '${acctTypeMap[acctType] ?? ''}充值',
      ),
      body: Container(
        padding: EdgeInsets.all(PlatformTool.isPC ? 0 : 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            PlatformTool.isPC ? backPCTop(context) : SizedBox.shrink(),
            _buildRechargeView(),
            _buildPcOtherMoney(),
            _buildAdTip(),
            _buildActivityComment(),
            _buildButton()
          ],
        ),
      ),
    );
  }
}
