import 'package:flutter/material.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:waimai_e_fe_ffw_utils/platform_tool.dart';
import 'package:waimai_e_fe_flutter_finance/src/common/flap.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/back.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/modal.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/newButton.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/phoneVerify/index.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/balanceWithdraw.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/balanceWithdraw.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/uiTools.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/util/index.dart';

@Flap('finance')
class BalanceWithdrawPage extends StatelessWidget {
  const BalanceWithdrawPage({this.params, this.pageName});
  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  Widget build(BuildContext context) {
    return LoadingPage(
      child: BalanceWithdraw(
        pageName: pageName,
        params: params,
      ),
    );
  }
}

class BalanceWithdraw extends StatefulWidget {
  BalanceWithdraw({String pageName, Map<dynamic, dynamic> params})
      : this.pageName =
            pageName ?? (params != null ? params['mtf_page'].toString() : ''),
        this.params = params ?? {};
  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  BalanceWithdrawState createState() => BalanceWithdrawState();
}

class BalanceWithdrawState extends State<BalanceWithdraw>
    with BalanceWithdrawMixin, RouteLifecycleStateMixin {
  WithdrawInfoModal withdrawInfo;

  final bool isPC = PlatformTools.isPC;

  final copywriterStyle = TextStyle(
    color: Color(0xFF999999),
    fontSize: 12,
    fontWeight: FontWeight.w400,
  );

  final pcTitleTextStyle = TextStyle(
    color: Color(0xFF666666),
    fontSize: 16,
  );
  final pcContentTextStyle = TextStyle(
    color: Color(0xFF222222),
    fontSize: 16,
    fontWeight: FontWeight.w500,
  );

  final String payTip = '预计3个工作日内到账';
  String wmPoiId;

  Map<String, String> bankMap = {
    '中国农业银行':
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/3099b35b193a21b6abbda46bf1a8b251/abc.png',
    '中国银行':
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/461ca94587683630377f429b95db7775/boc.png',
    '交通银行':
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/aa9305acda22bcd8b64eb806fa4d232f/bocom.png',
    '渤海银行':
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/4e0f3281de1073b7c8125fccda6936d0/cbhb.png',
    '中国建设银行':
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/2c339b369ed901350055b3732b809f12/ccb.png',
    '光大银行':
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/2643cf3801fd654d78931859a66b3948/ceb.png',
    '广发银行':
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/a98b63b92bc6045632bad1314ea4b249/cgb.png',
    '兴业银行':
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/a8cfb2a1d6aaae7a3b626329bd628c98/cib.png',
    '中信银行':
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/7cc4384c885acc03208544207ea031e6/citic.png',
    '招商银行':
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/3363817de83781a7d79b223c40a36a02/cmb.png',
    '中国民生银行':
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/2d89383e6cab4c08ca74f32cf100ee6a/cmbc.png',
    '恒丰银行':
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/24793071d95f0fdcd96873e35fb8515c/hfb.png',
    '华夏银行':
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/bad7849418e2182d1e86d5d38defa85d/hxb.png',
    '中国工商银行':
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/c3e6b2b58161c99330de8588b20e7c61/icbc.png',
    '平安银行':
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/cdde39affaf35a0ae4d45e247fb35f86/pingan.png',
    '中国邮政储蓄银行':
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/ced554e30bc30c9bc39818222e1cc054/psbc.png',
    '浦发银行':
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/854e30a637eae66c92ca98f5b0686653/spdb.png',
  };

  // 查询账户信息
  void _fetchWithdrawInfo() {
    Loading.showLoading();
    fetchWithdrawInfo({
      'acctType': widget.params['acctType'],
    }).then((WithdrawInfoModal response) {
      setState(() {
        withdrawInfo = response;
      });
    }).whenComplete(() => Loading.dismissLoading());
  }

  String getLogoProps(String bankName) {
    String bankImg =
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/6cd212081c90c89bbac909be7db2d3bb/default.png';
    bankMap.forEach((String k, String v) {
      if (bankName.contains(k)) {
        bankImg = v;
      }
    });
    return bankImg;
  }

  // 充值提示文案
  Widget _buildPromptCopywriter() {
    return Container(
      margin: EdgeInsets.only(top: 20),
      padding: EdgeInsets.symmetric(horizontal: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          Container(
            margin: EdgeInsets.only(bottom: 4),
            child: Text(
              '以下情况无法发起手动提现操作：',
              style: TextStyle(
                color: Color(0xFF666666),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            '1.当前可提现余额小于最低提现金额',
            style: copywriterStyle,
          ),
          Text(
            '2.当前门店属于多门店合并打款',
            style: copywriterStyle,
          ),
          Text(
            '3.可提现余额小于等于0',
            style: copywriterStyle,
          ),
        ],
      ),
    );
  }

  // 提现金额
  Widget _buildAmount() {
    return Flex(
      direction: Axis.horizontal,
      children: <Widget>[
        Expanded(
          child: Container(
            alignment: Alignment.center,
            child: Text.rich(
              TextSpan(
                style: TextStyle(
                  color: Color(0xFF222222),
                ),
                children: [
                  TextSpan(
                    text: '￥',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  TextSpan(
                    text: '${MoneyTool.formatMoney(withdrawInfo?.balance)}',
                    style: TextStyle(
                      fontSize: 32,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
        )
      ],
    );
  }

  // 提现操作
  void handleWithdrawalConfirm() {
    if (withdrawInfo.balanceOutStatus == 0 || withdrawInfo.balance <= 0) {
      return;
    }
    if (withdrawInfo?.accountMobile == null ||
        withdrawInfo?.accountMobile == '') {
      Loading.showToast(message: '需要绑定手机号，才能提现。');
      return;
    }
    int acctType = int.tryParse('${widget.params['acctType']}');
    String accountMobile = withdrawInfo?.accountMobile;
    int balance = withdrawInfo.balance ?? 0;
    String newBlance = (balance / 100).toStringAsFixed(2);

    /// 手机验证码页面需要的参数
    Map<String, dynamic> params = {
      'acctType': acctType,
      'phone': accountMobile,
      'withdrawAmount': newBlance,
    };
    Modal.openDialogPCAndPageApp(
        context,
        '/phoneVerify',
        PhoneVerifyPage(
          params: params,
        ),
        title: '提现手机验证',
        params: params);
  }

  /// pc上取消操作
  void handleWithdrawalCancel() {
    Util.back(context);
  }

  // 充值按钮
  Widget _buildButton() {
    String buttonText = isPC ? '确认提现' : '全部提现';
    if (withdrawInfo.balanceOutStatus == 0) {
      buttonText = withdrawInfo?.balanceOutMsg ?? '--';
    } else if (withdrawInfo.balance <= 0) {
      buttonText = '余额不足，无法提现';
    }

    bool activeButton =
        withdrawInfo.balanceOutStatus != 0 && withdrawInfo.balance > 0;

    return ButtonNew(
      onClick: handleWithdrawalConfirm,
      text: buttonText,
      hasColor: true,
      disabled: !activeButton,
    );
  }

  Widget _buildCancelButton() {
    return ButtonNew(
      onClick: handleWithdrawalCancel,
      text: '取消',
      hasColor: false,
      width: 68,
    );
  }

// 账户信息
  Widget _buildAccountInfo() {
    // 商家钱包ID
    String walletId = withdrawInfo.walletId != null && withdrawInfo.walletId > 0
        ? withdrawInfo.walletId.toString()
        : '异常';

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(10.5),
      ),
      child: Column(
        children: <Widget>[
          Container(
            padding: EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Color(0xFFEEEEEE),
                  width: 0.5,
                ),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                Text(
                  withdrawInfo.bankAccountName ?? '--',
                  style: TextStyle(
                    color: Color(0xFF222222),
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                withdrawInfo.payBindType == 2
                    ? Text(
                        '商家钱包 $walletId',
                        style: TextStyle(
                          color: Color(0xFF666666),
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                        ),
                      )
                    : SizedBox.shrink()
              ],
            ),
          ),
          GestureDetector(
            onTap: () {
              ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_65w4xbqq_mc');
              RouterTools.flutterPageUrl(context, '/accountDetails', params: {
                'from': 'flutter',
              });
            },
            child: Container(
              color: Colors.transparent,
              padding: EdgeInsets.only(top: 12, bottom: 19.5),
              child: Flex(
                direction: Axis.horizontal,
                children: <Widget>[
                  Expanded(
                    flex: 6,
                    child: Container(
                      margin: EdgeInsets.only(right: 8),
                      child: Image.network(
                        getLogoProps(withdrawInfo?.bankName ?? ''),
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 41,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          '${withdrawInfo?.bankName ?? ''} (****${withdrawInfo?.cardNumber ?? ''})',
                          style: TextStyle(
                            color: Color(0xFF222222),
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          payTip,
                          style: TextStyle(
                            color: Color(0xFF999999),
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Image.network(
                      'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/27aa028dad636936701ddc3f46bf9c6f/arrow_right_grey.png',
                      height: 12,
                    ),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  // 金额区域
  Widget _buildWithdrawAmount() {
    return Container(
      margin: EdgeInsets.only(top: 10),
      padding: EdgeInsets.fromLTRB(12, 32, 12, 20),
      decoration: BoxDecoration(
        color: Color(0xFFFFFFFF),
        borderRadius: BorderRadius.all(Radius.circular(10.5)),
      ),
      child: Column(
        children: <Widget>[
          Text(
            '全部金额',
            style: TextStyle(
              color: Color(0xFF666666),
              fontSize: 14,
              fontWeight: FontWeight.w400,
            ),
          ),
          _buildAmount(),
          Padding(
            padding: EdgeInsets.only(top: 20),
            child: _buildButton(),
          ),
          _buildPromptCopywriter()
        ],
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    if (widget.params['acctType'] != null && isPC) {
      _fetchWithdrawInfo();
    }
    Util.getPoiId().then((value) {
      wmPoiId = value;
    });
    ReportLX.pv(pageKeyInfo, cid);
  }

  @override
  void didAppear() {
    // PC下首次不会触发
    print('didAppear');
    if (widget.params['acctType'] != null) {
      _fetchWithdrawInfo();
    }
  }

  @override
  void didDisappear() {}

  Widget _buildTitlePC(String title) {
    return Container(
      width: 66,
      margin: EdgeInsets.only(
        right: 20,
      ),
      child: Text(
        title,
        style: pcTitleTextStyle,
      ),
    );
  }

  Widget _buildRow(Widget innerContent) {
    return Container(
      alignment: Alignment.centerLeft,
      margin: EdgeInsets.only(
        bottom: 30,
      ),
      child: innerContent,
    );
  }

  Widget _buildPC() {
    bool isbankCardType = withdrawInfo?.payBindType == 1;
    String accoutType = isbankCardType ? '银行卡' : '钱包';
    String bankCardName =
        '${withdrawInfo?.bankName ?? ''}  ${withdrawInfo?.bankAccountName ?? ''}  (****${withdrawInfo?.cardNumber ?? ''})';
    String walletName =
        '${withdrawInfo?.walletAccountName ?? ''} ${withdrawInfo?.walletId ?? ''}';
    String accountName = isbankCardType ? bankCardName : walletName;
    return Container(
      padding: EdgeInsets.only(
        top: 30,
        left: 30,
        bottom: 30,
      ),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(21))),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildRow(
            Text(
              '余额提现到$accoutType',
              style: TextStyle(
                color: Color(0xFF222222),
                fontSize: 20,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          _buildRow(
            Row(
              children: [
                _buildTitlePC(accoutType),
                Text(
                  accountName,
                  style: pcContentTextStyle,
                ),
              ],
            ),
          ),
          _buildRow(
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: 36,
                  alignment: Alignment.centerLeft,
                  child: _buildTitlePC('提现金额'),
                ),
                Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      height: 36,
                      width: 225,
                      padding: EdgeInsets.only(
                        left: 12,
                      ),
                      decoration: BoxDecoration(
                        color: Color(0xFFEEEEEE),
                        border: Border.all(
                          color: Color(0xFFCCCCCC),
                        ),
                      ),
                      alignment: Alignment.centerLeft,
                      child: Text(
                        '${MoneyTool.formatMoney(withdrawInfo?.balance)}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xFF999999),
                        ),
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(
                        top: 2,
                      ),
                      child: Text(
                        '默认提现全部余额，无法修改，敬请谅解',
                        style: TextStyle(
                          color: Color(0xFFFF192D),
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
          _buildRow(
            Row(
              children: [
                _buildTitlePC('到账时间'),
                Text(
                  payTip,
                  style: pcContentTextStyle,
                ),
              ],
            ),
          ),
          _buildRow(
            Row(
              children: [
                SizedBox(
                  width: 86,
                ),
                _buildButton(),
                SizedBox(
                  width: 10,
                ),
                _buildCancelButton(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  _buildContent() {
    return isPC
        ? Column(
            children: [
              backPCTop(context),
              _buildPC(),
            ],
          )
        : Container(
            padding: EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                _buildAccountInfo(),
                _buildWithdrawAmount(),
              ],
            ),
          );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: UITools.renderNavbar(
        context: context,
        title: '余额提现',
      ),
      body: withdrawInfo != null ? _buildContent() : SizedBox(),
    );
  }
}
