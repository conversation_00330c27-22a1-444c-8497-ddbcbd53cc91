import 'dart:convert';

/// 蜜蜂APP中内嵌的“服务费测算小工具”的结果展示页面
/// Author：z<PERSON><PERSON>24

// import 'dart:html';

import 'package:flutter/material.dart';
import 'package:toast_widget_flutter/toast_widget_flutter.dart';
import 'package:waimai_e_fe_flutter_finance/src/common/flap.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/orderDetail/widgets/orderLevel1.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/orderDetail/widgets/orderLevel2.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/calculateServiceFee.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/calculateServiceFee.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/order/billChargeDetailDynamic.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/platformTool.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/uiTools.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/util/index.dart';

@Flap('finance')
class CalcServiceFeeToolPage extends StatelessWidget {
  const CalcServiceFeeToolPage({this.params});
  final Map<dynamic, dynamic> params;

  @override
  Widget build(BuildContext context) {
    return LoadingPage(
      child: CalcServiceFeeTool(
        params: params,
      ),
    );
  }
}

class CalcServiceFeeTool extends StatefulWidget {
  CalcServiceFeeTool({Map<dynamic, dynamic> params})
      : this.params = params ?? {};
  final Map<dynamic, dynamic> params;

  @override
  CalcServiceFeeToolState createState() => CalcServiceFeeToolState();
}

class CalcServiceFeeToolState extends State<CalcServiceFeeTool> {
  Map<String, dynamic> urlParams = {};
  ExtInfo extInfo = ExtInfo();
  List<DataList> dataList = [];
  @override
  void initState() {
    super.initState();
    fetchData();
  }

  void fetchData() {
    // 通过 URL 获取请求参数
    // 接口说明：https://km.sankuai.com/page/358284150
    String paramsStr = Uri.decodeComponent(Util.getUrlParam('params') ?? '');
    urlParams = jsonDecode(paramsStr == '' ? '{}' : paramsStr);
    fetchServiceFee(urlParams).then((CalculateServiceFeeModel response) {
      setState(() {
        dataList = response?.wmPoiBillChargeCardDynamicVo?.dataList ?? [];
        extInfo = response?.extInfo;
      });
    });
  }

  Widget _buildBasicItem(String title, String desc) {
    TextStyle titleStyle = TextStyle(fontSize: 14);
    TextStyle descStyle = TextStyle(fontSize: 12, color: Color(0xFF858687));
    return Container(
      margin: EdgeInsets.only(bottom: 10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            title,
            style: titleStyle,
          ),
          Text(
            desc,
            style: descStyle,
          ),
        ],
      ),
    );
  }

  /// 商家基础信息
  Widget _buildBasicInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildBasicItem('商家ID：', '${extInfo?.partnerId ?? ''}'),
        _buildBasicItem('收费模式：', '${extInfo?.chargeMode ?? ''}'),
        _buildBasicItem('配送方式：', '${extInfo?.logisticsCode ?? ''}'),
        SizedBox(
          height: 10,
        ),
        Text(
          '订单交易信息',
          textAlign: TextAlign.left,
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        SizedBox(
          height: 10,
        ),
        Container(
          padding: EdgeInsets.all(10),
          decoration: BoxDecoration(
            border: Border.all(width: 1, color: Color(0xFFD3D3D3)),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('商品小计：'),
                  Text(
                    '${extInfo?.foodTotalPrice ?? '￥0.00'}',
                    style: TextStyle(color: Color(0xFFff4a26)),
                  ),
                ],
              ),
              SizedBox(
                height: 10,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text('活动支出：'),
                  Text(
                    '${extInfo?.poiChargeFee ?? '-￥0.00'}',
                    style: TextStyle(color: Color(0xFFff4a26)),
                  ),
                ],
              ),
            ],
          ),
        )
      ],
    );
  }

  List<Widget> _buildFeeInfo() {
    List<Widget> list = [];
    dataList.asMap().forEach((int index, DataList item) {
      list.add(OrderLevel1(
          tags: item.tags ?? [],
          version: 0,
          title: item.title ?? '',
          totalAmount: item.totalAmount ?? '0',
          remark: item.remark ?? '',
          couponDetail: item.couponDetail ?? null,
          isShowDetail: item.isShowDetail,
          originTotalAmount: item.originTotalAmount ?? ''));
      list.add(OrderLevel2(
        version: 0,
        parentName: '',
        subDataList: item.subDataList ?? [],
        chargeModel: int.tryParse(extInfo?.chargeMode ?? '0'),
        keys: '',
        subRelatedInfo: item.subRelatedInfo,
      ));
    });
    return list;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      child: Scaffold(
        appBar: UITools.renderNavbar(
            context: context,
            title: '账户结算信息',
            onLeftIconTap: () {
              Util.back2Steps(context);
            }),
        body: SingleChildScrollView(
          child: Container(
            color: Colors.white,
            padding: EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                PlatformTool.isPC
                    ? GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () {
                          Util.back2Steps(context);
                        },
                        child: Container(
                            padding: EdgeInsets.only(bottom: 20),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: <Widget>[
                                Icon(
                                  Icons.arrow_back_ios,
                                  size: 16,
                                  color: Colors.black,
                                ),
                                Text(
                                  '返回',
                                  style: TextStyle(
                                    fontSize: 16,
                                    height: 1.0,
                                  ),
                                ),
                              ],
                            )),
                      )
                    : SizedBox.shrink(),
                _buildBasicInfo(),
                SizedBox(height: 20),
                Column(
                  children: _buildFeeInfo(),
                ),
              ],
            ),
          ),
        ),
      ),
      onWillPop: () async {
        String routeFrom =
            widget?.params != null ? widget?.params['from'] : '--';
        if (PlatformTool.isWeb && routeFrom != "flutter") {
          if (UITools.isPc) {
            // PC 页面为弹窗展示，禁止浏览器返回，do nothing
            return false;
          } else {
            // H5 端使用location.go(-1)
            Util.back(context);
          }
        } else {
          Navigator.pop(context);
        }
        return true;
      },
    );
  }
}
