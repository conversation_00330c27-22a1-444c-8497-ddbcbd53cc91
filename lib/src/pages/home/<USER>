import 'package:universal_html/html.dart' as html;

class HomeUtils {
  static void setLocalStorage(key, value) {
    try {
      html.window.localStorage[key] = value;
    } catch (e) {
      print(e);
    }
  }

  static String getLocalStorage(key) {
    try {
      return html.window.localStorage[key] ?? "";
    } catch (e) {
      return "";
    }
  }

  static String getCurrentUrl() {
    try {
      return html.window.location.href;
    } catch (e) {
      print('获取当前URL出错: $e');
      return '';
    }
  }
}
