import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';

const String GOOD_IMAGE_URL =
    'https://p0.meituan.net/merchantoperation/d64f51100e7956eb1d5f834590bb024c1091.png';
const String ACT_GOOD_IMAGE_URL =
    'https://p0.meituan.net/merchantoperation/80d2fa38445008f21582958c46ca5497744.png';

class FeedbackWidget extends StatefulWidget {
  final String sceneType;
  final String sessionID;
  FeedbackWidget({
    this.sceneType,
    this.sessionID,
  });
  @override
  _FeedbackWidgetState createState() => _FeedbackWidgetState();
}

class _FeedbackWidgetState extends State<FeedbackWidget> {
  String feedbackVal = '';

  final String cid = 'c_waimai_e_72m50aaj';
  final String pageKeyInfo = 'ai_feedBack';

  void handleLike() {
    String pageFrom = widget.sceneType ?? '0';
    ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_85mmtpw7_mc', val: {
      'page_from': pageFrom,
      'session_id': widget.sessionID,
    });
    ScaffoldMessenger.of(context)
        .showSnackBar(SnackBar(content: Text('感谢您的反馈')));
    setState(() {
      feedbackVal = 'like';
    });
  }

  void handleDisLike() {
    String pageFrom = widget.sceneType ?? '0';
    ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_cwto9cqe_mc', val: {
      'page_from': pageFrom,
      'session_id': widget.sessionID,
    });
    ScaffoldMessenger.of(context)
        .showSnackBar(SnackBar(content: Text('感谢您的反馈，我们会持续改进')));
    setState(() {
      feedbackVal = 'dislike';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 6),
      alignment: Alignment.centerRight,
      child: feedbackVal == ''
          ? Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                GestureDetector(
                  onTap: handleLike,
                  child: Row(
                    children: [
                      Image.network(GOOD_IMAGE_URL, width: 14, height: 14),
                      SizedBox(width: 4),
                      Text('有帮助'),
                    ],
                  ),
                ),
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 10),
                  height: 14,
                  width: 1,
                  color: Colors.grey[300],
                ),
                GestureDetector(
                  onTap: handleDisLike,
                  child: Row(
                    children: [
                      Transform.rotate(
                        angle: 3.14,
                        child: Image.network(GOOD_IMAGE_URL,
                            width: 14, height: 14),
                      ),
                      SizedBox(width: 4),
                      Text('无帮助'),
                    ],
                  ),
                ),
              ],
            )
          : feedbackVal == 'like'
              ? Image.network(ACT_GOOD_IMAGE_URL, width: 14, height: 14)
              : Transform.rotate(
                  angle: 3.14,
                  child:
                      Image.network(ACT_GOOD_IMAGE_URL, width: 14, height: 14),
                ),
    );
  }
}
