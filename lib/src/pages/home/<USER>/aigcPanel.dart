import 'dart:async';
import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:mt_flutter_web_utils/mt_flutter_web_utils.dart';
import 'package:roo_flutter/basic_components/button_adapted/roo_button_adapted.dart';
import 'package:roo_flutter/basic_components/dialog/roo_dialog.dart';
import 'package:roo_flutter/tools/string_utils.dart';
import 'package:roo_flutter/util/array_util.dart';
import 'package:uuid/uuid.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/double_line_chart/index.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/double_line_chart/modal/index.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/aigcComment.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/aigcAnalysis.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/requests.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/tools4native/webFunction.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/tools4web/webFunction.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/util/index.dart';
import 'package:wef_network/wef_request.dart';

/// FFW上的AIGC详情面板
class AigcPanel extends StatefulWidget {
  AigcPanel(this.sceneType, this.dailyBillDate);

  final String sceneType;
  final String dailyBillDate;
  @override
  State<StatefulWidget> createState() => _AigcPanelState();
}

class _AigcPanelState extends State<AigcPanel> {
  final String streamAPI = '/finance/waimai/aigc/api/stream/chat';
  final String authorization = 'finance_aigc_analysis_authorize';

  StreamController<ResponseData> controller;

  Map<String, dynamic> params;
  bool isRelatedSceneType = false;
  RelatedSceneType relatedScene;
  String message;
  String lineChartData;
  String subMessage;
  String subLineChartData;
  List<RelatedSceneType> relatedScenes;

  String leftYname = '日账单金额（元）';
  String rightYname = '订单数（单）';

  bool showTip = true;
  bool isAuthorized = false;
  bool needFirstPageUpdate = false;

  @override
  void initState() {
    super.initState();
    controller = StreamController<ResponseData>.broadcast();
    initData();
    String acctId = Util.getUrlParam('acctId') ?? Util.getCookie('acctId');
    if (HomeUtils.getLocalStorage('${authorization}_${acctId}') == 'true') {
      if (mounted) {
        setState(() {
          isAuthorized = true;
        });
      }
      $fetchStream(streamAPI, params, controller);
    }
  }

  @override
  void dispose() {
    controller.close();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant AigcPanel oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (isAuthorized &&
        widget.sceneType == AigcSceneType.dailyBill &&
        oldWidget.dailyBillDate != widget.dailyBillDate) {
      params['sessionID'] = Uuid().v4();
      params['dailyBillDate'] = widget.dailyBillDate;
      controller.close();
      // 清空当前日期数据
      lineChartData = null;
      message = null;
      relatedScenes = null;
      subLineChartData = null;
      subMessage = null;
      needFirstPageUpdate = true;
      controller = StreamController<ResponseData>.broadcast();
      $fetchStream(streamAPI, params, controller);
      setState(() {});
    }
  }

  void initData() {
    params = getCommParams();
    // 业务线，外卖标识为1
    params['businessLine'] = 1;
    params['sceneType'] = widget.sceneType;
    params['dailyBillDate'] = widget.dailyBillDate;
    params['sessionID'] = Uuid().v4();
  }

  Widget analyze(ResponseData response) {
    if (response == null || response.data == null) {
      return Container();
    }

    AigcAnalysis analysis = AigcAnalysis.fromJson(response.data);
    if (analysis == null) {
      return Container();
    }
    // event 为 AigcAnalysisEvent.all_done时不做处理
    if (analysis.event == AigcAnalysisEvent.cmpl) {
      if (isRelatedSceneType) {
        subMessage = analysis.cuiResponse.content;
      } else {
        message = analysis.cuiResponse.content;
        relatedScenes = analysis.relatedSceneType;
      }
    } else if (analysis.event == AigcAnalysisEvent.lineChartData) {
      List<ReplyMessage> list = analysis?.cuiResponse?.replyMessage;
      if (ArrayUtil.isNotEmpty(list)) {
        // 只返回一项，默认取第一个
        if (isRelatedSceneType) {
          subLineChartData = list[0]?.message ?? '';
        } else {
          lineChartData = list[0]?.message ?? '';
        }
      }
    }

    if (isRelatedSceneType) {
      return _buildAnalyzeBody(
          subMessage,
          // 等待全部内容传输结束，再插入表格数据
          analysis.event == AigcAnalysisEvent.all_done ? subLineChartData : '',
          [],
          analysis.event == AigcAnalysisEvent.all_done ? true : false);
    } else {
      return _buildAnalyzeBody(
          message,
          // 等待全部内容传输结束，再插入表格数据
          analysis.event == AigcAnalysisEvent.all_done ? lineChartData : '',
          relatedScenes,
          analysis.event == AigcAnalysisEvent.all_done ? true : false);
    }
  }

  Widget _buildAnalyzeBody(String message, String lineChartData,
      List<RelatedSceneType> relatedScenes, bool isReady) {
    List<Widget> list = [];
    if (StringUtil.isNotEmpty(message)) {
      list.add(analyzeString(message));
    }

    if (StringUtil.isNotEmpty(lineChartData)) {
      list.add(_buildLineChart(lineChartData));
    }
    if (isReady) {
      list.add(FeedbackWidget(
        sceneType: relatedScene?.sceneType ?? "1",
        sessionID: params['sessionID'],
      ));
    }
    if (ArrayUtil.isNotEmpty(relatedScenes)) {
      list.add(_buildRelatedSceneType(relatedScenes));
    }

    return Column(crossAxisAlignment: CrossAxisAlignment.start, children: list);
  }

  Widget analyzeString(String content) {
    RegExp linkRegExp = RegExp(r'\[(.*?)\|(.*?)\]');
    List<TextSpan> spans = [];
    int start = 0;

    for (final Match match in linkRegExp.allMatches(content)) {
      if (match.start > start) {
        spans.add(TextSpan(
          text: content.substring(start, match.start),
          style: TextStyle(color: Color(0xFF222222), fontSize: 14),
        ));
      }

      String text = match.group(1);
      String property = match.group(2);

      if (property.startsWith('http') ||
          property.startsWith('/') ||
          property.startsWith('itakeawaybiz')) {
        spans.add(
          TextSpan(
            text: text,
            style: TextStyle(color: Color(0xFFFF6A00), fontSize: 14),
            recognizer: TapGestureRecognizer()
              ..onTap = () {
                MTFlutterWebUtils.bridgeJump(property);
              },
          ),
        );
      } else {
        spans.add(
          TextSpan(
            text: text,
            style: TextStyle(color: Color(0xFFFF6A00), fontSize: 14),
          ),
        );
      }

      start = match.end;
    }

    if (start < content.length) {
      spans.add(TextSpan(
        text: content.substring(start),
        style: TextStyle(color: Color(0xFF222222), fontSize: 14),
      ));
    }

    return Container(
        margin: EdgeInsets.only(bottom: 10),
        child: RichText(
          text: TextSpan(
            children: spans,
          ),
        ));
  }

  Widget _buildLineChart(String lineChartData) {
    if (StringUtil.isEmpty(lineChartData)) {
      return Container();
    }
    List<LineChartItem> list = (jsonDecode(lineChartData) as List)
            ?.map((e) => LineChartItem.fromJson(e))
            ?.toList() ??
        [];

    if (list.length == 0) {
      return Container();
    }

    List<DoubleLineChartData> chartList = list
        .map((e) => DoubleLineChartData(
            xAxisData: e.dt.substring(4), leftY: e.amount, rightY: e.order))
        .toList();

    return Container(
        margin: EdgeInsets.only(top: 12),
        child: LayoutBuilder(builder: (context, constraints) {
          return DoubleLineCharts(
            width: constraints.maxWidth,
            height: 225,
            lineType: 0,
            buildTip: (DoubleLineChartData nowFocusItem) {
              if (!showTip) {
                nowFocusItem = null;
                showTip = true;
              }
              return chartList != null &&
                      chartList.length > 0 &&
                      nowFocusItem != null &&
                      showTip
                  ? _buildTip(nowFocusItem)
                  : SizedBox();
            },
            doubleLineChartDatas: chartList,
            leftYname: leftYname,
            rightYname: rightYname,
          );
        }));
  }

  Widget _buildTip(DoubleLineChartData nowFocusItem) {
    Color leftYColor = Color(0xFF0074FF);
    Color rightYColor = Color(0xFFFF8C42);
    return Container(
        padding: EdgeInsets.symmetric(vertical: 10, horizontal: 12),
        constraints: BoxConstraints(minWidth: 50),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6.5),
          color: Color(0xFF222222),
        ),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Container(
            margin: EdgeInsets.only(bottom: 10),
            child: Text(
              nowFocusItem.xAxisData,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Color(0XFFFFFFFF),
              ),
            ),
          ),
          _buildTipItem(leftYColor, leftYname, nowFocusItem.leftY),
          SizedBox(height: 8),
          _buildTipItem(rightYColor, rightYname, nowFocusItem.rightY),
        ]));
  }

  Widget _buildTipItem(Color color, String name, num data) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        Container(
          width: 6,
          height: 6,
          margin: EdgeInsets.only(right: 4),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(2),
            color: color,
          ),
        ),
        Text.rich(
          TextSpan(style: TextStyle(fontSize: 12, height: 1.2), children: [
            TextSpan(
              text: name,
              style: TextStyle(
                fontWeight: FontWeight.w400,
                color: Color.fromRGBO(255, 255, 255, 0.6),
              ),
            ),
            TextSpan(text: '  '),
            TextSpan(
              text: data?.toString() ?? 0,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Color(0XFFFFFFFF),
              ),
            )
          ]),
        ),
      ],
    );
  }

  Widget _buildTitle() {
    return Container(
      padding: EdgeInsets.only(top: 14, bottom: 17.5),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(10), topRight: Radius.circular(10))),
      child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [
        Image(
            width: 16,
            image: NetworkImage(
                'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/f801c3237149d349/<EMAIL>')),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 2),
          child: Text(
            '财务管家',
            style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 16,
                height: 1.25,
                color: Color(0xFF222222)),
          ),
        ),
        GestureDetector(
          onTap: () {
            showDialog(
                context: context,
                builder: (context) {
                  return RooDialog(
                    context,
                    content: Text(
                      '💡财务管家是一款为外卖商家设计的账单分析工具，它能基于财务数据，利用自然语言处理技术，智能生成对账分析报告，努力使商家对账更简单。\n💪功能目前处于内测阶段，还在持续迭代中，内容仅供参考。',
                    ),
                    confirmText: '我知道了',
                  );
                });
          },
          child: Image(
              width: 14,
              image: NetworkImage(
                  'http://p0.meituan.net/tuling/1922f6d6ed30414cf74acbef94eca82f712.png')),
        ),
      ]),
    );
  }

  Widget _buildRelatedSceneType(List<RelatedSceneType> relatedSceneType) {
    if (ArrayUtil.isEmpty(relatedSceneType)) {
      return Container();
    }

    List<Widget> list =
        relatedSceneType.map((e) => _buildRelatedSceneItem(e)).toList();

    return Container(
      margin: EdgeInsets.only(top: 24),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Text(
          '您可以继续查看相关分析：',
          style: TextStyle(fontSize: 12, color: Color(0xFF999999)),
        ),
        Row(
          children: list,
        )
      ]),
    );
  }

  Widget _buildRelatedSceneItem(RelatedSceneType scene) {
    if (scene == null) {
      return Container();
    }
    return GestureDetector(
      onTap: () {
        params['sceneType'] = scene.sceneType;
        params['sessionID'] = Uuid().v4();
        controller.close();
        controller = StreamController<ResponseData>.broadcast();
        $fetchStream(streamAPI, params, controller);
        if (mounted) {
          setState(() {
            isRelatedSceneType = true;
            relatedScene = scene;
          });
        }
      },
      child: Container(
        margin: EdgeInsets.only(top: 6, right: 8),
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(6)),
        child: Row(children: [
          Text(
            scene.sceneTypeName ?? '',
            style:
                TextStyle(fontSize: 12, color: Color(0xFF222222), height: 1.2),
          ),
          Image(
              width: 12,
              height: 12,
              image: NetworkImage(
                  'https://p0.meituan.net/ingee/50072cb3dacd1c741d6f3bef7bed205e631.png'))
        ]),
      ),
    );
  }

  Widget _buildRefreshText() {
    return Container(
        margin: EdgeInsets.only(top: isRelatedSceneType ? 100 : 200),
        alignment: Alignment.center,
        child: Row(mainAxisAlignment: MainAxisAlignment.center, children: [
          Text(
            '加载失败，',
            style: TextStyle(fontSize: 14, color: Color(0xFF999999)),
          ),
          GestureDetector(
              onTap: () {
                // 先添加事件代表 error 被处理，回到加载态
                controller.add(ResponseData(0, 'Error handled', null));
                controller.close();
                controller = StreamController<ResponseData>.broadcast();
                $fetchStream(streamAPI, params, controller);
                setState(() {});
              },
              child: Text(
                '请重试',
                style: TextStyle(fontSize: 14, color: Color(0xFFFF6A00)),
              ))
        ]));
  }

  Widget _buildAigcPanelRelated() {
    if (relatedScene == null) {
      return Container();
    }
    return Column(children: [
      LayoutBuilder(builder: (context, constraints) {
        return Container(
          width: constraints.maxWidth,
          constraints: BoxConstraints(
              minHeight: (MediaQuery.of(context).size.height / 2 - 55)),
          padding: EdgeInsets.all(16),
          margin: EdgeInsets.only(bottom: 24),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6), color: Colors.white),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                margin: EdgeInsets.only(bottom: 12),
                child: Text(
                  relatedScene?.sceneTypeName ?? '',
                  style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF222222)),
                ),
              ),
              StreamBuilder(
                  stream: controller.stream,
                  builder: ((context, responseData) {
                    if (responseData.hasData &&
                        (responseData.data as ResponseData)?.data != null) {
                      return Container(
                        child: analyze(responseData.data),
                      );
                    }
                    if (responseData.hasError) {
                      if (responseData.error is StreamError) {
                        return _buildRefreshText();
                      }
                    }
                    return Container(
                        margin: EdgeInsets.only(top: 100),
                        child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              CupertinoActivityIndicator(),
                              SizedBox(width: 4),
                              Text(
                                '加载中',
                                style: TextStyle(
                                    fontSize: 14, color: Color(0xFF999999)),
                              )
                            ]));
                  }))
            ],
          ),
        );
      }),
      RooButtonAdapted(
        type: RooButtonAdaptedType.hollow,
        width: 280,
        height: 36,
        onClick: () {
          params['sceneType'] = widget.sceneType;
          params['sessionID'] = Uuid().v4();
          controller.close();
          if (needFirstPageUpdate) {
            controller = StreamController<ResponseData>.broadcast();
            $fetchStream(streamAPI, params, controller);
            needFirstPageUpdate = false;
          }
          if (mounted) {
            setState(() {
              isRelatedSceneType = false;
              relatedScene = null;
            });
          }
        },
        text: "返回上一页",
      )
    ]);
  }

  Widget _buildAigcPanelMain() {
    if (!isAuthorized) {
      return Column(crossAxisAlignment: CrossAxisAlignment.center, children: [
        Text(
          '财务管家是一款为外卖商家设计的账单分析工具，它能基于财务数据，利用自然语言处理技术，智能生成对账分析报告。使财务对账变得更简单，快来体验吧！',
          style: TextStyle(fontSize: 12, color: Color(0xFF666666), height: 1.5),
        ),
        SizedBox(
          height: 8,
        ),
        Row(children: [
          Text(
            '报告由AI生成，仅供参考。点击查看',
            style:
                TextStyle(fontSize: 12, color: Color(0xFF666666), height: 1.5),
          ),
          GestureDetector(
            onTap: () {
              showDialog(
                  context: context,
                  builder: (context) {
                    return RooDialog(
                      context,
                      titleText: '详情',
                      titleTextStyle: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w400,
                          color: Color(0xFF222222)),
                      content: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '财务管家是一款专为外卖商家设计的账单分析工具，它能基于商家的财务数据，利用自然语言处理技术智能生成对账分析报告，为了向您提供相关AI服务。',
                              style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xFF3D3D3D)),
                            ),
                            SizedBox(height: 20),
                            Text(
                              '您同意并知悉：',
                              style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xFF3D3D3D)),
                            ),
                            Text(
                              '1、您同意将订单等财务数据共享给AI平台进行分析处理，我们将按照法律法规保护您的相关数据。\n2、您知悉，分析报告由AI生成，相关内容仅供参考。\n3、受对账时间维度的影响，您理解AI分析的财务数据与财务往来询证函的数据可能不同。',
                              style: TextStyle(
                                  fontSize: 14, color: Color(0xFF3D3D3D)),
                            ),
                            SizedBox(height: 20),
                            Text(
                              '点击“已阅读详细说明并开启使用”即视为您同意上述全部内容。',
                              style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xFF3D3D3D)),
                            ),
                          ]),
                      confirmText: '知道了',
                    );
                  });
            },
            child: Row(children: [
              Text('详细说明',
                  style: TextStyle(
                      fontSize: 12,
                      color: Color(0xFFFF6A00),
                      fontWeight: FontWeight.w500,
                      height: 1.5)),
              Icon(
                Icons.chevron_right,
                size: 13,
                color: Color(0xFFFF6A00),
              )
            ]),
          )
        ]),
        GestureDetector(
            onTap: () {
              String acctId =
                  Util.getUrlParam('acctId') ?? Util.getCookie('acctId');
              HomeUtils.setLocalStorage('${authorization}_${acctId}', 'true');
              if (mounted) {
                setState(() {
                  isAuthorized = true;
                });
              }
              $fetchStream(streamAPI, params, controller);
            },
            child: Container(
              margin: EdgeInsets.only(top: 16),
              constraints: BoxConstraints(maxWidth: 160),
              padding: EdgeInsets.symmetric(vertical: 6, horizontal: 8),
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  color: Color(0xFF222222),
                  borderRadius: BorderRadius.circular(2)),
              child: Text(
                '已阅读详细说明并立即启用',
                style: TextStyle(fontSize: 12, color: Colors.white),
              ),
            )),
      ]);
    }
    return StreamBuilder(
        stream: controller.stream,
        builder: ((context, responseData) {
          if (responseData.hasData &&
              (responseData.data as ResponseData)?.data != null) {
            return Container(
              child: analyze(responseData.data),
            );
          }

          if (responseData.hasError) {
            if (responseData.error is StreamError) {
              return _buildRefreshText();
            }
          }

          if (!isRelatedSceneType &&
              (StringUtil.isNotEmpty(message) ||
                  StringUtil.isNotEmpty(lineChartData))) {
            return Column(
              children: [
                Container(
                  child: _buildAnalyzeBody(
                      message, lineChartData, relatedScenes, true),
                ),
              ],
            );
          }

          return Container(
              margin: EdgeInsets.only(top: 200),
              child:
                  Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                CupertinoActivityIndicator(),
                SizedBox(width: 4),
                Text(
                  '加载中',
                  style: TextStyle(fontSize: 14, color: Color(0xFF999999)),
                )
              ]));
        }));
  }

  @override
  Widget build(BuildContext context) {
    return Listener(
      onPointerDown: (move) {
        if (showTip) {
          setState(() {
            showTip = false;
          });
          ;
        }
      },
      child: Container(
        margin: EdgeInsets.only(left: 10),
        padding: EdgeInsets.symmetric(horizontal: 16),
        width: 375,
        decoration: BoxDecoration(
            image: DecorationImage(
                fit: BoxFit.fill,
                image: NetworkImage(
                    'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/d98309eddd9e6466/<EMAIL>')),
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(10), topRight: Radius.circular(10))),
        child: Column(crossAxisAlignment: CrossAxisAlignment.center, children: [
          _buildTitle(),
          SingleChildScrollView(
              child: isRelatedSceneType
                  ? _buildAigcPanelRelated()
                  : _buildAigcPanelMain())
        ]),
      ),
    );
  }
}
