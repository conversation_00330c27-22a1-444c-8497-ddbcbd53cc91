import 'package:ffw_components_package/ffw_components_package.dart';
import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/aigcPanel.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/platformTool.dart';

/// AigcPanel 的封装组件，在 web 环境下使用 iframe 加载 URL
class AigcPanelWrapper extends StatefulWidget {
  const AigcPanelWrapper({
    Key key,
    @required this.sceneType,
    @required this.dailyBillDate,
    @required this.iframeUrl,
    this.width = 375,
    this.height = 600,
  }) : super(key: key);

  final String sceneType;
  final String dailyBillDate;
  final String iframeUrl;
  final double width;
  final double height;

  @override
  State<AigcPanelWrapper> createState() => _AigcPanelWrapperState();
}

class _AigcPanelWrapperState extends State<AigcPanelWrapper> {
  // 使用ValueKey避免不必要的重建
  final Key _iframeKey = ValueKey('aigc_iframe');

  @override
  Widget build(BuildContext context) {
    // 在 web 环境下，如果提供了 iframeUrl，则使用 iframe 加载
    if (PlatformTool.isWeb && widget.iframeUrl != null) {
      // 处理 URL 参数
      String url = widget.iframeUrl;
      print('Iframe 刷新了');

      // 使用固定宽度和高度，避免因布局变化导致的频繁刷新
      return Container(
        margin: EdgeInsets.only(left: 10),
        width: 375,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(10),
            topRight: Radius.circular(10),
          ),
        ),
        // 移除固定高度，而是让高度自适应
        child: IFrame(
          key: _iframeKey,
          src: url,
          width: 375,
          // 使用一个足够大的高度值
          height: MediaQuery.of(context).size.height,
        ),
      );
    } else {
      // 在非 web 环境下，或者没有提供 iframeUrl，使用原有的 AigcPanel
      return AigcPanel(widget.sceneType, widget.dailyBillDate);
    }
  }

  @override
  void didUpdateWidget(AigcPanelWrapper oldWidget) {
    super.didUpdateWidget(oldWidget);
  }
}
