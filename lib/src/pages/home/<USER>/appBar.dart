import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/kf.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/qrCode.dart';

class HomeAppbar extends StatefulWidget {
  HomeAppbar({
    this.moreTap,
    this.kfUrl,
    this.showEntryGray = false,
    this.showOpenSesame = false,
  });
  final Function moreTap;
  final String kfUrl;
  final bool showEntryGray;
  final bool showOpenSesame;
  @override
  HomeAppbarState createState() => HomeAppbarState();
}

class HomeAppbarState extends State<HomeAppbar> {
  @override
  void initState() {
    super.initState();
  }

  moreWidget() {
    return GestureDetector(
      onTap: () {
        widget.moreTap();
      },
      child: Container(
        margin: EdgeInsets.only(left: 15),
        child: Image.network(
          'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/1fafb9c3b94658f71803138ad169b82c/more.png',
          width: 24,
          height: 24,
        ),
      ),
    );
  }

  // 根据状态来渲染元素
  renderWithFlag() {
    List<Widget> list = [];
    // 客服按钮常驻
    list.add(KefuWidget(
      kfUrl: widget.kfUrl,
    ));
    // 有发票，显示三个点
    if (widget.showOpenSesame || widget.showEntryGray) {
      list.add(moreWidget());
    } else {
      list.add(QrCode());
    }
    return Row(
      children: list,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(right: 16),
      child: renderWithFlag(),
    );
  }
}
