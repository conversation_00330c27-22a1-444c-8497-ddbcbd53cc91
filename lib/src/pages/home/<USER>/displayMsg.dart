import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';

class DisplayMsg extends StatefulWidget {
  DisplayMsg({@required this.withdrawDisplayMsgModle});
  final WithdrawDisplayMsgModel withdrawDisplayMsgModle;

  @override
  State<StatefulWidget> createState() => DisplayMsgState();
}

class DisplayMsgState extends State<DisplayMsg> {
  bool display = false;
  @override
  void initState() {
    super.initState();
    display = widget.withdrawDisplayMsgModle?.display == true;
  }

  @override
  void didUpdateWidget(DisplayMsg oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return display
        ? GestureDetector(
            child: Text(widget.withdrawDisplayMsgModle.message),
            onTap: () {
              setState(() {
                display = false;
              });
            },
          )
        : SizedBox();
  }
}
