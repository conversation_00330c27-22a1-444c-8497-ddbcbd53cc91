import 'package:flutter/material.dart';

class NewbeeHint extends StatelessWidget {
  NewbeeHint({@required this.text, this.margin, this.type});
  final String text;
  final EdgeInsets margin;
  final String type;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        Container(
          width: 14,
          height: 0,
          margin: margin ?? EdgeInsets.all(0),
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(
                  color: Color(0xFFFFCC33), width: 6, style: BorderStyle.solid),
              right: BorderSide(
                  color: Colors.transparent,
                  width: 6,
                  style: BorderStyle.solid),
              left: BorderSide(
                  color: Colors.transparent,
                  width: 6,
                  style: BorderStyle.solid),
            ),
          ),
        ),
        type != null
            ? Container(
                width: 140,
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                    color: Color(0xFFFFCC33),
                    borderRadius: BorderRadius.circular(6)),
                child: Text('$text'),
              )
            : Container(
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                    color: Color(0xFFFFCC33),
                    borderRadius: BorderRadius.circular(6)),
                child: Text('$text'),
              )
      ],
    );
  }
}
