import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';

class KefuWidget extends StatelessWidget with HomeMixin {
  KefuWidget({@required this.kfUrl});
  final String kfUrl;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Image.network(
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/85916aabaf7ebfe5d64476839fb48664/kf_new.png',
        width: 24,
        height: 24,
      ),
      onTap: () async {
        RouterTools.openWebPageUrl(kfUrl);
        ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_9oe2q8ga_mc');
        ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_9oe2q8ga_mv');
      },
    );
  }
}
