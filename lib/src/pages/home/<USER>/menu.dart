import 'package:flutter/material.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/qrCode.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';
import 'package:waimai_e_native_business/waimai_e_native_business.dart';

// import '../../../tools/util/index.dart';

class Menu extends StatelessWidget with HomeMixin {
  final bool showEntryGray;
  final bool showOpenSesame;

  const Menu({Key key, this.showEntryGray, this.showOpenSesame})
      : super(key: key);

  menus(BuildContext context) {
    Widget question = Container(
        padding: EdgeInsets.only(bottom: 12),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              width: 0.5,
              color: Color(0xFF858687),
            ),
          ),
        ),
        child: Row(
          children: <Widget>[
            Image.network(
              'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/8bf980fc1036bba7d8d076274c604210/question.png',
              width: 16,
              height: 16,
            ),
            SizedBox(width: 5),
            GestureDetector(
              child: Text(
                '商户通',
                style: TextStyle(
                  color: Color(0xFFFFFFFF),
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                ),
              ),
              onTap: () {
                showDialog<bool>(
                  context: context,
                  builder: (context) {
                    return Center(
                      child: QrCodeContent(),
                    );
                  },
                );
              },
            )
          ],
        ));
    Widget receipt = GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        RouterTools.openWebPageUrl('/finance/MH5/finance/invoice/index');
        ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_9w9c2p9y_mc');
        ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_9w9c2p9y_mv');
      },
      child: showOpenSesame
          ? Container(
              margin: EdgeInsets.only(top: 12),
              child: Row(
                children: <Widget>[
                  Image.network(
                    'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/8cbbfb598a25f0f77322a6b82d394f61/receipt.png',
                    width: 16,
                    height: 16,
                  ),
                  SizedBox(width: 5),
                  Text(
                    '开发票',
                    style: TextStyle(
                      color: Color(0xFFFFFFFF),
                      fontWeight: FontWeight.w400,
                      fontSize: 14,
                    ),
                  )
                ],
              ),
            )
          : SizedBox.shrink(),
    );

    /// 跑腿开票
    goInvoicelists() async {
      final envInfo = await WaimaiENativeBusiness.getEnvironmentInfo();
      final userInfo = await WaimaiENativeBusiness.getUserInfo();

      String host = "https://peisong.meituan.com";
      if (envInfo != null) {
        if (envInfo["hostType"] == "TEST" || envInfo["hostType"] == "QA") {
          host = "https://page.banma.test.sankuai.com";
        }
        if (envInfo["hostType"] == "STAGE") {
          host = "https://page.banma.st.sankuai.com";
        }
      }
      Map<String, dynamic> param = {
        "channel": "waimaib_app",
        "accountToken": userInfo?.accessToken ?? "",
      };
      List<String> list = [];
      param.forEach((key, value) {
        list.add('$key=$value');
      });
      String paramStr = list.join('&');
      String finalUrl =
          '${host}/page/paotui2b/waimaib/v3.8.5/invoicelists.shtml?$paramStr';
      RouteUtils.open(finalUrl);
    }

    Widget preIssue = showEntryGray
        ? GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              goInvoicelists();
            },
            child: Container(
              margin: EdgeInsets.only(top: 12),
              child: Row(
                children: <Widget>[
                  Image.network(
                    'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/8cbbfb598a25f0f77322a6b82d394f61/receipt.png',
                    width: 16,
                    height: 16,
                  ),
                  SizedBox(width: 5),
                  Text(
                    '开跑腿配送费发票',
                    style: TextStyle(
                      color: Color(0xFFFFFFFF),
                      fontWeight: FontWeight.w400,
                      fontSize: 14,
                    ),
                  )
                ],
              ),
            ),
          )
        : SizedBox.shrink();
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6.5),
        color: Color(0xCC333333),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[question, receipt, preIssue],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      right: 5,
      top: 6,
      child: menus(context),
    );
  }
}
