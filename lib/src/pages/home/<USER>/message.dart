import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';

class Message extends StatelessWidget {
  Message({this.message, this.closeTap, this.display, this.url});
  final bool display;
  final String message;
  final String url;
  final Function closeTap;
  @override
  Widget build(BuildContext context) {
    return message != null && message != '' && display
        ? Container(
            padding: EdgeInsets.all(16),
            margin: EdgeInsets.fromLTRB(16, 10, 16, 0),
            decoration: BoxDecoration(
              color: Color(0xFFFFFAF1),
            ),
            child: Flex(
              direction: Axis.horizontal,
              children: <Widget>[
                Expanded(
                  flex: 1,
                  child: GestureDetector(
                    onTap: () {
                      RouterTools.openWebPageUrl(url);
                    },
                    child: Text(
                      message ?? '',
                      style: TextStyle(
                        color: Color(0xFFF89800),
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 10),
                GestureDetector(
                  child: Image.network(
                    'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/20e1c35f36776ea01b365178e0b745ff/close.png',
                    width: 14,
                    height: 14,
                  ),
                  onTap: () {
                    closeTap();
                  },
                )
              ],
            ),
          )
        : SizedBox();
  }
}
