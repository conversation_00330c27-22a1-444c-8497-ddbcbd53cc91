import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:roo_flutter/tools/response_system.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';

class MonthSelectPicker extends StatefulWidget {
  final ValueChanged<List<String>> onMonthChange;
  final initIndex;
  MonthSelectPicker({@required this.onMonthChange, @required this.initIndex});

  @override
  _MonthSelectState createState() => _MonthSelectState();
}

class _MonthSelectState extends State<MonthSelectPicker> {
  @override
  void initState() {
    super.initState();
  }

  List<String> _getMonthDesc() {
    int monthNum = 5;
    DateTime now = DateTime.now();
    List<String> res = ['全部'];
    for (int i = 1; i < monthNum; i++) {
      DateTime resDate = DateTime(now.year, now.month - (i - 1), 1);
      String resStr = DateFormat.formatMMWithMonth(resDate);
      res.add(resStr);
    }
    return res;
  }

  getMonthDuration(int index) {
    String startDate = '';
    String endDate = '';
    DateTime now = DateTime.now();
    String selectedMonthStr = '';
    if (index == 0) {
      startDate = DateFormat.formatYYYYMMDD(DateFormat.get90DayAgo());
      endDate = DateFormat.formatYYYYMMDD(now.millisecondsSinceEpoch);
      selectedMonthStr = '全部月份';
    } else {
      DateTime selectedMonth = DateTime(now.year, now.month - (index - 1), 1);
      List<String> list = DateFormat.getFirstLastDay(selectedMonth);
      startDate = list[0];
      endDate = list[1];
      selectedMonthStr = DateFormat.formatYYYYMM(selectedMonth);
    }
    return [startDate, endDate, selectedMonthStr, '$index'];
  }

  _buildTab() {
    return RooTabs(
      tabHeight: ResponsiveSystem.bothAppPc(
        runApp: 32.0,
        runPc: 36.0,
      ),
      tabMargin: EdgeInsets.only(
        right: ResponsiveSystem.bothAppPc(
          runApp: 8.0,
          runPc: 0.0,
        ),
      ),
      type: ResponsiveSystem.bothAppPc(
        runApp: RooTabsType.card,
        runPc: RooTabsType.line,
      ),
      tabBarPaddingLeft: 0,
      onIndexChange: (index) {
        List<String> res = getMonthDuration(index);
        if (widget.onMonthChange != null) {
          widget.onMonthChange(res);
        }
      },
      tabTitles: _getMonthDesc(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return _buildTab();
  }
}
