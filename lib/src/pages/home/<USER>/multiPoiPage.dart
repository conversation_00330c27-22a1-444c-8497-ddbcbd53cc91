import 'package:flutter/material.dart';
import 'package:mt_flutter_web_utils/mt_flutter_web_utils.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/empty.dart';

class MultiPoiPage extends StatelessWidget {
  MultiPoiPage(this.inGray);
  final bool inGray;
  @override
  Widget build(BuildContext context) {
    return inGray
        ? Center(
            child: Container(
            width: 1170,
            height: 800,
            color: Colors.white,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Container(
                  child: Image.network(
                    'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/ccf269f4f0606725/<EMAIL>',
                    width: 80,
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 9, bottom: 11),
                  child: Text(
                    '此功能暂不适用于全部门店，请切换单门店使用',
                    style: TextStyle(
                      fontSize: 16,
                      color: Color(0xFF222222),
                    ),
                  ),
                ),
                Text(
                  '如需查看全部门店的账单统计，可前往财务统计页面',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF666666),
                  ),
                ),
                Container(
                    margin: EdgeInsets.only(top: 14),
                    child: RooGradientButton(
                        size: Size(132, 36),
                        onPressed: () {
                          MTFlutterWebUtils.bridgeJump(
                              '/finance/web/financeStatistic');
                        },
                        child: Text(
                          '前往财务统计',
                          style: TextStyle(
                              fontSize: 14, fontWeight: FontWeight.w400),
                        )))
              ],
            ),
          ))
        : Empty(
            title: '此功能暂不适用于全部门店，请切换单门店使用',
          );
  }
}
