import 'package:flutter/material.dart';
// import 'package:mt_flutter_route/mt_flutter_route.dart';
// import 'package:mt_flutter_web_utils/mt_flutter_web_utils.dart';
// import 'package:waimai_e_fe_ffw_utils/platform_tool.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/arrow.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/textTicker.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';

class MoneyInfo extends StatefulWidget {
  MoneyInfo({Key key, @required this.simpleInfoModel}) : super(key: key);
  final SimpleInfoModel simpleInfoModel;

  @override
  MoneyInfoState createState() => MoneyInfoState();
}

/// 滚动内容区域
class MoneyInfoState extends State<MoneyInfo> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  scrollText() {
    List<TipList> tipList = widget.simpleInfoModel?.tipList;
    if (tipList.length == 0) {
      return SizedBox();
    } else {
      return TextTicker(child: tipList);
    }
  }

  isFail() {
    SimpleInfoModel simpleInfoModel = widget.simpleInfoModel;
    int withDrawStatus = simpleInfoModel?.withDrawStatus;
    // 1提现中，2提现成功，3提现失败
    if (withDrawStatus == 1 || withDrawStatus == 2) {
      return false;
    }
    return true;
  }

  getStatusText(int type) {
    String text = '';
    switch (type) {
      case 1:
        text = '处理中';
        break;
      case 2:
        text = '已到账';
        break;
      case 3:
        text = '失败!';
        break;
    }
    return text;
  }

  showNotify() {
    if (widget.simpleInfoModel != null &&
        [1, 2, 3].indexOf(widget.simpleInfoModel?.withDrawStatus) > -1 &&
        widget.simpleInfoModel.tipList.length > 0) {
      return true;
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    return showNotify()
        ? GestureDetector(
            onTap: () {
              if (widget.simpleInfoModel?.tipList[0].withDrawId > 0) {
                RouterTools.flutterPageUrl(context, '/exportDetail', params: {
                  'outId': widget.simpleInfoModel?.tipList[0].withDrawId,
                  'tipType': widget.simpleInfoModel?.tipList[0]?.tipType,
                  'from': 'flutter', // 区分 web 页面跳转，解决浏览器回退的路由跳转问题
                });
                // PlatformTools.isPC
                //     ? MTFlutterWebUtils.bridgeJump(
                //         '/finance/web/exportDetail?outId=${widget.simpleInfoModel?.tipList[0].withDrawId}')
                //     : RouteUtils.open(
                //         'https://waimaieapp.meituan.com/finance/fe/exportDetail?outId=${widget.simpleInfoModel?.tipList[0].withDrawId}');
              }
            },
            child: Container(
              padding: EdgeInsets.fromLTRB(12, 8, 12, 8),
              margin: EdgeInsets.only(top: 12),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6.5),
                color: Color(0xFFF5F6FA),
              ),
              child: Flex(
                direction: Axis.horizontal,
                children: <Widget>[
                  Image.network(
                    'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/622f3e41409c7b85bdc694247a27b93c/message.png',
                    width: 16,
                    height: 16,
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: scrollText(),
                    flex: 1,
                  ),
                  ArrowIcon(),
                ],
              ),
            ),
          )
        : SizedBox();
  }
}
