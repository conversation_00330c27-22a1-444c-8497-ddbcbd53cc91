import 'package:flutter/material.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:roo_flutter/tools/response_system.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';

class PoiBaseInfo extends StatelessWidget {
  const PoiBaseInfo({this.key, this.simpleInfoModel}) : super(key: key);

  final SimpleInfoModel simpleInfoModel;
  final Key key;

  @override
  Widget build(BuildContext context) {
    TextStyle infoStyle = TextStyle(
      color: Color(0xFF222222),
      fontSize: 12,
      height: 1.0,
      fontWeight: FontWeight.w400,
    );
    return Container(
      height: 100,
      margin: EdgeInsets.fromLTRB(
          0, ResponsiveSystem.bothAppPc(runApp: 13, runPc: 0), 0, 0),
      padding: EdgeInsets.fromLTRB(
          ResponsiveSystem.bothAppPc(runApp: 16, runPc: 30), 24, 16, 24),
      decoration: BoxDecoration(
        image: DecorationImage(
            image: NetworkImage(
              'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/18528611a0cf391e/img_banner_top.png',
            ),
            fit: BoxFit.fill),
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(6), topRight: Radius.circular(6)),
      ),
      child: Row(children: [
        Container(
          margin: EdgeInsets.only(
            right: 15,
          ),
          child: ClipRRect(
            child: Image.network(
              simpleInfoModel?.picUrl ??
                  "https://p0.meituan.net/tuling/eadbff7d82c8fef4517b95df828007905999972.png",
              width: 52,
              height: 52,
              fit: BoxFit.cover,
            ),
            borderRadius: BorderRadius.all(
              Radius.circular(6),
            ),
          ),
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            mainAxisSize: MainAxisSize.max,
            children: [
              Padding(
                padding: EdgeInsets.only(top: 2),
                child: Text(
                  simpleInfoModel?.bankAccountName ?? "",
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(
                  bottom: 4,
                ),
                child: GestureDetector(
                  child: Row(children: [
                    Image.network(
                      'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/20e6aac46d60abad856eee96b6e1193c/bank-card.png',
                      width: 16,
                      height: 16,
                    ),
                    RooTooltip(
                        target: Text(
                          '账户详情',
                          style: infoStyle.copyWith(
                            color: Color(0xFF222222),
                            backgroundColor: Color(0xFFFFE862),
                          ),
                        ),
                        tip: '点击这里修改结算信息'),
                    Icon(
                      Icons.arrow_forward_ios_rounded,
                      color: Color(0xFF222222),
                      size: 12,
                    ),
                  ]),
                  onTap: () {
                    RouterTools.flutterPageUrl(
                      context,
                      '/accountInfo',
                    );
                  },
                ),
              )
            ],
          ),
        )
      ]),
    );
  }
}
