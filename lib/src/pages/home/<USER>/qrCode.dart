import 'package:flutter/material.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:toast_widget_flutter/toast_widget_flutter.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/platformTool.dart';

// 商户通
class QrCode extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        showDialog<bool>(
            context: context,
            builder: (context) {
              return Center(
                child: QrCodeContent(),
              );
            });
      },
      child: Container(
        margin: EdgeInsets.only(left: 15),
        child: Text(
          '商户通',
          style: TextStyle(
            color: Color(0xFF222222),
            fontSize: 16,
          ),
        ),
      ),
    );
  }
}

class QrCodeContent extends StatefulWidget {
  @override
  State<QrCodeContent> createState() => _QrCodeContentState();
}

class _QrCodeContentState extends State<QrCodeContent> {
  final String qrcodeUrl =
      'https://s3plus.meituan.net/v1/mss_c4375b35f5cb4e678b5b55a48c40cf9d/staticfile/waimaie-qrcode.jpg';
  buttonSave(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      padding: EdgeInsets.fromLTRB(20, 10, 20, 10),
      height: 40,
      width: 140,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(22),
        gradient: LinearGradient(colors: [
          Color(0xFFFFE14D),
          Color(0xFFFFC34D),
        ]),
      ),
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          KNB.use('downloadImage', {"imageUrl": qrcodeUrl}).then((value) {
            if (mounted) {
              Loading.showToast(message: '图片保存成功');
              Navigator.pop(context);
            }
          }).catchError((e) {
            Loading.showToast(message: '图片保存失败');
          });
        },
        child: Text(
          '保存并关闭',
          strutStyle: PlatformTool.isWeb
              ? null
              : StrutStyle(
                  forceStrutHeight: true,
                  height: 1,
                ),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Color(0xFF222222),
          ),
        ),
      ),
    );
  }

  buttonBack(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        Navigator.pop(context);
      },
      child: Container(
        alignment: Alignment.center,
        padding: EdgeInsets.fromLTRB(20, 10, 20, 10),
        width: 140,
        height: 40,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(22),
            color: Colors.white,
            border: Border.all(color: Color(0xFF666666))),
        child: Text(
          '返回',
          strutStyle: PlatformTool.isWeb
              ? null
              : StrutStyle(
                  forceStrutHeight: true,
                  height: 1,
                ),
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Color(0xFF222222),
          ),
        ),
      ),
    );
  }

  content(BuildContext context) {
    return Container(
      width: 300,
      height: 350,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Text(
            '美团外卖商户通',
            style: TextStyle(
              color: Color(0xFF222222),
              fontWeight: FontWeight.w500,
              fontSize: 18,
            ),
          ),
          SizedBox(height: 11.5),
          Text(
            '微信扫描二维码，关注美团外卖商户通公众号，绑定账号，第一时间获取账单信息',
            style: TextStyle(
              color: Color(0xFF666666),
              fontSize: 16,
            ),
          ),
          SizedBox(height: 15),
          Image.network(
            qrcodeUrl,
            width: 170,
            height: 170,
          ),
          SizedBox(height: 15),
          Container(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                buttonBack(context),
                buttonSave(context),
              ],
            ),
          )
        ],
      ),
    );
  }

  wrapper(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Color(0x0D000000),
            offset: Offset(0, 1),
            blurRadius: 10.5,
          )
        ],
        borderRadius: BorderRadius.circular(10.5),
      ),
      child: content(context),
    );
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTextStyle(
      child: wrapper(context),
      style: TextStyle(
        fontSize: 12,
        color: Colors.black,
        decoration: TextDecoration.none,
      ),
    );
  }
}
