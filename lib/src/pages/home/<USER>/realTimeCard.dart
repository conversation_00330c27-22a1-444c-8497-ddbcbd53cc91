import 'package:flutter/material.dart';
import 'package:roo_flutter/tools/response_system.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/arrow.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/pie_chart/chart.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/pie_chart/chart_data_model.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/pie_chart/params_model.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/real_time_model.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/requests.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';

class RealTimeWCard extends StatefulWidget {
  const RealTimeWCard({@required this.cid, @required this.pageKeyInfo});
  final String cid;
  final String pageKeyInfo;

  @override
  State<RealTimeWCard> createState() => _RealTimeWCardState();
}

getFormatValue(dynamic preValue, [dynamic curValue]) {
  if (preValue == null) {
    return '--';
  }
  return curValue ?? preValue;
}

List<Color> colorList = [Color(0xFFFF8C42), Color(0xFF0E75F7)];

class _RealTimeWCardState extends State<RealTimeWCard> {
  RealTimeModel realTimeModel;
  @override
  void initState() {
    super.initState();
    getData();
  }

  double get radius => ResponsiveSystem.bothAppPc(runApp: 30.0, runPc: 50.0);

  void getData() {
    comGetApi(
      path: '/finance/waimai/dailyBill/api/todaySummary',
      params: {'businessLine': 1},
    ).then((value) {
      if (value?.data != null) {
        realTimeModel = RealTimeModel.fromJson(value.data);
        if (realTimeModel != null && realTimeModel.unFinishOrderCount > 0) {
          ReportLX.mv(widget.pageKeyInfo, widget.cid, 'b_waimai_e_vbnlzkj3_mv');
        }
        if (realTimeModel != null && realTimeModel.finishOrderCount > 0) {
          ReportLX.mv(widget.pageKeyInfo, widget.cid, 'b_waimai_e_oh9rdotx_mv');
        }
        setState(() {});
      }
    });
  }

  renderZeroMoney() {
    if (realTimeModel == null ||
        (realTimeModel.finishOrderAmountCent -
                realTimeModel.unFinishOrderAmountCent -
                0.0 ==
            0.0)) {
      return Container(
        width: radius * 2,
        child: Column(children: [
          Text('总金额', style: TextStyle(fontSize: 14, color: Color(0xFF222222))),
          SizedBox(height: 5),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text('¥', style: TextStyle(fontSize: 12)),
              Text(realTimeModel == null ? '--' : '0.0',
                  style: TextStyle(fontSize: 14, color: Color(0xFF222222))),
            ],
          )
        ]),
      );
    }
    return PieChart(
      params: PieChartParams(
        pieData: [
          ChartDataModel(
            data: realTimeModel.finishOrderAmountCent.abs(),
            color: colorList.first,
          ),
          ChartDataModel(
            data: realTimeModel.unFinishOrderAmountCent.abs(),
            color: colorList.last,
          ),
        ],
        pieRadius: radius,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // if (realTimeModel == null) return SizedBox.shrink();
    return Container(
      padding:
          EdgeInsets.all(ResponsiveSystem.bothAppPc(runApp: 0.0, runPc: 32.0)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(
          ResponsiveSystem.bothAppPc(runApp: 0.0, runPc: 8.0),
        ),
        // boxShadow: [
        //   BoxShadow(
        //     color: Color.fromRGBO(173, 173, 173, 0.10),
        //     offset: Offset(5, 20),
        //     blurRadius: 8,
        //   )
        // ]
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
            margin: EdgeInsets.only(top: 20),
            child: Text(
              '今日实时订单',
              style: TextStyle(
                fontSize: ResponsiveSystem.bothAppPc(runApp: 18.0, runPc: 26.0),
                fontWeight: FontWeight.w500,
                color: Color(0xFF222222),
              ),
            ),
          ),
          Container(
            decoration: BoxDecoration(
              color: Color(0xFFF5F6FA),
              borderRadius: BorderRadius.circular(6.5),
              // only(
              //   topRight: Radius.circular(6.5),
              //   topLeft: Radius.circular(6.5),
              // ),
            ),
            margin: EdgeInsets.only(top: 12.5),
            padding: EdgeInsets.all(16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                renderZeroMoney(),
                SizedBox(width: 16),
                ResponsiveSystem(
                    app: RealTimeCartAPP(
                        realTimeModel: realTimeModel,
                        cid: widget.cid,
                        pageKeyInfo: widget.pageKeyInfo),
                    pc: SizedBox(
                      width: 600,
                      child: RealTimeCartPC(
                        realTimeModel: realTimeModel,
                      ),
                    ))
              ],
            ),
          ),

          /// 这部分需求已经做完，但是PM又要求去掉，所以隐藏此模块，避免下次重复开发
          Visibility(
            child: ResponsiveSystem(
              app: Container(
                padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                decoration: BoxDecoration(
                  color: Color(0xFFF9FBFF),
                  borderRadius: BorderRadius.only(
                    bottomRight: Radius.circular(6.5),
                    bottomLeft: Radius.circular(6.5),
                  ),
                ),
                child: Row(
                  children: [
                    Text(
                      '预计总收入',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF666666),
                      ),
                    ),
                    Expanded(
                      child: Container(
                        alignment: Alignment.centerRight,
                        child: Text(
                          '${getFormatValue(22)}单',
                          style: TextStyle(
                            fontSize: 14,
                            color: Color(0xFF222222),
                          ),
                        ),
                      ),
                    ),
                    Container(
                      width: 120,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Text(
                            '¥${getFormatValue(123131, MoneyTool.formatMoney(3213111))}',
                            style: TextStyle(
                                fontSize: 14,
                                color: Color(0xFF222222),
                                fontWeight: FontWeight.w500),
                          ),
                          ArrowIcon(),
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ),
            visible: false,
          )
        ],
      ),
    );
  }
}

class RealTimeCartAPP extends StatelessWidget {
  RealTimeCartAPP({Key key, this.realTimeModel, this.cid, this.pageKeyInfo})
      : super(key: key);
  final RealTimeModel realTimeModel;
  final String pageKeyInfo;
  final String cid;

  Widget itemWidget(Color color, String title, int count, num money,
      {bool isSub}) {
    return Row(
      children: [
        isSub != true
            ? Container(
                height: 10,
                width: 10,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5),
                  color: color,
                ),
              )
            : DashedLine(
                axis: Axis.horizontal,
                length: 5,
              ),
        SizedBox(width: 5),
        Text(
          title,
          style: TextStyle(
            color: isSub == true ? Color(0xFF666666) : Color(0xFF222222),
            fontSize: isSub == true ? 12 : 14,
          ),
        ),
        Expanded(
          child: Container(
            alignment: Alignment.centerRight,
            child: Text(
              '${getFormatValue(count)}单',
              style: TextStyle(
                fontSize: isSub == true ? 12 : 14,
                color: isSub == true ? Color(0xFF666666) : Color(0xFF222222),
              ),
            ),
          ),
        ),
        Container(
          width: 100,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                '¥${getFormatValue(money, MoneyTool.formatMoney(money))}',
                style: TextStyle(
                    fontSize: isSub == true ? 12 : 14,
                    color:
                        isSub == true ? Color(0xFF666666) : Color(0xFF222222),
                    fontWeight: FontWeight.w500),
              ),
              isSub == true ? SizedBox(width: 12) : ArrowIcon(),
            ],
          ),
        )
      ],
    );
  }

  renderItem(Color color, String title, int count, Function onTap, num money,
      {bool showChild}) {
    return GestureDetector(
      onTap: onTap,
      child: itemWidget(color, title, count, money),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
            height: 60,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: renderItem(
                    colorList.first,
                    '已完单',
                    realTimeModel?.finishOrderCount,
                    () {
                      ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_oh9rdotx_mc');
                      RouterTools.flutterPageUrl(context, '/dailyBills',
                          params: {
                            "dailyBillDate": DateFormat.formatYYYYMMDD(
                              DateTime.now().millisecondsSinceEpoch,
                            )
                          });
                    },
                    realTimeModel?.finishOrderAmountCent,
                  ),
                ),
                SizedBox(height: 12),
                Expanded(
                  child: renderItem(
                    colorList.last,
                    '进行中',
                    realTimeModel?.unFinishOrderCount,
                    () {
                      ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_vbnlzkj3_mc');
                      RouterTools.flutterPageUrl(context, '/home', params: {
                        "tabIndex": 2,
                      });
                    },
                    realTimeModel?.unFinishOrderAmountCent,
                    showChild: true,
                  ),
                ),
              ],
            ),
          ),
          Visibility(
            visible: (realTimeModel?.unFinishOrderCount ?? 0) > 0,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                SizedBox(width: 5),
                Container(
                  child: DashedLine(axis: Axis.vertical, length: 50),
                ),
                Expanded(
                  child: Transform.translate(
                    offset: Offset(0, 8),
                    child: Column(
                      children: [
                        itemWidget(
                          colorList.last,
                          '今日下单',
                          realTimeModel?.todayUnFinishOrderCount,
                          realTimeModel?.todayUnFinishOrderAmountCent,
                          isSub: true,
                        ),
                        SizedBox(height: 10),
                        itemWidget(
                          colorList.last,
                          '历史下单',
                          realTimeModel?.historyUnFinishOrderCount,
                          realTimeModel?.historyUnFinishOrderAmountCent,
                          isSub: true,
                        ),
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
          SizedBox(height: 10),
        ],
      ),
    );
  }
}

class RealTimeCartPC extends StatelessWidget {
  RealTimeCartPC({Key key, this.realTimeModel}) : super(key: key);
  final RealTimeModel realTimeModel;

  Widget itemWidget(Color color, String title, int count, num money,
      {bool isSub}) {
    return Row(
      children: [
        isSub != true
            ? Container(
                height: 10,
                width: 10,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(5),
                  color: color,
                ),
              )
            : DashedLine(
                axis: Axis.horizontal,
                length: 5,
              ),
        SizedBox(width: 5),
        Text(
          title,
          style: TextStyle(
            color: Color(0xFF666666),
            fontSize: 12,
          ),
        ),
        Expanded(
          child: Container(
            alignment: Alignment.centerRight,
            child: Text(
              '${getFormatValue(count)}单',
              style: TextStyle(
                fontSize: 12,
                color: Color(0xFF666666),
              ),
            ),
          ),
        ),
        Container(
          width: 400,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                '¥${getFormatValue(money, MoneyTool.formatMoney(money))}',
                style: TextStyle(
                    fontSize: 12,
                    color: Color(0xFF666666),
                    fontWeight: FontWeight.w500),
              ),
              isSub == true ? SizedBox(width: 12) : ArrowIcon(),
            ],
          ),
        )
      ],
    );
  }

  renderPCItem(Color color, String title, int count, Function onTap, num money,
      {Color bgColor,
      bool showChild = false,
      int todayUnFinishOrderCount,
      num todayUnFinishOrderAmountCent,
      int historyUnFinishOrderCount,
      num historyUnFinishOrderAmountCent}) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12, horizontal: 20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(1),
        color: bgColor ?? Color(0xFFF5F6FA),
      ),
      child: GestureDetector(
          onTap: onTap,
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.stretch, children: [
            Row(
              children: [
                Container(
                  height: 10,
                  width: 10,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5), color: color),
                ),
                SizedBox(width: 5),
                Text(
                  title,
                  style: TextStyle(
                    color: Color(0xFF222222),
                    fontSize: 14,
                    height: 1,
                  ),
                ),
                Expanded(
                  child: Container(
                    alignment: Alignment.centerRight,
                    child: Text(
                      '${getFormatValue(count)}单',
                      style: TextStyle(
                        fontSize: 14,
                        height: 1,
                        color: Color(0xFF222222),
                      ),
                    ),
                  ),
                ),
                Container(
                  width: 400,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        '¥${getFormatValue(money, MoneyTool.formatMoney(money))}',
                        style: TextStyle(
                            fontSize: 14,
                            height: 1,
                            color: Color(0xFF222222),
                            fontWeight: FontWeight.w500),
                      ),
                      ArrowIcon(),
                    ],
                  ),
                )
              ],
            ),
            Visibility(
              visible: showChild,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  SizedBox(width: 5),
                  Container(
                    child: DashedLine(axis: Axis.vertical, length: 50),
                  ),
                  Expanded(
                    child: Transform.translate(
                      offset: Offset(0, 8),
                      child: Column(
                        children: [
                          itemWidget(
                            colorList.last,
                            '今日下单',
                            todayUnFinishOrderCount,
                            todayUnFinishOrderAmountCent,
                            isSub: true,
                          ),
                          SizedBox(height: 10),
                          itemWidget(
                            colorList.last,
                            '历史下单',
                            historyUnFinishOrderCount,
                            historyUnFinishOrderAmountCent,
                            isSub: true,
                          ),
                        ],
                      ),
                    ),
                  )
                ],
              ),
            )
          ])),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (realTimeModel == null) return SizedBox.shrink();
    final int allMoney = realTimeModel.unFinishOrderAmountCent ??
        0 + realTimeModel.finishOrderAmountCent ??
        0;
    final double allCount = realTimeModel.finishOrderCount ??
        0 + realTimeModel.unFinishOrderCount ??
        0;
    return Column(crossAxisAlignment: CrossAxisAlignment.stretch, children: [
      renderPCItem(
        colorList.first,
        '已完单',
        realTimeModel?.finishOrderCount,
        () {
          RouterTools.flutterPageUrl(context, '/dailyBills', params: {
            "dailyBillDate": DateFormat.formatYYYYMMDD(
              DateTime.now().millisecondsSinceEpoch,
            )
          });
        },
        realTimeModel?.finishOrderAmountCent,
      ),
      SizedBox(height: 4),
      renderPCItem(
        colorList.last,
        '进行中',
        realTimeModel?.unFinishOrderCount,
        () {
          RouterTools.flutterPageUrl(context, '/progessOrder');
        },
        realTimeModel?.unFinishOrderAmountCent,
        showChild: (realTimeModel?.unFinishOrderCount ?? 0) > 0,
        todayUnFinishOrderCount: realTimeModel?.todayUnFinishOrderCount,
        todayUnFinishOrderAmountCent:
            realTimeModel?.todayUnFinishOrderAmountCent,
        historyUnFinishOrderCount: realTimeModel?.historyUnFinishOrderCount,
        historyUnFinishOrderAmountCent:
            realTimeModel?.historyUnFinishOrderAmountCent,
      ),
      SizedBox(height: 4),

      /// 这部分需求已经做完，但是PM又要求去掉，所以隐藏此模块，避免下次重复开发
      Visibility(
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 12, horizontal: 20),
          decoration: BoxDecoration(
            color: Color(0xFFF9FBFF),
            borderRadius: BorderRadius.circular(1),
          ),
          child: Row(
            children: [
              Text(
                '预计总收入',
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF666666),
                ),
              ),
              Expanded(
                child: Container(
                  alignment: Alignment.centerRight,
                  child: Text(
                    '${getFormatValue(allCount)}单',
                    style: TextStyle(
                      fontSize: 14,
                      height: 1,
                      color: Color(0xFF222222),
                    ),
                  ),
                ),
              ),
              Container(
                width: 400,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text(
                      '¥${getFormatValue(allMoney, MoneyTool.formatMoney(allMoney))}',
                      style: TextStyle(
                        fontSize: 14,
                        height: 1,
                        color: Color(0xFF222222),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    ArrowIcon(),
                  ],
                ),
              )
            ],
          ),
        ),
        visible: false,
      )
    ]);
  }
}

class DashedLine extends StatelessWidget {
  DashedLine({Key key, this.length, this.axis}) : super(key: key);

  final double length;
  final Axis axis;

  @override
  Widget build(BuildContext context) {
    final int lineCount =
        (length / (Axis.horizontal == axis ? 2 : 5)).floor().toInt();
    List<Widget> arr = [];
    for (var i = 0; i < lineCount; i++) {
      if (axis == Axis.vertical) {
        arr.add(Container(
          height: 2.5,
          width: 1,
          color: Color(0xFF0E75F7),
          margin: EdgeInsets.only(bottom: i == lineCount - 1 ? 0 : 2.5),
        ));
      } else {
        arr.add(Container(
          height: 1,
          width: 1.5,
          color: Color(0xFF0E75F7),
          margin: EdgeInsets.only(left: i == lineCount - 1 ? 0 : 1.5),
        ));
      }
    }

    return Axis.vertical == axis
        ? Column(children: arr)
        : Row(
            children: arr,
          );
  }
}
