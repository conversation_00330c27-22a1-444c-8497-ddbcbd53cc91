class RealTimeModel {
  int finishOrderCount;
  num finishOrderAmountCent;
  int unFinishOrderCount;
  num unFinishOrderAmountCent;
  int todayUnFinishOrderCount;
  num todayUnFinishOrderAmountCent;
  int historyUnFinishOrderCount;
  num historyUnFinishOrderAmountCent;

  RealTimeModel({
    this.finishOrderCount,
    this.finishOrderAmountCent,
    this.unFinishOrderCount,
    this.unFinishOrderAmountCent,
    this.todayUnFinishOrderCount,
    this.todayUnFinishOrderAmountCent,
    this.historyUnFinishOrderCount,
    this.historyUnFinishOrderAmountCent,
  });

  RealTimeModel.fromJson(Map<String, dynamic> json) {
    finishOrderCount = json['finishOrderCount'];
    finishOrderAmountCent = json['finishOrderAmountCent'];
    unFinishOrderCount = json['unFinishOrderCount'];
    unFinishOrderAmountCent = json['unFinishOrderAmountCent'];
    todayUnFinishOrderCount = json['todayUnFinishOrderCount'];
    todayUnFinishOrderAmountCent = json['todayUnFinishOrderAmountCent'];
    historyUnFinishOrderCount = json['historyUnFinishOrderCount'];
    historyUnFinishOrderAmountCent = json['historyUnFinishOrderAmountCent'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['finishOrderCount'] = this.finishOrderCount;
    data['finishOrderAmountCent'] = this.finishOrderAmountCent;
    data['unFinishOrderCount'] = this.unFinishOrderCount;
    data['unFinishOrderAmountCent'] = this.unFinishOrderAmountCent;
    data['todayUnFinishOrderCount'] = this.todayUnFinishOrderCount;
    data['todayUnFinishOrderAmountCent'] = this.todayUnFinishOrderAmountCent;
    data['historyUnFinishOrderCount'] = this.historyUnFinishOrderCount;
    data['historyUnFinishOrderAmountCent'] =
        this.historyUnFinishOrderAmountCent;
    return data;
  }
}
