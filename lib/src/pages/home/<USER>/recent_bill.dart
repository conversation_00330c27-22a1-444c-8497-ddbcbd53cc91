import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/historyFlows.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';

/// PC页面最近余额流水记录
class RecentBillListPage extends StatelessWidget {
  const RecentBillListPage({
    this.recentBillList,
  });

  final List<HistoryFlowsModel> recentBillList;

  @override
  Widget build(BuildContext context) {
    TextStyle ts = TextStyle(fontSize: 14, color: Colors.black);
    List<Widget> timeCol = recentBillList
        .map((bill) => Padding(
              padding: EdgeInsets.only(top: 10),
              child: Text(
                bill?.flowTime ?? "",
                style: ts,
              ),
            ))
        .toList();
    List<Widget> nameCol = recentBillList
        .map((bill) => Padding(
              padding: EdgeInsets.only(top: 10),
              child: Text(
                bill?.flowTypeName ?? "",
                style: ts,
              ),
            ))
        .toList();
    List<Widget> flowCol = recentBillList.map((bill) {
      String flowInfo =
          '${MoneyTool.formatMoneyWithPrefixAndSuffix('${bill?.moneyCent ?? ""}', needPrefix: true)} / 余额${MoneyTool.formatMoneyWithPrefixAndSuffix('${bill?.balanceCent ?? ""}')}';
      return Padding(
        padding: EdgeInsets.only(top: 10),
        child: Text(
          flowInfo,
          style: ts,
        ),
      );
    }).toList();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
            padding: EdgeInsets.only(top: 0),
            child: GestureDetector(
              child: Row(
                children: [
                  Icon(
                    Icons.list_alt_rounded,
                    size: 14,
                  ),
                  Text(
                    '余额流水',
                    style: ts,
                  ),
                  Icon(
                    Icons.arrow_forward_ios_rounded,
                    color: Color(0xFF222222),
                    size: 12,
                  ),
                ],
              ),
              onTap: () {
                RouterTools.flutterPageUrl(
                  context,
                  '/accountInfo',
                );
              },
            )),
        Row(children: [
          timeCol.isNotEmpty == true
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: timeCol,
                )
              : SizedBox.shrink(),
          SizedBox(
            width: 20,
          ),
          nameCol.isNotEmpty == true
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: nameCol,
                )
              : SizedBox.shrink(),
          SizedBox(
            width: 20,
          ),
          flowCol.isNotEmpty == true
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: flowCol,
                )
              : SizedBox.shrink(),
        ])
      ],
    );
  }
}
