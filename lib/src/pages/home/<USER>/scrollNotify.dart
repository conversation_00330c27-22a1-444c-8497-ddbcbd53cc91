import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/scrollText.dart';

class ScrollNotify extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    Widget scrollText = ScrollText(
      child: Text(
        '2021-06-06 10:51:43已成功从余额提现 ¥115.68至钱包      ',
        style: TextStyle(
          color: Color(0xFF222222),
          fontWeight: FontWeight.w500,
          fontSize: 14,
        ),
      ),
    );
    return Flex(
      direction: Axis.horizontal,
      children: <Widget>[
        Image.network(
          'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/622f3e41409c7b85bdc694247a27b93c/message.png',
          width: 16,
          height: 16,
        ),
        SizedBox(width: 8),
        Expanded(
          child: scrollText,
          flex: 1,
        ),
      ],
    );
  }
}
