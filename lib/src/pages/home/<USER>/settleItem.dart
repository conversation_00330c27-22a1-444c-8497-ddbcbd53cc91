import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/arrow.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/showMore.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/enum/setttleType.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/gradient.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';

class SettleItem extends StatefulWidget {
  final SettleBill settleBill;
  final SettleTypeEnum settleType;
  final bool isFirst;

  SettleItem({
    @required this.settleBill,
    @required this.settleType,
    this.isFirst,
  });

  @override
  SettleItemState createState() => SettleItemState();
}

class SettleItemState extends State<SettleItem> with HomeMixin {
  bool isOpen = false;

  // 子项是否折叠
  bool subItemFold = true;

  @override
  void initState() {
    super.initState();
    isOpen =
        widget.settleType == SettleTypeEnum.toSettle || widget.isFirst == true;
  }

  /// 已结算下日账单数
  hasChildren() {
    if (widget.settleBill != null && widget.settleBill.dailyBills != null) {
      return widget.settleBill.dailyBills.length > 0;
    }
    return false;
  }

  // 明细项目中的一行数据
  billDetailOneRow(DailyBill dailyBill) {
    String dailyBillDate =
        DateFormat.formatYYYYMMDD(dailyBill.dailyBillDateTimestamp);
    return Container(
      padding: EdgeInsets.fromLTRB(16, 12, 16, 12),
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          RouterTools.flutterPageUrl(
            context,
            '/dailyBills',
            params: {"dailyBillDate": dailyBillDate},
          );
          if (widget.settleType == SettleTypeEnum.toSettle) {
            ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_10uzryh3_mc');
            ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_10uzryh3_mv');
          } else {
            ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_rwp8axmy_mc');
            ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_rwp8axmy_mv');
          }
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Row(
              children: <Widget>[
                Text(
                  dailyBillDate,
                  style: TextStyle(
                    color: Color(0xFF666666),
                    fontSize: 14,
                  ),
                ),
                SizedBox(width: 12),
                dailyBill.today == true
                    ? Text('今日已完单金额',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF999999),
                        ))
                    : SizedBox.shrink(),
              ],
            ),
            Row(
              children: <Widget>[
                Money(
                  size: Size.small,
                  text: MoneyTool.formatMoney(dailyBill?.dailyBillAmount),
                ),
                SizedBox(width: 4),
                ArrowIcon()
              ],
            )
          ],
        ),
      ),
    );
  }

  formateSettleDate(int settleDateTimestamp, SettleTypeEnum settleType) {
    String str = '';
    String settleDate = DateFormat.formatYYYYMMDD(settleDateTimestamp);
    switch (settleType) {
      case SettleTypeEnum.toSettle:
        str = '预计$settleDate结算';
        break;
      case SettleTypeEnum.settled:
        str = '$settleDate已汇入金额';
        break;
      default:
        str = '';
    }
    return str;
  }

  gotoDailyBillIcon() {
    if (widget.settleType == SettleTypeEnum.settled) {
      return hasChildren()
          ? ArrowIcon(
              direction: isOpen ? DirectionEnum.up : DirectionEnum.down,
            )
          : ArrowIcon(
              direction: DirectionEnum.right,
            );
    }
    return SizedBox();
  }

  // 结算日期，比如：2021/01/23 至 01/24
  getSettleDuration() {
    SettleBill settleBill = widget.settleBill;
    if (settleBill?.settleBillStartDateTimestamp != null &&
        settleBill?.settleBillEndDateTimestamp != null) {
      String startDate =
          DateFormat.formatMMDD(settleBill.settleBillStartDateTimestamp);
      String endDate =
          DateFormat.formatMMDD(settleBill.settleBillEndDateTimestamp);
      if (settleBill.settleBillStartDateTimestamp ==
          settleBill.settleBillEndDateTimestamp) {
        return startDate;
      } else {
        return '$startDate 至 $endDate';
      }
    }
    return '';
  }

  // 日期和钱数，主信息
  titleWidget() {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (widget.settleType == SettleTypeEnum.toSettle) {
          return;
        }
        this.setState(() {
          isOpen = !isOpen;
        });
      },
      child: widget.settleBill != null
          ? Container(
              padding: EdgeInsets.fromLTRB(16, 6, 20, 12),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        // 日期：10/11 至 10/13
                        // 日期 开始与结束都是同一天 展示一个日期 10/11
                        getSettleDuration(),
                        strutStyle: StrutStyle(
                          forceStrutHeight: true,
                          height: 1,
                        ),
                        style: TextStyle(
                          color: Color(0xFF222222),
                          fontWeight: FontWeight.w500,
                          fontSize: 16,
                        ),
                      ),
                      // 预计汇入金额
                      Text(
                        formateSettleDate(
                            widget.settleBill?.settleDateTimestamp,
                            widget.settleType),
                        style: TextStyle(
                          color: Color(0xFF999999),
                          fontWeight: FontWeight.w500,
                          fontSize: 12,
                        ),
                      )
                    ],
                  ),
                  Row(
                    children: <Widget>[
                      // 钱数
                      Money(
                        size: Size.big,
                        text: MoneyTool.formatMoney(
                          widget.settleBill?.totalAmount,
                        ),
                      ),
                      // 跳转日账单的每箭头
                      gotoDailyBillIcon(),
                    ],
                  )
                ],
              ),
            )
          : SizedBox(),
    );
  }

  // 每一项，待结算总共包含一项，已结算会包含多项
  _buildSettleItemWidget() {
    Widget title = titleWidget();
    List<Widget> childrenRows = [];
    List<DailyBill> list = widget?.settleBill?.dailyBills ?? [];
    if (list.length == 0) {
      childrenRows.add(SizedBox(height: 12));
    } else {
      // 数据多于2个
      int size = widget.settleType == SettleTypeEnum.toSettle ? 2 : 7;
      bool moreThan2 = list.length > size;
      if (moreThan2 &&
          subItemFold &&
          widget.settleType != SettleTypeEnum.toSettle) {
        list = list.sublist(0, size);
      }
      list.forEach((element) {
        childrenRows.add(billDetailOneRow(element));
      });
      if (moreThan2 && widget.settleType != SettleTypeEnum.toSettle) {
        childrenRows.add(
          Container(
            padding: EdgeInsets.all(12),
            child: ShowMoreWidget(
              fold: subItemFold,
              cb: () {
                setState(() {
                  subItemFold = !subItemFold;
                  list = list;
                });
              },
            ),
          ),
        );
      }
    }

    // 每一条都为一天的数据，点击会跳转到日账单
    Widget detail = hasChildren()
        ? Container(
            decoration: BoxDecoration(gradient: grayGradient),
            child: Column(children: childrenRows),
          )
        : SizedBox();
    Widget line = lineWidget();
    return Container(
      padding: EdgeInsets.fromLTRB(0, 16, 0, 0),
      child: Column(
        children: <Widget>[
          title,
          isOpen ? detail : line,
        ],
      ),
    );
  }

  lineWidget() {
    return Center(
      child: Container(
        width: 200,
        height: 0.5,
        color: Color(0xFFF5F6FA),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return _buildSettleItemWidget();
  }
}
