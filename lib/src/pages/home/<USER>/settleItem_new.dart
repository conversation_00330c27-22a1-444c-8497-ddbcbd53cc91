import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/arrow.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/enum/setttleType.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/gradient.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';

class SettleItemNew extends StatefulWidget {
  final SettleBill settleBill;
  final SettleTypeEnum settleType;
  final bool isFirst;

  SettleItemNew({
    @required this.settleBill,
    @required this.settleType,
    this.isFirst,
  });

  @override
  SettleItemNewState createState() => SettleItemNewState();
}

class SettleItemNewState extends State<SettleItemNew> with HomeMixin {
  bool isOpen = false;

  @override
  void initState() {
    super.initState();
    isOpen =
        widget.settleType == SettleTypeEnum.toSettle || widget.isFirst == true;
  }

  buildSingleItem(List<DailyBill> dailyBillList) {
    if (dailyBillList != null && dailyBillList.length > 0) {
      if (dailyBillList.length == 1) {
        return billDetailOneRow(dailyBillList[0]);
      } else {
        return titleWidget(dailyBillList);
      }
    }
    return Container();
  }

  /// 已结算下日账单数
  hasChildren() {
    if (widget.settleBill != null && widget.settleBill.dailyBills != null) {
      return widget.settleBill.dailyBills.length > 0;
    }
    return false;
  }

  /// 展开/收起
  getArrowIcon() {
    if (widget.settleType == SettleTypeEnum.settled) {
      return ArrowIcon(
        direction: isOpen ? DirectionEnum.up : DirectionEnum.down,
      );
    } else {
      return SizedBox.shrink();
    }
  }

  // 明细项目中的一行数据
  billDetailOneRow(DailyBill dailyBill) {
    String dailyBillDate =
        DateFormat.formatYYYYMMDD(dailyBill.dailyBillDateTimestamp);
    String dateStr = DateFormat.formatMMDD(dailyBill.dailyBillDateTimestamp);
    String settleDate =
        DateFormat.formatYYYYMMDD(widget?.settleBill?.settleDateTimestamp);
    return Container(
      padding: EdgeInsets.fromLTRB(16, 20, 16, 20),
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          RouterTools.flutterPageUrl(
            context,
            '/dailyBills',
            params: {"dailyBillDate": dailyBillDate},
          );
          if (widget.settleType == SettleTypeEnum.toSettle) {
            ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_10uzryh3_mc');
            ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_10uzryh3_mv');
          } else {
            ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_rwp8axmy_mc');
            ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_rwp8axmy_mv');
          }
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    Text(
                      dateStr,
                      strutStyle: StrutStyle(
                        forceStrutHeight: true,
                        height: 1,
                      ),
                      style: TextStyle(
                        color: Color(0xFF222222),
                        fontWeight: FontWeight.w500,
                        fontSize: 16,
                      ),
                    ),
                    SizedBox(width: 12),
                    dailyBill.today == true &&
                            widget.settleType == SettleTypeEnum.toSettle
                        ? Container(
                            height: 16,
                            padding: EdgeInsets.only(left: 2, right: 2),
                            decoration: BoxDecoration(
                                border: Border.all(
                                    color: Color(0xFFFF6A00), width: 0.5),
                                borderRadius: BorderRadius.circular((1.5))),
                            child: Text('今日已完单金额',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Color(0xFFFF6A00),
                                )))
                        : SizedBox.shrink(),
                  ],
                ),
                Container(
                  child: widget.settleType == SettleTypeEnum.toSettle
                      ? Text(
                          '预计${dailyBill.settleDate}结算',
                          style: TextStyle(
                            color: Color(0xFF999999),
                            fontWeight: FontWeight.w500,
                            fontSize: 12,
                          ),
                        )
                      : Text(
                          '$settleDate已汇入余额',
                          style: TextStyle(
                            color: Color(0xFF999999),
                            fontWeight: FontWeight.w500,
                            fontSize: 12,
                          ),
                        ),
                )
              ],
            ),
            Row(
              children: <Widget>[
                Money(
                  size: Size.big,
                  text: MoneyTool.formatMoney(
                    dailyBill?.dailyBillAmount,
                  ),
                ),
                SizedBox(width: 4),
                ArrowIcon()
              ],
            )
          ],
        ),
      ),
    );
  }

  // 结算日期，比如：2021/01/23 至 01/24
  getToSettleDuration(int startDateStamp, int endDateStamp) {
    if (startDateStamp != null && endDateStamp != null) {
      String startDate = DateFormat.formatMMDD(startDateStamp);
      String endDate = DateFormat.formatMMDD(endDateStamp);
      return '$startDate 至 $endDate';
    }
    return '';
  }

  getSettleDuration() {
    SettleBill settleBill = widget.settleBill;
    if (settleBill?.settleBillStartDateTimestamp != null &&
        settleBill?.settleBillEndDateTimestamp != null) {
      String startDate =
          DateFormat.formatMMDD(settleBill.settleBillStartDateTimestamp);
      String endDate =
          DateFormat.formatMMDD(settleBill.settleBillEndDateTimestamp);
      if (settleBill.settleBillStartDateTimestamp ==
          settleBill.settleBillEndDateTimestamp) {
        return startDate;
      } else {
        return '$startDate 至 $endDate';
      }
    }
    return '';
  }

  // 日期和钱数，主信息
  titleWidget(List<DailyBill> dailyBillList) {
    List<Widget> childrenRows = [];
    int totalAmount = 0;
    String toSettleDate = dailyBillList[0].settleDate;
    String settleDate =
        DateFormat.formatYYYYMMDD(widget?.settleBill?.settleDateTimestamp);

    dailyBillList.forEach((element) {
      totalAmount += element.dailyBillAmount;
      childrenRows.insert(0, subTitleRow(element));
    });

    return Column(
      children: <Widget>[
        GestureDetector(
            onTap: () {
              if (widget.settleType == SettleTypeEnum.toSettle) {
                return;
              }
              this.setState(() {
                isOpen = !isOpen;
              });
            },
            child: Container(
              padding: EdgeInsets.fromLTRB(16, 20, 20, 20),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        // 日期：10/11 至 10/13
                        // 日期 开始与结束都是同一天 展示一个日期 10/11
                        widget.settleType == SettleTypeEnum.toSettle
                            ? getToSettleDuration(
                                dailyBillList.last.dailyBillDateTimestamp,
                                dailyBillList.first.dailyBillDateTimestamp)
                            : getSettleDuration(),
                        strutStyle: StrutStyle(
                          forceStrutHeight: true,
                          height: 1,
                        ),
                        style: TextStyle(
                          color: Color(0xFF222222),
                          fontWeight: FontWeight.w500,
                          fontSize: 16,
                        ),
                      ),
                      // 预计汇入金额
                      widget.settleType == SettleTypeEnum.toSettle
                          ? Text(
                              '预计$toSettleDate结算',
                              style: TextStyle(
                                color: Color(0xFF999999),
                                fontWeight: FontWeight.w500,
                                fontSize: 12,
                              ),
                            )
                          : Text(
                              '$settleDate已汇入余额',
                              style: TextStyle(
                                color: Color(0xFF999999),
                                fontWeight: FontWeight.w500,
                                fontSize: 12,
                              ),
                            )
                    ],
                  ),
                  Row(
                    children: <Widget>[
                      // 钱数
                      Money(
                        size: Size.big,
                        text: MoneyTool.formatMoney(totalAmount),
                      ),
                      getArrowIcon()
                    ],
                  )
                ],
              ),
            )),
        isOpen
            ? Container(
                decoration: BoxDecoration(gradient: grayGradient),
                child: Column(children: childrenRows),
              )
            : SizedBox.shrink()
      ],
    );
  }

  // 每一项，待结算总共包含一项，已结算会包含多项
  _buildSettleItemWidget() {
    List<Widget> childrenRows = [];
    List<DailyBill> list = widget?.settleBill?.dailyBills ?? [];
    if (list.length == 0) {
      childrenRows.add(SizedBox(height: 12));
    } else {
      List<DailyBill> temp = [];
      List<List<DailyBill>> result = [];
      for (int i = 0; i < list.length; i++) {
        if (i == 0 || list[i].settleDate == list[i - 1].settleDate) {
          temp.add(list[i]);
        } else {
          result.add(temp);
          temp = [list[i]];
        }
      }
      result.add(temp);
      result.forEach((element) {
        childrenRows.add(buildSingleItem(element));
      });
    }

    Widget detail = Container(
      child: Column(children: childrenRows),
    );
    return Container(
      padding: EdgeInsets.fromLTRB(0, 16, 0, 0),
      child: Column(
        children: <Widget>[detail],
      ),
    );
  }

  subTitleRow(DailyBill dailyBill) {
    String dailyBillDate =
        DateFormat.formatYYYYMMDD(dailyBill.dailyBillDateTimestamp);
    return Container(
      padding: EdgeInsets.fromLTRB(16, 12, 16, 12),
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          RouterTools.flutterPageUrl(
            context,
            '/dailyBills',
            params: {"dailyBillDate": dailyBillDate},
          );
          if (widget.settleType == SettleTypeEnum.toSettle) {
            ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_10uzryh3_mc');
            ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_10uzryh3_mv');
          } else {
            ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_rwp8axmy_mc');
            ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_rwp8axmy_mv');
          }
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Row(
              children: <Widget>[
                Text(
                  dailyBillDate,
                  style: TextStyle(
                    color: Color(0xFF666666),
                    fontSize: 14,
                  ),
                ),
                SizedBox(width: 12),
                dailyBill.today == true
                    ? Container(
                        // width: 88,
                        // height: 16,
                        padding: EdgeInsets.only(
                            left: 2, right: 2, top: 1, bottom: 1),
                        decoration: BoxDecoration(
                            border: Border.all(
                                color: Color(0xFFFF6A00), width: 0.5),
                            borderRadius: BorderRadius.circular((1.5))),
                        child: Text('今日已完单金额',
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFFFF6A00),
                            )))
                    : SizedBox.shrink(),
              ],
            ),
            Row(
              children: <Widget>[
                Money(
                  size: Size.small,
                  text: MoneyTool.formatMoney(dailyBill?.dailyBillAmount),
                ),
                SizedBox(width: 4),
                ArrowIcon()
              ],
            )
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return _buildSettleItemWidget();
  }
}
