import 'package:flutter/material.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:roo_flutter/tools/response_system.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/noMoreDate.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/settleItem.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/settleItem_new.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/settle_item_pc.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/settle_item_pc_new.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/enum/setttleType.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/tools/gradient.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/platformTool.dart';

// import '../utils/settleListUtils.dart';

class SettleBillWidget extends StatefulWidget {
  SettleBillWidget({
    @required this.settlingBill,
    @required this.settledBillList,
    this.pageNo,
    this.totalPage,
    this.onPageChange,
    this.isDelay,
  });

  // 待结算账单
  final SettleBill settlingBill;
  // 已结算账单
  final List<SettleBill> settledBillList;

  final int pageNo;
  final int totalPage;
  final ValueChanged<int> onPageChange;
  final int isDelay;

  @override
  SettleBillWidgetState createState() => SettleBillWidgetState();
}

class SettleBillWidgetState extends State<SettleBillWidget> {
  List<String> resList = [];

  @override
  void initState() {
    super.initState();
    if (PlatformTool.isWeb) {
      /// pc先不做引导 结算账单引导
      // _showSettleGuideOverlay();
    }
  }

  getTypeText(SettleTypeEnum settleType) {
    String text = '';
    if (settleType == SettleTypeEnum.settled) {
      text = '已结算';
    } else if (settleType == SettleTypeEnum.toSettle) {
      text = '待结算';
    }
    return text;
  }

  // 图标+已结算/待结算
  _buildBillTitle(SettleTypeEnum settleType) {
    return Container(
        padding: EdgeInsets.fromLTRB(16, 16, 16, 0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Row(
              children: <Widget>[
                Image.network(
                  '${settleType == SettleTypeEnum.settled ? 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/bf9d63a242e4a36c552dbc868767129b/settled.png' : 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/e26d4194653e3ad78803f7b51bf9b314/to-settle.png'}',
                  width: 16,
                  height: 16,
                ),
                SizedBox(width: 4),
                Text(
                  getTypeText(settleType),
                  strutStyle: PlatformTool.isWeb
                      ? null
                      : StrutStyle(
                          forceStrutHeight: true,
                          height: 1,
                        ),
                  style: TextStyle(
                    color: Color(0xFF222222),
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                SizedBox(width: 5),
              ],
            ),
          ],
        ));
  }

  // widget包裹区
  wrapperWidget(Widget settleWidget) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Color(0x0D000000),
            offset: Offset(0, 1),
            blurRadius: 10.5,
          )
        ],
        borderRadius: BorderRadius.circular(10.5),
      ),
      child: settleWidget,
    );
  }

  // 待结算
  settlingWidget() {
    Widget header = _buildBillTitle(SettleTypeEnum.toSettle);
    Widget item = _buildToSettleTableForApp();
    Widget full = Container(
      child: Column(
        children: <Widget>[
          header,
          item,
        ],
      ),
    );
    return wrapperWidget(full);
  }

  // 已结算
  settledWidget() {
    Widget header = _buildBillTitle(SettleTypeEnum.settled);
    List<Widget> listWidget = [];
    List<SettleBill> settledList = widget.settledBillList ?? [];
    settledList.asMap().forEach((index, value) {
      widget.isDelay != null && widget.isDelay == 1
          ? listWidget.add(SettleItemNew(
              settleType: SettleTypeEnum.settled,
              settleBill: value,
              isFirst: index == 0,
            ))
          : listWidget.add(SettleItem(
              settleType: SettleTypeEnum.settled,
              settleBill: value,
              isFirst: index == 0,
            ));
    });
    if (settledList.length == 0) {
      listWidget.add(SizedBox(height: 16));
    }
    Widget full = Container(
      child: Column(
        children: <Widget>[
          header,
          isNoData()
              ? noDataWidget()
              : Column(
                  children: listWidget,
                ),
        ],
      ),
    );
    return wrapperWidget(full);
  }

  isNoData() {
    if (widget.settledBillList?.length == 0 && widget.settlingBill == null) {
      return true;
    }
    return false;
  }

  noDataWidget() {
    return Container(
      padding: EdgeInsets.all(40),
      child: Center(
        child: Text(
          '暂无交易数据',
          style: TextStyle(
            color: Color(0xFF222222),
            fontWeight: FontWeight.w500,
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  _buildPCTableList() {
    return Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
          color: Colors.white,
        ),
        padding: EdgeInsets.all(14),
        child: Column(
          children: [
            _buildBillTitle(SettleTypeEnum.toSettle),
            _buildToSettleTable(),
            _buildBillTitle(SettleTypeEnum.settled),
            _buildSettledTable(),
            widget?.totalPage != null && widget.totalPage > 1
                ? Padding(
                    padding: EdgeInsets.only(
                      top: 20,
                    ),
                    child: RooPagination(
                      onPageChanged: (int currentPage) async {
                        if (widget?.onPageChange != null) {
                          widget.onPageChange(currentPage);
                        }
                      },
                      currentPage: widget.pageNo,
                      totalPage: widget.totalPage,
                    ),
                  )
                : SizedBox.shrink(),
          ],
        ));
  }

  _buildToSettleTableForApp() {
    return widget.isDelay != null && widget.isDelay == 1
        ? SettleItemNew(
            settleType: SettleTypeEnum.toSettle,
            settleBill: widget.settlingBill,
          )
        : SettleItem(
            settleType: SettleTypeEnum.toSettle,
            settleBill: widget.settlingBill,
          );
  }

  _buildToSettleTable() {
    return widget.isDelay != null && widget.isDelay == 1
        ? SettleItemPCNew(
            settleBillList: [widget.settlingBill],
            settleType: SettleTypeEnum.toSettle,
          )
        : SettleItemPC(
            settleBillList: [widget.settlingBill],
            settleType: SettleTypeEnum.toSettle,
          );
  }

  _buildSettledTable() {
    return widget.isDelay != null && widget.isDelay == 1
        ? SettleItemPCNew(
            settleBillList: widget.settledBillList,
            settleType: SettleTypeEnum.settled,
          )
        : SettleItemPC(
            settleBillList: widget.settledBillList,
            settleType: SettleTypeEnum.settled,
          );
  }

  @override
  Widget build(BuildContext context) {
    return ResponsiveSystem(
      app: Container(
        decoration: BoxDecoration(
          gradient: grayGradient,
        ),
        child: Column(
          children: <Widget>[
            // 待结算
            settlingWidget(),
            // 已结算
            settledWidget(),
            // 电脑端展示文案
            ResponsiveSystem(
              app: NoMoreDataWidget(),
            ),
          ],
        ),
      ),
      pc: _buildPCTableList(),
    );
  }
}
