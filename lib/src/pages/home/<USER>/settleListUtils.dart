import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/util/index.dart';

class SettleListUtils {
  static void tryPlaySettleListStatisticAsg() {
    Util.getPoiId().then((values) {
      KNB
          .getStorage(key: '${values}asg__waimai_e__default_202413153549')
          .then((flag) {
        String value = flag['value'];
        if (value == null || value.isEmpty) {
          playSettleListStatisticAsg();
        }
        return Future.value(null);
      });
    });
  }

  static void playSettleListStatisticAsg() {
    Util.getPoiId().then((values) {
      KNB.use('asg.playASG', {
        'asgId': 'asg__waimai_e__default_202413153549',
        'max_play_count': -1
      }).then((value) {
        print('play asg__waimai_e__default_202413153549 result: ' +
            value.toString());
        KNB.setStorage(
            key: '${values}asg__waimai_e__default_202413153549',
            value: "true",
            level: 1);
      }).catchError((error) {
        print('play asg__waimai_e__default_20231013140056 fail !');
      });
    });
  }
}
