import 'package:flutter/material.dart';
import 'package:mt_flutter_web_utils/mt_flutter_web_utils.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:roo_flutter/tools/response_system.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/datePicker.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/question_icon.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/radio.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/monthSelect.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';

// typedef void RadioCallback(bool checked);
typedef void RadioCallback();

class SettlePeriod extends StatefulWidget {
  SettlePeriod({
    @required this.simpleInfoModel,
    @required this.cb,
    @required this.filterData,
    @required this.onMonthSelected,
    @required this.showBillFilter,
    this.isDelay,
    this.delayNum,
    this.isIncrementDelay,
  });
  final SimpleInfoModel simpleInfoModel;
  final RadioCallback cb;
  final bool filterData;
  final ValueChanged<List<String>> onMonthSelected;
  // 吸顶展示的时候不展示时间筛选模块，否则会不统一
  final bool showBillFilter;
  final int isDelay;
  final int delayNum;
  final int isIncrementDelay;

  @override
  SettlePeriodState createState() => SettlePeriodState();
}

class SettlePeriodState extends State<SettlePeriod> with HomeMixin {
  // 查日账单是否选中
  bool tapDailyBill = false;

  int selectedMonthIndex = 0;

  @override
  void initState() {
    super.initState();
  }

  queryButton(String text, Function cb, {bool isSelected}) {
    return GestureDetector(
      onTap: () {
        cb();
      },
      child: Text(
        text ?? '',
        style: TextStyle(
            color: isSelected == true ? Color(0xFFFF6A00) : Color(0xFF666666),
            fontSize: 12,
            fontWeight: isSelected == true ? FontWeight.w500 : FontWeight.w400),
      ),
    );
  }

  blank() {
    return Container(
      width: 24.5,
      child: Center(
        child: Container(
          width: 0.5,
          height: 7,
          color: Color(0xFFCCCCCC),
        ),
      ),
    );
  }

  queryButtons() {
    return Row(
      children: <Widget>[
        queryButton('查日账单', () async {
          tapDailyBill = true;
          setState(() {});
          DateTime curDate = DateTime.now();
          if (!mounted) {
            return;
          }
          final DateTime pickedDate = await showCustomDatePicker(
            context: context,
            initialDate: DateTime.now(),
            firstDate: DateTime(curDate.year, curDate.month, curDate.day - 90),
            lastDate: DateTime.now(),
          );
          setState(() {
            tapDailyBill = false;
          });
          if (pickedDate != null && context != null) {
            String dailyBillDate =
                DateFormat.formatYYYYMMDD(pickedDate.millisecondsSinceEpoch);
            RouterTools.flutterPageUrl(
              context,
              '/dailyBills',
              params: {"dailyBillDate": dailyBillDate},
            );
          }
        }, isSelected: tapDailyBill),
        blank(),
        queryButton('查订单', () {
          RouterTools.flutterPageUrl(context, '/orderQuery');
          ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_e8sutnxf_mc');
        }),
        blank(),
        queryButton('查看月度统计', () {
          RouterTools.flutterPageUrl(context, '/monthBills');
        }),
      ],
    );
  }

  // 账单，以及按钮
  _buildRow1() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: <Widget>[
        // 左侧
        Text(
          '账单',
          style: TextStyle(
            color: Color(0xFF222222),
            fontWeight: FontWeight.w600,
            fontSize: 22,
          ),
        ),
        // 右侧
        queryButtons(),
      ],
    );
  }

  _buildFilterDataWidget() {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        widget?.cb();
      },
      child: Row(
        children: <Widget>[
          CustomRadio(
            checked: widget.filterData,
          ),
          SizedBox(width: 6),
          Text(
            '仅显示有交易账期',
            strutStyle: StrutStyle(
              forceStrutHeight: true,
              height: 1,
            ),
            style: TextStyle(
              color: Color(0xFF666666),
              fontWeight: FontWeight.w400,
              fontSize: 14,
            ),
          )
        ],
      ),
    );
  }

  _buildRow2() {
    String settlePeriodName = widget.simpleInfoModel?.settlePeriodName ?? '';
    int isDelay = widget.isDelay != null ? widget.isDelay : 0;
    int isIncrementDelay =
        widget.isIncrementDelay != null ? widget.isIncrementDelay : 0;
    int delayNum = widget.delayNum ?? 0;
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: <Widget>[
        isDelay != 1
            ? Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                    Text(
                      settlePeriodName ?? '',
                      strutStyle: StrutStyle(forceStrutHeight: true, height: 1),
                      style: TextStyle(
                        color: Color(0xFF999999),
                        fontWeight: FontWeight.w400,
                        fontSize: ResponsiveSystem.bothAppPc(
                          runApp: 12.0,
                          runPc: 14.0,
                        ),
                      ),
                    ),
                    settlePeriodName != ''
                        ? RooTooltip(
                            lineWordCount: 20,
                            target: QuestionIcon(),
                            title: '结算周期说明',
                            tip:
                                '是指商家在平台入驻后，按结算周期给商家支付结算款项的时间周期。以结算周期为一个计量单位，从商家结算设置签约开始系统自动结算账期。例如，商户设置的结算周期为3天，则1月1号至1月3号期间产生的所有账单收入，会在1月4号统一结算给商家。')
                        : SizedBox.shrink(),
                  ])
            : Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                    isIncrementDelay == 1
                        ? Container(
                            margin: EdgeInsets.only(right: 3),
                            child: RooSquareBadge(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(14.0)),
                              badgeContent: Text('更新',
                                  style: TextStyle(
                                      color: Colors.white, fontSize: 11),
                                  textAlign: TextAlign.center),
                            ),
                          )
                        : SizedBox.shrink(),
                    Text(
                      delayNum != null ? '结算账期${delayNum}天' : '',
                      strutStyle: StrutStyle(forceStrutHeight: true, height: 1),
                      style: TextStyle(
                        color: Color(0xFF999999),
                        fontWeight: FontWeight.w400,
                        fontSize: ResponsiveSystem.bothAppPc(
                          runApp: 12.0,
                          runPc: 14.0,
                        ),
                      ),
                    ),
                    delayNum != null
                        ? RooTooltip(
                            lineWordCount: 20,
                            target: QuestionIcon(),
                            title: '滚动结算账期说明',
                            tip:
                                '是指订单完成后资金结算到账户余额的周期。例如，商户设置的结算账期为3天，则1月1日的账单收入会在三个自然日之后，即1月4日统一结算给商家。')
                        : SizedBox.shrink(),
                  ]),
        ResponsiveSystem(
          app: _buildFilterDataWidget(),
          pc: SizedBox.shrink(),
        )
      ],
    );
  }

  Widget _buildTimeFilter() {
    return MonthSelectPicker(
      onMonthChange: (List<String> rList) {
        if (rList != null && rList.length == 4) {
          selectedMonthIndex = int.parse(rList[3]);
          if (widget.onMonthSelected != null) {
            widget.onMonthSelected(rList);
          }
        }
      },
      initIndex: selectedMonthIndex,
    );
  }

  _buildPCBillHeader() {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(6),
        color: Colors.white,
      ),
      margin: EdgeInsets.only(
        top: 20,
        bottom: 10,
      ),
      padding: EdgeInsets.only(top: 30, left: 15, right: 30, bottom: 0),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Padding(
          padding: EdgeInsets.only(
            left: 15,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Text(
                    '账单信息',
                    style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.w600,
                        color: Colors.black),
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: 15),
                    child: _buildRow2(),
                  ),
                ],
              ),
              RooButtonAdapted(
                  onClick: () {
                    MTFlutterWebUtils.bridgeJump(
                        '/finance/static/html_pc/billReconciliation.html#/settle-bill-list');
                  },
                  text: '全部账单')
            ],
          ),
        ),
        SizedBox(
          height: 8,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            _buildTimeFilter(),
            _buildFilterDataWidget(),
          ],
        )
      ]),
    );
  }

  @override
  Widget build(BuildContext context) {
    bool isAtTop = widget.showBillFilter == true;
    return ResponsiveSystem(
      app: Container(
        padding: EdgeInsets.fromLTRB(0, 16, 0, 0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            _buildRow1(),
            SizedBox(height: 10),
            _buildRow2(),
            SizedBox(
              height: isAtTop ? 10 : 0,
            ),
            isAtTop ? SizedBox.shrink() : _buildTimeFilter()
          ],
        ),
      ),
      pc: _buildPCBillHeader(),
    );
  }
}
