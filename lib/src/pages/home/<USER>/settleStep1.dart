import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/gradientButton.dart';
import 'settleSteps.dart';

class SettleStep1 extends StatelessWidget {
  SettleStep1({this.callback});
  final StepCallback callback;

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: <Widget>[
        Positioned(
          left: 12,
          top: 770,
          child: Container(
            width: 990,
            height: 60,
            padding: EdgeInsets.only(left: 14),
            decoration: BoxDecoration(
                gradient: LinearGradient(colors: [
                  Color(0xFFFFFFFF),
                  Color(0xFFFFFFFF),
                ]),
                borderRadius: BorderRadius.circular(10)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: <Widget>[
                Image.network(
                  'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/bf9d63a242e4a36c552dbc868767129b/settled.png',
                  width: 16,
                  height: 16,
                ),
                SizedBox(width: 4),
                Text(
                  '已结算',
                  style: TextStyle(
                    color: Color(0xFF222222),
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ),
        Positioned(
            left: 35,
            top: 620,
            child: Column(
              children: [
                Text(
                  '结算方案更新',
                  style: TextStyle(color: Color(0xFFFFFFFF)),
                ),
                Text('之前的周期结算方案更新为滚动结算，每日的金额都将在3日后结算',
                    style: TextStyle(color: Color(0xFFFFFFFF))),
                GradientButton(
                  text: '下一步(1/2)',
                  width: 174,
                  height: 40,
                  onTap: () {
                    this.callback(2);
                  },
                )
              ],
            )),
      ],
    );
  }
}
