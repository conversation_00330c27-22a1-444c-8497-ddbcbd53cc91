import 'package:flutter/material.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/gradientButton.dart';
import 'settleSteps.dart';

class SettleStep2 extends StatelessWidget {
  SettleStep2({this.callback});
  final StepCallback callback;

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: <Widget>[
        Positioned(
          left: 12,
          top: 770,
          child: Container(
            width: 990,
            height: 60,
            padding: EdgeInsets.only(left: 14),
            decoration: BoxDecoration(
                gradient: LinearGradient(colors: [
                  Color(0xFFFFFFFF),
                  Color(0xFFFFFFFF),
                ]),
                borderRadius: BorderRadius.circular(10)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: <Widget>[
                Image.network(
                  'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/bf9d63a242e4a36c552dbc868767129b/settled.png',
                  width: 16,
                  height: 16,
                ),
                SizedBox(width: 4),
                Text(
                  '待结算',
                  style: TextStyle(
                    color: Color(0xFF222222),
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ),
        Positioned(
            left: 35,
            top: 620,
            child: Column(
              children: [
                Text(
                  '流水入账规则更新',
                  style: TextStyle(color: Color(0xFFFFFFFF)),
                ),
                Text('流水按订单下单时间汇总结算。待结算日期显示金额为实时统计，后续实际打款金额可能变化',
                    style: TextStyle(color: Color(0xFFFFFFFF))),
                GradientButton(
                  text: '知道了(2/2)',
                  width: 174,
                  height: 40,
                  onTap: () {
                    callback(-1);
                    Navigator.pop(context);
                    KNB.setStorage(
                        key: 'Finance_StoreSettleGuide', value: '1', level: 1);
                  },
                )
              ],
            )),
      ],
    );
  }
}
