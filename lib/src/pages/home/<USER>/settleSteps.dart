import 'package:flutter/material.dart';
import 'settleStep1.dart';
import 'settleStep2.dart';

typedef void StepCallback(int step);

class SettleSteps extends StatefulWidget {
  SettleSteps({String pageName, this.params})
      : this.pageName =
            pageName ?? (params != null ? params['mtf_page'].toString() : '');
  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  SettleStepsState createState() => SettleStepsState();
}

class SettleStepsState extends State<SettleSteps> {
  int curStep = 1;
  StepCallback callback;

  _callback(int step) {
    setState(() {
      curStep = step ?? 1;
    });
  }

  _buildStep() {
    switch (curStep) {
      case 1:
        return SettleStep1(callback: this._callback);
      case 2:
        return SettleStep2(callback: this._callback);
      case 3:
      default:
        return SettleStep2(callback: this._callback);
    }
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTextStyle(
      style: TextStyle(
        fontWeight: FontWeight.w400,
        decoration: TextDecoration.none,
        color: Colors.black,
      ),
      child: Container(
        child: _buildStep(),
        color: Color.fromRGBO(0, 0, 0, .2),
      ),
    );
  }
}
