import 'package:flutter/material.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/empty.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/question.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/home.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/enum/setttleType.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';

TextStyle jumpTextStyle = TextStyle(
    color: Color(0xFFFF6A00), fontSize: 14, fontWeight: FontWeight.w400);
TextStyle headerTextStyle = TextStyle(
    color: Color(0xFF222222), fontSize: 14, fontWeight: FontWeight.w500);
Color billTableColor = Color(0x4DCCCCCC);

double defaultTableWidth = 200;
double billInfoWidth = 210;

class SettleItemPC extends StatelessWidget {
  final List<SettleBill> settleBillList;
  final SettleTypeEnum settleType;

  SettleItemPC({
    this.settleType,
    this.settleBillList,
  });

  Widget _buildTableRowTemplate(
      Widget firstCol, Widget secondWidget, Widget thirdWidget) {
    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Container(
          width: defaultTableWidth,
          padding: EdgeInsets.only(
            left: 45,
          ),
          alignment: Alignment.centerLeft,
          child: firstCol,
        ),
        Container(
            width: defaultTableWidth,
            alignment: Alignment.centerRight,
            child: secondWidget),
        Container(
          width: 180,
          alignment: Alignment.center,
          child: thirdWidget,
        ),
      ],
    );
  }

  Widget _buildToSee(DailyBill dailyBill, BuildContext context) {
    return GestureDetector(
      child: Text(
        '查看',
        style: jumpTextStyle,
      ),
      onTap: () {
        RouterTools.flutterPageUrl(context, '/dailyBills', params: {
          "dailyBillDate":
              DateFormat.changeDateSplitChar(dailyBill.dailyBillDate)
        });
      },
    );
  }

  Widget _buildToDownload(DailyBill dailyBill) {
    return GestureDetector(
      child: Text(
        '下载',
        style: jumpTextStyle,
      ),
      onTap: () {
        Map params = {
          'beginDate': DateFormat.changeSplitChar(dailyBill.dailyBillDate),
          'endDate': DateFormat.changeSplitChar(dailyBill.dailyBillDate),
        };
        createBillExportTask(params).then((value) {
          if (value == true) {
            Loading.showToast(message: '下载成功，请到下载专区下载');
          } else {
            Loading.showToast(message: '下载异常，请稍后重试');
          }
        }).catchError((error) {});
      },
    );
  }

  Widget _buildTableOneRow(DailyBill dailyBill, BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: 10,
      ),
      child: _buildTableRowTemplate(
        Text(
            '${dailyBill.dailyBillDate} ${DateFormat.getDay(dailyBill.dailyBillDate)}'),
        Text('${MoneyTool.formatMoneyWithPrex(dailyBill.dailyBillAmount)}'),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            _buildToSee(dailyBill, context),
            Container(
              margin: EdgeInsets.symmetric(
                horizontal: 6,
              ),
              width: 1,
              height: 12,
              color: Colors.grey,
            ),
            _buildToDownload(dailyBill),
          ],
        ),
      ),
    );
  }

  /// 渲染一个账期内容
  Widget _buildOneSettleBill(
      SettleBill settleBill, int index, BuildContext context) {
    if (settleBill == null || settleBill?.dailyBills == null) {
      return SizedBox.shrink();
    }
    List<Widget> allDaliyBill = settleBill.dailyBills
        .map((e) => _buildTableOneRow(e, context))
        .toList();
    Widget settleInfo = IntrinsicHeight(
      child: Container(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.max,
          children: [
            Text(
              '${MoneyTool.formatMoneyNoPrex(settleBill?.totalAmount ?? 0)}',
              style: TextStyle(
                fontSize: 26,
                fontWeight: FontWeight.w700,
                color: Color(0xFF222222),
              ),
            ),
            Text(
                '${settleBill.settleBillStartDate}至${settleBill.settleBillEndDate}'),
            Text(settleType == SettleTypeEnum.toSettle
                ? '预计${settleBill.settleDate}入账'
                : '已汇入余额'),
          ],
        ),
        width: billInfoWidth,
      ),
    );
    return Container(
      decoration: index > 0
          ? BoxDecoration(
              border: Border(
                top: BorderSide(
                  color: billTableColor,
                ),
              ),
            )
          : null,
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          settleInfo,
          Expanded(
              child: Container(
            child: allDaliyBill.isNotEmpty == true
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: allDaliyBill,
                  )
                : SizedBox.shrink(),
            decoration: BoxDecoration(
              border: Border(
                left: BorderSide(
                  color: billTableColor,
                  width: 1,
                ),
              ),
            ),
          )),
        ],
      ),
      padding: EdgeInsets.symmetric(
        horizontal: 20,
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: 10,
        horizontal: 20,
      ),
      color: Color(0x66EEEEEE),
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Container(
          width: billInfoWidth,
          child: Text(
            '账单信息',
            style: headerTextStyle,
          ),
        ),
        Expanded(
          child: _buildTableRowTemplate(
            Text(
              '账单时间',
              style: headerTextStyle,
            ),
            settleType == SettleTypeEnum.toSettle
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        '实时账单金额',
                        style: headerTextStyle,
                      ),
                      SizedBox(
                        width: 2,
                      ),
                      RooTooltip(
                        target: Question(isDark: true),
                        tip: '待结算日期显示金额为实时统计后，后续实际打款金额可能变化',
                      ),
                    ],
                  )
                : Text(
                    '账单金额',
                    style: headerTextStyle,
                  ),
            Text(
              '操作',
              style: headerTextStyle,
            ),
          ),
        ),
      ]),
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget tableHeader = _buildHeader();
    Widget emptyTip = Empty(
      title: '暂无数据',
      margin: EdgeInsets.symmetric(
        vertical: 30,
      ),
    );
    bool hasBill = settleBillList != null && settleBillList.length > 0;
    List<Widget> tableBody = [];
    if (hasBill) {
      for (int i = 0; i < settleBillList.length; i++) {
        tableBody.add(_buildOneSettleBill(settleBillList[i], i, context));
      }
    }

    return Container(
        margin: EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 20,
        ),
        decoration: BoxDecoration(
          border: Border.all(color: billTableColor),
          borderRadius: BorderRadius.all(
            Radius.circular(
              8,
            ),
          ),
        ),
        child: Column(
          children: hasBill
              ? [
                  tableHeader,
                  ...tableBody,
                ]
              : [
                  tableHeader,
                  emptyTip,
                ],
        ));
  }
}
