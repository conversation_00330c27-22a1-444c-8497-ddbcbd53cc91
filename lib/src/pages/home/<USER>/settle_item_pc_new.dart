import 'package:flutter/material.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/empty.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/home.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/enum/setttleType.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';

TextStyle jumpTextStyles = TextStyle(
    color: Color(0xFFFF6A00), fontSize: 14, fontWeight: FontWeight.w400);
TextStyle headerTextStyles = TextStyle(
    color: Color(0xFF222222), fontSize: 14, fontWeight: FontWeight.w500);
Color billTableColors = Color(0x4DCCCCCC);

double defaultTableWidths = 200;
double billInfoWidths = 210;

class SettleItemPCNew extends StatefulWidget {
  final List<SettleBill> settleBillList;
  final SettleTypeEnum settleType;

  SettleItemPCNew({
    this.settleType,
    this.settleBillList,
  });
  @override
  SettleItemPCNewState createState() => SettleItemPCNewState();
}

class SettleItemPCNewState extends State<SettleItemPCNew> {
  Map<int, bool> isOpenMap = {}; // 存储每个账单项的展开状态

  // bool isOpen = false;

  @override
  void initState() {
    super.initState();
  }

  Widget _buildTableRowTemplate(Widget firstCol, Widget secondWidget,
      Widget thirdWidget, Widget fourWidget) {
    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        Container(
          width: 260,
          padding: EdgeInsets.only(
            left: 45,
          ),
          alignment: Alignment.centerLeft,
          child: firstCol,
        ),
        Container(
            width: defaultTableWidths,
            alignment: Alignment.centerRight,
            child: secondWidget),
        Container(
            width: defaultTableWidths,
            alignment: Alignment.centerLeft,
            child: thirdWidget),
        Container(
          width: 180,
          alignment: Alignment.center,
          child: fourWidget,
        ),
      ],
    );
  }

  Widget _buildToSee(DailyBill dailyBill, BuildContext context) {
    return GestureDetector(
      child: Text(
        '查看',
        style: jumpTextStyles,
      ),
      onTap: () {
        RouterTools.flutterPageUrl(context, '/dailyBills', params: {
          "dailyBillDate":
              DateFormat.changeDateSplitChar(dailyBill.dailyBillDate)
        });
      },
    );
  }

  Widget _buildToDownload(DailyBill dailyBill) {
    return GestureDetector(
      child: Text(
        '下载',
        style: jumpTextStyles,
      ),
      onTap: () {
        Map params = {
          'beginDate': DateFormat.changeSplitChar(dailyBill.dailyBillDate),
          'endDate': DateFormat.changeSplitChar(dailyBill.dailyBillDate),
        };
        createBillExportTask(params).then((value) {
          if (value == true) {
            Loading.showToast(message: '下载成功，请到下载专区下载');
          } else {
            Loading.showToast(message: '下载异常，请稍后重试');
          }
        }).catchError((error) {});
      },
    );
  }

  _buildToSeeOne(SettleBill settleBill, BuildContext context) {
    return GestureDetector(
      child: Text(
        '查看',
        style: jumpTextStyles,
      ),
      onTap: () {
        RouterTools.flutterPageUrl(context, '/dailyBills', params: {
          "dailyBillDate":
              DateFormat.changeDateSplitChar(settleBill.settleBillStartDate)
        });
      },
    );
  }

  _buildToDownloadOne(SettleBill settleBill) {
    return GestureDetector(
      child: Text(
        '下载',
        style: jumpTextStyles,
      ),
      onTap: () {
        Map params = {
          'beginDate':
              DateFormat.changeSplitChar(settleBill.settleBillStartDate),
          'endDate': DateFormat.changeSplitChar(settleBill.settleBillEndDate),
        };
        createBillExportTask(params).then((value) {
          if (value == true) {
            Loading.showToast(message: '下载成功，请到下载专区下载');
          } else {
            Loading.showToast(message: '下载异常，请稍后重试');
          }
        }).catchError((error) {});
      },
    );
  }

  Widget _buildTableOneRow(
      DailyBill dailyBill, int index, BuildContext context) {
    bool isSettle = widget.settleType == SettleTypeEnum.settled;

    return Container(
        padding: EdgeInsets.symmetric(
          vertical: 10,
        ),
        decoration: index > 0
            ? BoxDecoration(
                border: Border(
                  top: BorderSide(
                    color: billTableColors,
                  ),
                ),
              )
            : null,
        child: _buildTableRowTemplate(
          Transform.translate(
              offset: Offset(-5, 0),
              child: Container(
                  margin: EdgeInsets.only(left: isSettle ? 0 : 20),
                  child: Text(
                      '${dailyBill.dailyBillDate} ${DateFormat.getDay(dailyBill.dailyBillDate)}'))),
          Transform.translate(
              offset: Offset(isSettle ? -130 : -115, 0),
              child: Text(
                  '${MoneyTool.formatMoneyWithPrex(dailyBill.dailyBillAmount)}')),
          Transform.translate(
              offset: Offset(-5, 0),
              child: Text(!isSettle && dailyBill.settleDate != null
                  ? '预计${dailyBill.settleDate}入账'
                  : '')),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              _buildToSee(dailyBill, context),
              Container(
                margin: EdgeInsets.symmetric(
                  horizontal: 6,
                ),
                width: 1,
                height: 12,
                color: Colors.grey,
              ),
              _buildToDownload(dailyBill),
            ],
          ),
        ));
  }

  /// 账单日期
  getBillDate(settleBillStartDate, settleBillEndDate) {
    if (settleBillStartDate != null && settleBillEndDate != null) {
      if (settleBillStartDate == settleBillEndDate) {
        return '${settleBillStartDate} ${DateFormat.getDay(settleBillStartDate)}';
      } else {
        return '$settleBillStartDate 至 $settleBillEndDate';
      }
    }
    return '';
  }

  /// 渲染一个账期内容
  Widget _buildOneSettleBill(
      SettleBill settleBill, int index, BuildContext context) {
    if (settleBill == null || settleBill?.dailyBills == null) {
      return SizedBox.shrink();
    }
    List<Widget> allDaliyBill = settleBill.dailyBills
        .map((e) => _buildTableOneRow(e, 0, context))
        .toList();
    List<Widget> toSettlist = [];

    for (int i = 0; i < settleBill.dailyBills.length; i++) {
      toSettlist.add(_buildTableOneRow(settleBill.dailyBills[i], i, context));
    }
    bool isOpen =
        isOpenMap[index] ?? settleBill.dailyBills.length > 1; // 获取展开状态
    return Container(
      decoration: index > 0
          ? BoxDecoration(
              border: Border(
                top: BorderSide(
                  color: billTableColors,
                ),
              ),
            )
          : null,
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(
              vertical: 10,
            ),
            child: widget.settleType == SettleTypeEnum.settled
                ? _buildTableRowTemplate(
                    GestureDetector(
                        onTap: () {
                          if (settleBill.dailyBills.length <= 1) {
                            return;
                          }
                          this.setState(() {
                            isOpenMap[index] = !isOpen;
                          });
                        },
                        child: Transform.translate(
                            offset: Offset(-5, 0),
                            child: Row(
                              children: [
                                settleBill.dailyBills.length > 1
                                    ? Image.network(isOpen
                                        ? 'https://p0.meituan.net/ingee/18ff9942bea31131ed16398da80e418a227.png'
                                        : 'https://p0.meituan.net/ingee/21e6bd72c1f1bb3838a5a919840f6c30235.png')
                                    : SizedBox.shrink(),
                                Container(
                                    margin: EdgeInsets.only(
                                        left: settleBill.dailyBills.length > 1
                                            ? 4
                                            : 20),
                                    child: Text(getBillDate(
                                        settleBill.settleBillStartDate,
                                        settleBill.settleBillEndDate))),
                              ],
                            ))),
                    Transform.translate(
                        offset: Offset(-115, 0),
                        child: Container(
                            child: Text(
                          '${MoneyTool.formatMoneyWithPrex(settleBill?.totalAmount ?? 0)}',
                        ))),
                    Transform.translate(
                        offset: Offset(-5, 0),
                        child: Text(widget.settleType == SettleTypeEnum.toSettle
                            ? '预计${settleBill.settleDate}入账'
                            : '${settleBill.settleDate} 已汇入余额')),
                    !isOpen
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              _buildToSeeOne(settleBill, context),
                              Container(
                                margin: EdgeInsets.symmetric(
                                  horizontal: 6,
                                ),
                                width: 1,
                                height: 12,
                                color: Colors.grey,
                              ),
                              _buildToDownloadOne(settleBill),
                            ],
                          )
                        : Text(''),
                  )
                : Column(
                    children: toSettlist,
                  ),
          ),
          isOpen && widget.settleType == SettleTypeEnum.settled
              ? Container(
                  color: Color(0xFFbaF7F8FA),
                  padding: EdgeInsets.only(left: 24),
                  child: allDaliyBill.isNotEmpty == true
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: allDaliyBill,
                        )
                      : SizedBox.shrink(),
                )
              : SizedBox.shrink()
        ],
      ),
      padding: EdgeInsets.symmetric(
          // horizontal: 20,
          ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: 10,
        horizontal: 20,
      ),
      color: Color(0x66EEEEEE),
      child: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
        Expanded(
          child: _buildTableRowTemplate(
            Text(
              '账单日期',
              style: headerTextStyles,
            ),
            Transform.translate(
                offset: Offset(-120, 0),
                child: Text(
                  '账单金额',
                  style: headerTextStyles,
                )),
            Text(
              widget.settleType == SettleTypeEnum.toSettle ? '预计结算日期' : '结算日期',
              style: headerTextStyles,
            ),
            Text(
              '操作',
              style: headerTextStyles,
            ),
          ),
        ),
      ]),
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget tableHeader = _buildHeader();
    Widget emptyTip = Empty(
      title: '暂无数据',
      margin: EdgeInsets.symmetric(
        vertical: 30,
      ),
    );
    bool hasBill =
        widget.settleBillList != null && widget.settleBillList.length > 0;
    List<Widget> tableBody = [];
    if (hasBill) {
      for (int i = 0; i < widget.settleBillList.length; i++) {
        tableBody
            .add(_buildOneSettleBill(widget.settleBillList[i], i, context));
      }
    }

    return Container(
        margin: EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 20,
        ),
        decoration: BoxDecoration(
          border: Border.all(color: billTableColors),
          borderRadius: BorderRadius.all(
            Radius.circular(
              8,
            ),
          ),
        ),
        child: Column(
          children: hasBill
              ? [
                  tableHeader,
                  ...tableBody,
                ]
              : [
                  tableHeader,
                  emptyTip,
                ],
        ));
  }
}
