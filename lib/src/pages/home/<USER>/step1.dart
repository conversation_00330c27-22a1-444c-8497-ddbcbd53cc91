import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/arrow.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/gradientButton.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/hint.dart';
import 'hint.dart';
import 'steps.dart';

class NewbeeStep1 extends StatelessWidget {
  NewbeeStep1({this.callback});
  final StepCallback callback;

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: <Widget>[
        Positioned(
          left: 12,
          top: 146,
          child: Container(
            width: 220,
            height: 60,
            decoration: BoxDecoration(
                gradient: LinearGradient(colors: [
                  Color(0xFFFFEA53),
                  Color(0xFFFED301),
                ]),
                borderRadius: BorderRadius.circular(20)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Text(
                  '120,999.00',
                  style: TextStyle(
                    color: Color(0xFF222222),
                    fontWeight: FontWeight.w600,
                    fontSize: 32,
                  ),
                ),
                ArrowIcon(),
              ],
            ),
          ),
        ),
        Positioned(left: 35, top: 220, child: NewbeeHint(text: '点击这里查询余额流水')),
        Positioned(
          bottom: 60,
          child: GradientButton(
            text: '立即进入',
            width: 174,
            height: 40,
            onTap: () {
              this.callback(2);
            },
          ),
        )
      ],
    );
  }
}
