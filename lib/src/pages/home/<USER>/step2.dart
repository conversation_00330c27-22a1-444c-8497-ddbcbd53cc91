import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/gradientButton.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/hint.dart';

import 'hint.dart';
import 'steps.dart';

class NewbeeStep2 extends StatelessWidget {
  NewbeeStep2({this.callback});
  final StepCallback callback;
  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: <Widget>[
        Positioned(
          right: 264,
          top: 54,
          child: Container(
            padding: EdgeInsets.only(left: 14, top: 4, right: 14, bottom: 4),
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(4)),
            child: Text(
              '统计',
              style: TextStyle(
                color: Color(0xFF222222),
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
          ),
        ),
        Positioned(
          right: 240,
          top: 96,
          child: NewbeeHint(
              text: '您可以在这里查看月度账单统计',
              margin: EdgeInsets.only(left: 40),
              type: '2'),
        ),
        Positioned(
          bottom: 60,
          child: <PERSON>rad<PERSON><PERSON><PERSON><PERSON>(
            text: '下一步',
            width: 174,
            height: 40,
            onTap: () {
              this.callback(3);
            },
          ),
        )
      ],
    );
  }
}
