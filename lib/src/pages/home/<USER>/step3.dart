import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/gradientButton.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/hint.dart';
import 'hint.dart';
import 'steps.dart';

class NewbeeStep3 extends StatelessWidget {
  NewbeeStep3({this.callback, this.moneyInfoKey});
  final StepCallback callback;
  final GlobalKey moneyInfoKey;

  blank() {
    return Container(
      width: 24.5,
      child: Center(
        child: Container(
          width: 0.5,
          height: 7,
          color: Color(0xFFCCCCCC),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    dynamic moneyInfoState = moneyInfoKey.currentState;
    final moneyInfoContext = moneyInfoKey.currentContext;
    double height = 0;
    double widgetTop = 460;
    double hintTop = 500;
    if (moneyInfoContext != null) {
      final RenderBox box = moneyInfoContext.findRenderObject();
      if (box?.hasSize == true) {
        height = box.size.height;
        if (moneyInfoState != null && moneyInfoState.showNotify()) {
          widgetTop += height;
          hintTop += height;
        }
      }
    }
    return Stack(
      alignment: Alignment.center,
      children: <Widget>[
        Positioned(
            right: 4,
            top: widgetTop,
            child: Container(
              padding: EdgeInsets.only(left: 14, top: 4, right: 14, bottom: 4),
              decoration: BoxDecoration(
                  color: Colors.white, borderRadius: BorderRadius.circular(4)),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  Text(
                    '查日账单',
                    style: TextStyle(
                      color: Color(0xFF666666),
                      fontSize: 12,
                    ),
                  ),
                  blank(),
                  Text(
                    '查订单',
                    style: TextStyle(
                      color: Color(0xFF666666),
                      fontSize: 12,
                    ),
                  ),
                  blank(),
                  Text(
                    '查看月度统计',
                    style: TextStyle(
                      color: Color(0xFF666666),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            )),
        Positioned(
          right: 4,
          top: hintTop,
          child: NewbeeHint(
            text: '您可以在这里查询日账单、订单和月度统计',
            margin: EdgeInsets.only(right: 114),
          ),
        ),
        Positioned(
          bottom: 60,
          child: GradientButton(
            text: '下一步',
            width: 174,
            height: 40,
            onTap: () {
              callback(4);
            },
          ),
        )
      ],
    );
  }
}
