import 'package:flutter/material.dart';
import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/gradientButton.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/hint.dart';

import 'hint.dart';
import 'steps.dart';

class NewbeeStep4 extends StatelessWidget {
  NewbeeStep4({this.callback});
  final StepCallback callback;

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: <Widget>[
        Positioned(
            right: 74,
            top: 12,
            child: Container(
              padding: EdgeInsets.fromLTRB(6, 4, 6, 4),
              decoration: BoxDecoration(
                  color: Colors.white, borderRadius: BorderRadius.circular(4)),
              child: Image.network(
                'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/85916aabaf7ebfe5d64476839fb48664/kf_new.png',
                width: 24,
                height: 24,
              ),
            )),
        Positioned(right: 12, top: 54, child: NewbeeHint(text: '有问题可以在这里咨询')),
        Positioned(
            bottom: 60,
            child: GradientButton(
              text: '立即使用',
              width: 174,
              height: 40,
              onTap: () {
                callback(-1);
                Navigator.pop(context);
                // 新手引导完成，将字段置为1
                KNB.setStorage(
                    key: 'Finance_StoreNewbeeGuide', value: '1', level: 1);
              },
            ))
      ],
    );
  }
}
