import 'package:flutter/material.dart';
import 'step1.dart';
import 'step2.dart';
import 'step3.dart';
import 'step4.dart';

typedef void StepCallback(int step);

class NewbeeSteps extends StatefulWidget {
  NewbeeSteps({String pageName, this.params})
      : this.pageName =
            pageName ?? (params != null ? params['mtf_page'].toString() : '');
  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  NewbeeStepsState createState() => NewbeeStepsState();
}

class NewbeeStepsState extends State<NewbeeSteps> {
  int curStep = 1;
  StepCallback callback;

  _callback(int step) {
    setState(() {
      curStep = step ?? 1;
    });
  }

  _buildStep() {
    switch (curStep) {
      case 1:
        return NewbeeStep1(callback: this._callback);
      case 2:
        return NewbeeStep2(callback: this._callback);
      case 3:
        return NewbeeStep3(
            callback: this._callback,
            moneyInfoKey: widget.params['moneyInfoKey']);
      case 4:
      default:
        return NewbeeStep4(callback: this._callback);
    }
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTextStyle(
      style: TextStyle(
        fontWeight: FontWeight.w400,
        decoration: TextDecoration.none,
        color: Colors.black,
      ),
      child: Container(
        child: _buildStep(),
        color: Color.fromRGBO(0, 0, 0, .2),
      ),
    );
  }
}
