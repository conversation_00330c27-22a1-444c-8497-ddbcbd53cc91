import 'package:flutter/material.dart';

typedef TabClickCallback = void Function(int);

class CustomTab extends StatefulWidget {
  CustomTab({@required this.cb, @required this.param});
  final TabClickCallback cb;
  final Map<dynamic, dynamic> param;

  @override
  CustomTabState createState() => CustomTabState();
}

class CustomTabState extends State<CustomTab> {
  int selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    if (widget.param != null && widget.param['tabIndex'] != null) {
      selectedIndex = 2;
    }
  }

  renderTabItem(String text, int index) {
    bool selected = index == selectedIndex;
    return GestureDetector(
      child: Column(
        children: <Widget>[
          Text(
            text,
            style: TextStyle(
                color: Color(0xFF222222),
                fontSize: selected ? 18 : 16,
                fontWeight: selected ? FontWeight.w500 : FontWeight.normal),
          ),
          Container(
            width: 20,
            height: 6,
            child: selected
                ? Image.network(
                    'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/d3eafe86662dabd590d7b6cb564565a6/slider.png')
                : null,
          )
        ],
      ),
      onTap: () {
        setState(() {
          widget?.cb(index);
          selectedIndex = index;
        });
      },
    );
  }

  renderTab() {
    return Container(
      color: Colors.white,
      child: Flex(
        direction: Axis.horizontal,
        children: <Widget>[
          Container(
            margin: EdgeInsets.only(left: 14),
            child: Center(
              child: renderTabItem('对账', 0),
            ),
          ),
          SizedBox(
            width: 28,
          ),
          Container(
            child: Center(
              child: renderTabItem('统计', 1),
            ),
          ),
          SizedBox(
            width: 28,
          ),
          Container(
            child: Center(
              child: renderTabItem('订单', 2),
            ),
          )
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // return SafeArea(child: renderTab());
    return renderTab();
  }
}
