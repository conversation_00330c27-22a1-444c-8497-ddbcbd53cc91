import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/arrow.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/tag.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/home.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';

// 今日实时账单
class TodaySettle extends StatefulWidget {
  @override
  TodaySettleState createState() => TodaySettleState();
}

class TodaySettleState extends State<TodaySettle> with HomeMixin {
// 今日实时订单
  DailyBill dailyLiveBill;

  @override
  void initState() {
    super.initState();
    fetchData();
  }

  fetchData() {
    // 查询今日实时
    fetchTodayBill().then((dailyBillListModel) {
      List<DailyBill> list = dailyBillListModel?.dailyBillList;
      if (list != null && list.length > 0) {
        dailyLiveBill = dailyBillListModel.dailyBillList[0];
        setState(() {});
      }
    });
  }

  leftWiget() {
    return Row(
      children: <Widget>[
        Tag(
          text: '今日实时',
          type: TypeEnum.full,
        ),
        SizedBox(width: 4),
        Text(
          dailyLiveBill?.dailyBillDate ?? '',
          style: TextStyle(
            color: Color(0xFF222222),
            fontWeight: FontWeight.w500,
            fontSize: 16,
          ),
        )
      ],
    );
  }

  rightWiget() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        Money(
          size: Size.small,
          text: MoneyTool.formatMoney(dailyLiveBill?.dailyBillAmount ?? 0),
        ),
        ArrowIcon(direction: DirectionEnum.right)
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_ea82r0f8_mv');
    return dailyLiveBill != null
        ? GestureDetector(
            child: Container(
              padding: EdgeInsets.fromLTRB(0, 23, 0, 23),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Color(0xFFEEEEEE), width: 0.5),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[leftWiget(), rightWiget()],
              ),
            ),
            onTap: () {
              RouterTools.flutterPageUrl(context, '/dailyBills', params: {
                "dailyBillDate": DateFormat.formatYYYYMMDD(
                  DateTime.now().millisecondsSinceEpoch,
                )
              });
              ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_ea82r0f8_mc');
            },
          )
        : SizedBox();
  }
}
