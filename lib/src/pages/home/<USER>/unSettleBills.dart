import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/settleItem.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/enum/setttleType.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';

class UnSettleBills extends StatelessWidget {
  getJson() {
    Map<String, dynamic> json = {
      "settleBillStartDate": "2021-03-04",
      "settleBillEndDate": "2021-03-06",
      "settleState": 0,
      "settleDate": "2021-03-07",
      "convertedSettleDate": "2021-03-07",
      "accountSuccessTime": null,
      "tag": "今日实时",
      "totalAmount": 29734,
      "currency": "",
      "dailyBills": [
        {
          "dailyBillDate": "2021-07-01",
          "dailyBillAmount": 19734,
          "currency": null
        },
        {
          "dailyBillDate": "2021-06-30",
          "dailyBillAmount": 10000,
          "currency": ""
        },
      ]
    };
    return json;
  }

  getTypeText(SettleTypeEnum settleType) {
    String text = '';
    if (settleType == SettleTypeEnum.settled) {
      text = '已结算';
    } else if (settleType == SettleTypeEnum.toSettle) {
      text = '待结算';
    }
    return text;
  }

  // 图标+已结算/待结算
  billTitle(SettleTypeEnum settleType) {
    return Container(
      padding: EdgeInsets.fromLTRB(16, 16, 16, 0),
      child: Row(
        children: <Widget>[
          Image.network(
            '${settleType == SettleTypeEnum.settled ? 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/bf9d63a242e4a36c552dbc868767129b/settled.png' : 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/e26d4194653e3ad78803f7b51bf9b314/to-settle.png'}',
            width: 16,
            height: 16,
          ),
          SizedBox(width: 4),
          Text(
            getTypeText(settleType),
            style: TextStyle(
              color: Color(0xFF222222),
              fontWeight: FontWeight.w500,
              fontSize: 14,
            ),
          )
        ],
      ),
    );
  }

  // widget包裹区
  wrapperWidget(Widget settleWidget) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Color(0x0D000000),
            offset: Offset(0, 1),
            blurRadius: 10.5,
          )
        ],
        borderRadius: BorderRadius.circular(10.5),
      ),
      child: settleWidget,
    );
  }

  // 待结算
  settlingWidget(SettleBill settleBill) {
    Widget header = billTitle(SettleTypeEnum.toSettle);
    Widget item = SettleItem(
      settleType: SettleTypeEnum.toSettle,
      settleBill: settleBill,
    );
    Widget full = Container(
      child: Column(
        children: <Widget>[
          header,
          item,
        ],
      ),
    );
    return wrapperWidget(full);
  }

  @override
  Widget build(BuildContext context) {
    SettleBill settleBill = SettleBill.fromJson(getJson());
    return settlingWidget(settleBill);
  }
}
