import 'package:flutter/material.dart';
import 'package:toast_widget_flutter/toast_widget_flutter.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/home.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';

class IndexPage extends StatelessWidget {
  IndexPage({this.params, this.pageName});
  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  Widget build(BuildContext context) {
    return LoadingPage(
      child: _HomePage(
        pageName: pageName,
        params: params,
      ),
    );
  }
}

class _HomePage extends StatefulWidget {
  _HomePage({String pageName, this.params})
      : this.pageName =
            pageName ?? (params != null ? params['mtf_page'].toString() : '');
  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<_HomePage> {
  SimpleInfoModel simpleInfoModel;

  fetchData() async {
    SimpleInfoModel simpleInfo = await fetchSimpleInfo();
    setState(() {
      simpleInfoModel = simpleInfo;
    });
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Demo'),
      ),
      body: Flex(
        direction: Axis.vertical,
        children: <Widget>[
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              // final url = SchemeUrls.flutterPageUrl(
              //   'finance/demo',
              // );
              // RouteUtils.open(url);
              // fetchData();
              RouterTools.flutterPageUrl(
                context,
                '/finance/demo',
                params: {'dailyBillDate': '2021/06/15', 'acctType': 1008},
              );
            },
            child: Column(
              children: <Widget>[
                Text(
                  '点我打开 demo 页面',
                  style: TextStyle(color: Colors.amber, fontSize: 12),
                ),
              ],
            ),
          ),
          // Container(
          //   width: double.infinity,
          //   height: 1,
          //   color: Colors.yellow,
          // ),
          // Expanded(
          // child: DailyBillsPage(),
          // child: MonthBillsPage(),
          // child: HomePage(),
          // child: DemoPage(),
          // child: MonthBill(),
          // child: OrderDetail(),
          // child: OrderQuery(),
          // child: DemoPage(),
          // child: AccountInfoPage()
          // ),
        ],
      ),
    );
  }
}
