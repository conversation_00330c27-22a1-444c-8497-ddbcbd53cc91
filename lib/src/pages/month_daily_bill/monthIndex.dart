import 'package:flutter/material.dart';
import 'package:toast_widget_flutter/toast_widget_flutter.dart';
import 'package:waimai_e_fe_flutter_finance/src/common/flap.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/month_daily_bill/widgets/monthStatic.dart';

@Flap('finance')
class MonthBillsPage extends StatelessWidget {
  MonthBillsPage({this.params, this.pageName});
  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  Widget build(BuildContext context) {
    return LoadingPage(
      child: MonthBill(
        pageName: pageName,
        params: params,
      ),
    );
  }
}
