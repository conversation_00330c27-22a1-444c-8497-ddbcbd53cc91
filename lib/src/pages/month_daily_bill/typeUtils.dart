import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/util/index.dart';

class TypeUtils {
  static void tryPlayTypeStatisticAsg() {
    Util.getPoiId().then((values) {
      KNB.getStorage(key: '${values}asg__waimaie__finace_detail').then((flag) {
        String value = flag['value'];
        if (value == null || value.isEmpty) {}
      });
      playTypeStatisticAsg();
    });
  }

  static void playTypeStatisticAsg() {
    Util.getPoiId().then((values) {
      KNB.use('asg.playASG', {
        'asgId': 'asg__waimaie__finace_detail',
        'max_play_count': -1
      }).then((value) {
        print('play asg__waimaie__finace_detail: ' + value.toString());
        KNB.setStorage(
            key: '${values}asg__waimaie__finace_detail',
            value: "true",
            level: 1);
      }).catchError((error) {
        print('play asg__waimaie__finace_detail fail !');
      });
    });
  }
}
