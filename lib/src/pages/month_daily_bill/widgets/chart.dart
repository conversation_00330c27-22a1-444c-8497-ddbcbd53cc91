import 'package:flutter/material.dart';

class _GreenClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    Path path = Path();
    path.moveTo(0, 0);
    path.lineTo(size.width, 0);
    path.lineTo(size.width - 5, size.height);
    path.lineTo(0, size.height);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) {
    return false;
  }
}

class _RedClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    Path path = Path();
    path.moveTo(5, 0);
    path.lineTo(size.width, 0);
    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) {
    return false;
  }
}

class Chart extends StatelessWidget {
  Chart({@required this.maxWidth, @required this.income, @required this.pay});
  final double maxWidth;
  final int income;
  final int pay;

  @override
  Widget build(BuildContext context) {
    Color green = Color(0xFF00BF7F);
    Color red = Color(0xFFFE2A3B);

    double radio = 0;
    // 防止传入都为0的情况
    if (income == 0 && pay == 0) {
      radio = 0.5;
    } else {
      radio = income / (income.abs() + pay.abs());
    }
    double incomeWidth = radio * maxWidth;
    double payWidth = (1 - radio) * maxWidth;

    return ClipRRect(
      borderRadius: BorderRadius.circular(3),
      child: Row(
        children: <Widget>[
          ClipPath(
            clipper: _GreenClipper(),
            child: Container(
              width: incomeWidth,
              height: 6,
              decoration: BoxDecoration(
                color: green,
              ),
            ),
          ),
          // SizedBox(width: 5),
          ClipPath(
            clipper: _RedClipper(),
            child: Container(
              width: payWidth,
              height: 6,
              decoration: BoxDecoration(
                color: red,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
