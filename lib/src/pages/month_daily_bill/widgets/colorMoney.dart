import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';

// 带颜色的钱数，收入为绿，支出为红
class ColorMoney extends StatelessWidget {
  ColorMoney({@required this.money});
  final int money;
  @override
  Widget build(BuildContext context) {
    return Text(
      MoneyTool.formatMoneyWithPrex(money),
      style: TextStyle(
        color: money > 0 ? Color(0xFF00BF7F) : Color(0xFFFF192D),
        fontWeight: FontWeight.w500,
        fontSize: 14,
      ),
    );
  }
}
