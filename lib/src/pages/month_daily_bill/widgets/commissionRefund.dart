import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/month_daily_bill/widgets/otherBillOneRow.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/month_daily_bill/widgets/title.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/monthBill/monthOverview.dart';

class CommissionRefund extends StatefulWidget {
  CommissionRefund(
      {@required this.monthOverview,
      @required this.isDaily,
      this.dateDuration});

  final MonthOverviewModel monthOverview;
  final bool isDaily;
  final Map dateDuration;

  @override
  CommissionRefundState createState() => CommissionRefundState();
}

class CommissionRefundState extends State<CommissionRefund> {
  @override
  void initState() {
    super.initState();
  }

  commissionRefundDetail(String label, int billCnt, int moneyCnt,
      int billChargeTypeCode, int isShowDetail, String remark) {
    return OtherBillOneRow(
      label: label,
      billCnt: billCnt,
      moneyCnt: moneyCnt,
      billChargeTypeCode: billChargeTypeCode,
      isDaily: widget.isDaily,
      dateDuration: widget.dateDuration,
      isShowDetail: (isShowDetail ?? 0) == 1,
      remark: remark ?? '',
    );
  }

  _buildCommissionRefundDetail() {
    List<BillChargeTypeDetails> billChargeTypeDetails =
        widget.monthOverview?.billChargeTypeDetails ?? [];
    List<Widget> list = [];

    int total = 0;
    billChargeTypeDetails.forEach((e) {
      if (e.orderCategory == 3) {
        total += e.billChargeAmountSum;
      }
    });

    if (billChargeTypeDetails != null) {
      // 筛选非外卖订单、其他类订单
      billChargeTypeDetails = billChargeTypeDetails
          .where((element) => element.orderCategory == 3)
          .toList();
      // 增加title
      if (billChargeTypeDetails.length > 0) {
        list.add(BillTitle(
          label: '激励返还类',
          count: total,
        ));
      }
      billChargeTypeDetails.forEach((e) {
        list.add(commissionRefundDetail(
          e.billChargeTypeName,
          e.billChargeCount,
          e.billChargeAmountSum,
          e.billChargeTypeCode,
          e.isShowDetail,
          e.remark,
        ));
      });
    }

    return Column(
      children: list,
    );
  }

  @override
  Widget build(BuildContext context) {
    return _buildCommissionRefundDetail();
  }
}
