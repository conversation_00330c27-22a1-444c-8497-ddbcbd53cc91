import 'package:flutter/material.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/arrow.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/billChargetListJumper.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/select_bottom_dialog.dart';
// import 'package:waimai_e_fe_flutter_finance/src/pages/month_daily_bill/typeUtils.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/billChargeListForOrder.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/monthBill/monthOverview.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/platformTool.dart';

typedef void BillChargeTypeCodeChange(String code);

// 交易明细
class DailyChargeList extends StatefulWidget {
  DailyChargeList({
    @required this.billChangeList,
    @required this.dailyBillDate,
    @required this.billChargeTypeDetails,
    @required this.billChargeTypeCodeChange,
    this.typeKey,
    // @required this.noticeInfo,
  });

  final List<WmPoiBillChargeDynamicVoList> billChangeList;
  final String dailyBillDate;
  final List<BillChargeTypeDetails> billChargeTypeDetails;
  final BillChargeTypeCodeChange billChargeTypeCodeChange;
  final String typeKey;
  // final UnfinishOrderNotice noticeInfo;

  @override
  DailyChargeListState createState() => DailyChargeListState();
}

class DailyChargeListState extends State<DailyChargeList> with DailyBillMixin {
  String selectedType = '';

  String allCodeType = '';

  @override
  void initState() {
    super.initState();
    List<BillChargeTypeDetails> billChargeTypeDetails =
        widget.billChargeTypeDetails ?? [];
    String allCode = billChargeTypeDetails
        .map((e) => e.billChargeTypeCode)
        .toList()
        .join(',');
    selectedType = allCode;
    allCodeType = allCode;
    this.setState(() {});
    // if (widget.typeKey == '2') {
    //   /// 引导
    //   TypeUtils.tryPlayTypeStatisticAsg();
    // }
  }

  // 交易类型的选择与弹窗
  chargeType() {
    List<SelectItemFinance> selectList = [
      SelectItemFinance(
        id: allCodeType,
        title: '全部',
        isSelected: selectedType == allCodeType ? 1 : 0,
      )
    ];
    List<BillChargeTypeDetails> billChargeTypeDetails =
        widget.billChargeTypeDetails ?? [];
    billChargeTypeDetails.forEach((e) {
      selectList.add(SelectItemFinance(
        id: e.billChargeTypeCode.toString(),
        title: e.billChargeTypeName,
        isSelected: selectedType == e.billChargeTypeCode ? 1 : 0,
      ));
    });
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        SelectBottomDialog dialog = SelectBottomDialog(
            items: selectList,
            onClickItem: (SelectItemProvider item) {
              selectList.forEach((ele) {
                if (ele.id == item.id) {
                  ele.isSelected = 1;
                } else {
                  ele.isSelected = 0;
                }
              });
              selectedType = item.id;
              widget.billChargeTypeCodeChange(item.id);
            });
        dialog.show(context);
      },
      child: Row(
        children: <Widget>[
          Text(
            '交易类型',
            strutStyle: PlatformTool.isWeb
                ? null
                : StrutStyle(
                    forceStrutHeight: true,
                    height: 1,
                  ),
            style: TextStyle(
              color: Color(0xFF222222),
              fontSize: 14,
            ),
          ),
          ArrowIcon(
            direction: DirectionEnum.down,
          )
        ],
      ),
    );
  }

  headerWiget() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: 16,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Text(
            '交易明细',
            strutStyle: PlatformTool.isWeb
                ? null
                : StrutStyle(
                    forceStrutHeight: true,
                    height: 1,
                  ),
            style: TextStyle(
              color: Color(0xFF222222),
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
          ),
          chargeType(),
        ],
      ),
    );
  }

  // ignore: unused_element
  _buildNoticeWidget() {
    const TextStyle firstTipStyle = TextStyle(
      fontSize: 14,
      color: Color(0xFF222222),
    );
    const TextStyle secondTipStyle = TextStyle(
      fontSize: 12,
      color: Color(0xFF666666),
    );
    return RooTopTip(
        margin: EdgeInsets.only(
          top: 20,
        ),
        padding: EdgeInsets.symmetric(vertical: 6, horizontal: 16),
        content: "仅包含当日已完成订单",
        rightButtonTxt: "详情",
        showLeftIcon: true,
        showRightButton: true,
        radius: 2,
        onPressActionIcon: () {
          showDialog(
            context: context,
            builder: (context) => RooDialog(
              context,
              titleText: '详情',
              content: Padding(
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '当日未完成订单将延时自动完成并结算，可点击“查看”了解订单详情。',
                        style: firstTipStyle,
                      ),
                      SizedBox(
                        height: 8,
                      ),
                      Text(
                        '查看提示：',
                        style: secondTipStyle,
                      ),
                      Text(
                        '1. 尚未完成的订单会提示未完成原因及自动完成时间',
                        style: secondTipStyle,
                      ),
                      Text(
                        '2. 已完成的订单会进入完成日的账单列表并结算',
                        style: secondTipStyle,
                      ),
                    ]),
                padding: EdgeInsets.symmetric(
                  vertical: 14,
                ),
              ),
              cancelText: '取消',
              confirmText: '查看',
              confirmCallback: () {
                // String jumpUrl = widget?.noticeInfo?.appUrl ?? '';
                // if (jumpUrl.length > 0) {
                //   return RouteUtils.open(jumpUrl);
                // }
              },
            ),
          );
        });
  }

  hasAction(row) {
    return [1, 2, 3].contains(row.appDetailType);
  }

  /// 订单类型标识
  isType(List<OrderTag> tags) {
    List<Widget> tagList = [];
    tags != null && tags.length > 0
        ? tags.forEach((element) {
            if (element.code == 102) {
              tagList.add(Image.network(
                  'https://p0.meituan.net/ingee/f439eb1482af4c0f7c412b1f9c3bda042674.png'));
            } else if (element.code == 101) {
              tagList.add(Image.network(
                  'https://p0.meituan.net/ingee/454ca8a434cbaa67ecdaf2663fb8bda23776.png'));
            }
          })
        : SizedBox.shrink();
    return tagList;
  }

  _buildDetailWiget(WmPoiBillChargeDynamicVoList bill) {
    int seq = bill.poiOrderPushDayseq;
    String outCreateDate = DateFormat.formatYYYYMMDD(bill.outCreateTimestamp);
    String seqStr =
        seq != null && seq != -1 ? '${outCreateDate} #$seq' : outCreateDate;
    // 10-11 #1
    Widget seqWidget = Container(
      width: 110,
      child: Text(
        seqStr,
        style: TextStyle(
          color: Color(0xFF222222),
          fontWeight: FontWeight.w400,
          fontSize: 14,
        ),
      ),
    );
    // 外卖订单
    Widget typeWidget = Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          /// 先注释掉后续可能需要这么改
          // Text.rich(TextSpan(children: [
          //   WidgetSpan(
          //       alignment: PlaceholderAlignment.middle,
          //       child: Container(
          //           width: isType(bill.tags).length > 0 ? 19 : 0,
          //           height: isType(bill.tags).length > 0 ? 19 : 0,
          //           child: Column(
          //             children: isType(bill.tags),
          //           ))),
          //   TextSpan(
          //     text: bill.billChargeTypeName ?? '',
          //     style: TextStyle(
          //       color: Color(0xFF666666),
          //       fontWeight: FontWeight.w500,
          //       fontSize: 14,
          //     ),
          //   ),
          // ])),
          Wrap(
            children: [
              Container(
                  width: isType(bill.tags).length > 0 ? 19 : 0,
                  height: isType(bill.tags).length > 0 ? 19 : 0,
                  child: Column(
                    children: isType(bill.tags),
                  )),
              Text(
                bill.billChargeTypeName,
                textAlign: TextAlign.left,
                style: TextStyle(
                  color: Color(0xFF666666),
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              )
            ],
          ),
          bill.chargeTypeName != ''
              ? Wrap(
                  children: [
                    Container(
                      margin: EdgeInsets.only(top: 1.5),
                      child: Text(
                        bill.chargeTypeName,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: Color(0xFF999999),
                          fontWeight: FontWeight.w400,
                          fontSize: 12,
                        ),
                      ),
                    )
                  ],
                )
              : SizedBox()
        ],
      ),
    );
    // 钱数
    Widget moneyWidget = ConstrainedBox(
      constraints: BoxConstraints(minWidth: 80),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: <Widget>[
          Text(
            MoneyTool.formatMoneyWithPrex(bill.chargeAmount),
            style: TextStyle(
              color: Color(0xFF222222),
              fontWeight: FontWeight.w500,
              fontSize: 16,
            ),
          ),
          hasAction(bill) ? ArrowIcon() : SizedBox()
        ],
      ),
    );

    return BillChargeListJumper(
      bill: bill,
      dailyBillDate: widget.dailyBillDate,
      from: 'daily',
      keys: '1',
      child: Container(
        padding: EdgeInsets.fromLTRB(0, 12, 0, 12),
        margin: EdgeInsets.only(top: 12),
        child: Flex(
          direction: Axis.horizontal,
          children: <Widget>[seqWidget, typeWidget, moneyWidget],
        ),
      ),
    );
  }

  listDetails() {
    List<Widget> list = [];
    widget.billChangeList?.forEach((e) {
      list.add(_buildDetailWiget(e));
    });
    return list;
  }

  // 整个Card部分
  dailyBillContent() {
    // bool isShowNotice = widget?.noticeInfo?.showTip ?? false;
    // shadow区域
    Widget content = Container(
      padding: EdgeInsets.symmetric(
        vertical: 16,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Color(0x0D000000),
            offset: Offset(0, 1),
            blurRadius: 10.5,
          )
        ],
        borderRadius: BorderRadius.circular(10.5),
      ),
      child: Column(
        children: <Widget>[
          headerWiget(),
          // isShowNotice ? _buildNoticeWidget() : SizedBox.shrink(),
          // 多行数据
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: 16,
            ),
            child: Column(
              children: listDetails(),
            ),
          )
        ],
      ),
    );
    return content;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 20),
      child: dailyBillContent(),
    );
  }
}
