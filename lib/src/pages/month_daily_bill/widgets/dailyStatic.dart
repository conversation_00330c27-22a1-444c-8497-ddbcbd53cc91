import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:mt_flutter_web_utils/mt_flutter_web_utils.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:roo_flutter/tools/response_system.dart';
import 'package:roo_flutter/tools/string_utils.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/arrow.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/card.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/datePicker.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/aigcCard.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/home/<USER>/aigcPanel.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/month_daily_bill/widgets/commissionRefund.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/month_daily_bill/widgets/dailyChargeList.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/month_daily_bill/widgets/daily_charge_table.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/month_daily_bill/widgets/noData.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/month_daily_bill/widgets/otherBill.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/month_daily_bill/widgets/summary.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/month_daily_bill/widgets/waimaiBill.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/dailyBill.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/home.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/aigcAnalysis.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/billChargeListForOrder.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/dailyBill/unfinishOrderNotice.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/monthBill/monthOverview.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/third_part/date_format/date_format_base.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/uiTools.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/util/index.dart';

class DailyBillWidget extends StatelessWidget {
  DailyBillWidget({String pageName, this.params})
      : this.pageName =
            pageName ?? (params != null ? params['mtf_page'].toString() : '');
  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFF7F8FA),
      appBar: UITools.renderNavbar(context: context, title: '账单详情'),
      body: DailyStatic(
        params: params,
      ),
    );
  }
}

class DailyStatic extends StatefulWidget {
  DailyStatic({@required this.params});
  final Map<dynamic, dynamic> params;

  @override
  DailyStaticState createState() => DailyStaticState();
}

class DailyStaticState extends State<DailyStatic> with DailyBillMixin {
  MonthOverviewModel dailyOverview;

  /// 用来整理订单汇总信息
  MonthOverviewModel orderSummarys;

  // 交易明细
  List<WmPoiBillChargeDynamicVoList> billChangeList = [];
  // 订单类明细头部提示内容
  List<WmPoiBillChargeTitleVoList> billChargeTitleVoList = [];

  // 公告内容对象
  UnfinishOrderNotice noticeObj;

  /// 是否是Loading状态
  bool isLoading;

  String dailyBillDate = '';
  String billChargeTypeCode = '-1';
  int pageNo = 1;
  int pageSize = 20;
  String toWmPoiId = '-1';
  // 总条数
  int totalCount = 1;
  bool more = true;

  BillChargeTypeDetails summaryDetails;
  int activeCategory = 1;
  // 一级tab
  List<BillCategoryItem> categoryItems = [
    BillCategoryItem('订单类', 1),
    BillCategoryItem('其它类', 2),
    BillCategoryItem('激励返还类', 3),
  ];

  List<BillChargeItem> secondTabList = [];

  /// 是否展示AIGC面板
  bool showAigcPanel = false;

  /// 是否展示新AIGC面板
  bool showNewAigcPanel = false;

  @override
  void initState() {
    super.initState();
    if (widget?.params != null) {
      dailyBillDate = widget.params['dailyBillDate'];
      toWmPoiId = widget.params['toWmPoiId'];
      // 比较当前时间和传进来的时间
      DateTime curDate = DateTime.now();
      DateTime propsDate = DateFormat.formatStringToDateTime(dailyBillDate);
      DateTime dateStartRange =
          DateTime(curDate.year, curDate.month, curDate.day - 120);
      // 如果传进来的时间早，改成查询最早日期的
      if (propsDate.isBefore(dateStartRange)) {
        dailyBillDate = formatDate(dateStartRange, [yyyy, "-", mm, "-", dd]);
      }
    }
    fetchData();
    fetchAigcGray(AigcSceneType.dailyBill).then((value) {
      if (mounted) {
        setState(() {
          showAigcPanel = value;
        });
      }
    });

    fetchAigcNewGray().then((value) {
      if (mounted) {
        setState(() {
          showNewAigcPanel = value;
        });
      }
    });

    ReportLX.pv(pageKeyInfo, cid);
    if (widget.params != null && widget.params['key'] != null) {
      activeCategory = 3;
      Timer(Duration(seconds: 1), () => typeGuideModel());
    }
    getOrderSum();
  }

  @override
  void dispose() {
    super.dispose();
  }

  typeGuideModel() async {
    // if (!typeGuideModel()) return;
    final resultPc = PlatformTools.isPC
        ? await HomeUtils.getLocalStorage(
            '${Util.getCookie('wmPoiId')}Finance_TypeGuide')
        : '';
    if (resultPc == "" && PlatformTools.isPC) {
      showDialog(
          context: context,
          builder: (context) {
            return RooDialog(
              context,
              title: Container(),
              edgeInsets: EdgeInsets.all(0),
              content: Container(
                  child: Column(
                children: [
                  Container(
                      padding: EdgeInsets.all(12),
                      child: Column(
                        children: [
                          Text('这里可以切换交易类型，在【激励返还类】交易明细查看神抢手佣金返还'),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Container(),
                              Container(
                                  width: 96,
                                  height: 36,
                                  child: Row(
                                    children: [
                                      RooGradientButton(
                                        child: Text("我知道了"),
                                        onPressed: () {
                                          Navigator.of(context).pop();
                                          HomeUtils.setLocalStorage(
                                              '${Util.getCookie('wmPoiId')}Finance_TypeGuide',
                                              '1');
                                        },
                                        // size: Size(96, 36),
                                      )
                                    ],
                                  ))
                            ],
                          )
                        ],
                      ))
                ],
              )),
            );
          });
    }
  }

  updateSecondTab() {
    List<BillChargeTypeDetails> detailList = PlatformTools.isPC
        ? orderSummarys?.billChargeTypeDetails
        : dailyOverview?.billChargeTypeDetails;
    secondTabList = MonthOverviewModel.getTabCodeName(detailList ?? [],
        category: activeCategory);
    if (secondTabList.length > 0) {
      BillChargeItem allItem = secondTabList[0];
      // 获取全部选择下的code值
      // billChargeTypeCode = allItem.code;
      billChargeTypeCodeChange(allItem.code);
      // PC下-订单类需要展示特殊的详细信息总和
      if (activeCategory == 1 && PlatformTools.isPC) {
        summaryDetails = MonthOverviewModel.getSummaryByBillChargeTypeCode(
            detailList, int.tryParse(allItem.code));
      }
    }
  }

  fetchData() {
    fetchDailyOverview({
      'dailyBillDate': DateFormat.changeDateSplitChar(dailyBillDate),
      'toWmPoiId': toWmPoiId
    }).then((dailyOverviewModel) {
      dailyOverview = dailyOverviewModel;
      updateSecondTab();
    });
    // fetchNotice();
  }

  getOrderSum() {
    if (UITools.isPc) {
      fetchOrderSum({
        'dailyBillDate': DateFormat.changeSplitChar(dailyBillDate),
        'toWmPoiId': toWmPoiId
      }).then((orderSummary) {
        orderSummarys = orderSummary;
        updateSecondTab();
      });
    }
  }
  // fetchNotice() {
  //   fetchUnfinishOrderNotice({
  //     'dailyBillDate': dailyBillDate,
  //     'businessLine': 1,
  //   }).then((noticeModel) {
  //     noticeObj = noticeModel;
  //     setState(() {});
  //   });
  // }

  fetchBillChargeListData({
    int paramsPageNo,
  }) {
    if (isLoading == true) return;
    isLoading = true;
    Loading.showLoading();
    return fetchBillChargeListByDate({
      'dailyBillDate': DateFormat.changeSplitChar(dailyBillDate),
      'billChargeTypeCode': billChargeTypeCode,
      'pageNo': paramsPageNo ?? pageNo,
      'pageSize': pageSize,
      'toWmPoiId': toWmPoiId
    }).then((BillChargeListForOrderModel res) {
      /// 获取数据源内容
      List<WmPoiBillChargeDynamicVoList> billChMoList =
          res?.wmPoiBillChargeDynamicVoList;

      totalCount = res?.count ?? 1;
      if (billChMoList?.isNotEmpty == true) {
        if (PlatformTools.isPC) {
          billChangeList = billChMoList;
          if (paramsPageNo != null) {
            pageNo = paramsPageNo;
          }
        } else {
          billChangeList.addAll(billChMoList);
          pageNo++;
          if (billChangeList.length >= totalCount) {
            more = false;
          } else {
            more = true;
          }
        }
      }
      List<WmPoiBillChargeTitleVoList> billTitleVoList =
          res?.wmPoiBillChargeTitleVoList;
      if (billTitleVoList?.isNotEmpty == true) {
        billChargeTitleVoList = billTitleVoList;
      }
      if (PlatformTools.isPC) {
        summaryDetails = MonthOverviewModel.getSummaryByBillChargeTypeCode(
            orderSummarys?.billChargeTypeDetails ?? [],
            int.tryParse(
              billChargeTypeCode,
            ));
      }
      setState(() {});
    }).whenComplete(() {
      isLoading = false;
      Loading.dismissLoading();
    });
  }

  // 选择时间后的回调函数
  changeDateCallback(DateTime pickedDate) {
    setState(() {
      if (pickedDate != null) {
        String newDailyBillDate =
            DateFormat.formatYYYYMMDD(pickedDate.millisecondsSinceEpoch);
        if (newDailyBillDate != dailyBillDate) {
          dailyBillDate = newDailyBillDate;
          pageNo = 1;
          billChangeList = [];
          fetchData();

          getOrderSum();
        }
      }
    });
    ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_sf14io3u_mc');
  }

  dailyTitle() {
    // 显示已选择的日期，比如：2020/01/30
    DateTime curDate = DateTime.now();
    Widget datePickerPC = Container(
      width: 120,
      child: RooDatePickerAdapted(
        isRange: false,
        initialDate: DateFormat.formatStringToDateTime(dailyBillDate),
        firstDate: DateTime(curDate.year, curDate.month, curDate.day - 120),
        lastDate: curDate,
        onChanged: changeDateCallback,
      ),
    );
    // App侧临时方案，后续替换成组件，目前组件不支持自定义目标对象
    Widget appTemp = Container(
      margin: EdgeInsets.only(bottom: 16.5),
      child: GestureDetector(
        child: Row(children: <Widget>[
          Text(
            dailyBillDate ?? '',
            style: TextStyle(
              color: Color(0xFF222222),
              fontWeight: FontWeight.w400,
              fontSize: 16,
            ),
          ),
          ArrowIcon(
            direction: DirectionEnum.down,
          )
        ]),
        onTap: () async {
          final DateTime pickedDate = await showCustomDatePicker(
            context: context,
            initialDate: DateFormat.formatStringToDateTime(dailyBillDate),
            firstDate: DateTime(curDate.year, curDate.month, curDate.day - 120),
            lastDate: curDate,
          );
          changeDateCallback(pickedDate);
        },
      ),
    );
    return ResponsiveSystem(
      app: appTemp,
      pc: datePickerPC,
    );
  }

  Widget _buildDailyHeader() {
    return RadiusCard(
      margin: EdgeInsets.only(
        bottom: 10,
      ),
      padding: EdgeInsets.symmetric(vertical: 20, horizontal: 30),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.only(bottom: 10),
            child: Text(
              '日账单',
              style: TextStyle(
                  color: Colors.black,
                  fontSize: 20,
                  fontWeight: FontWeight.w600),
            ),
          ),
          Stack(
            children: [
              dailyTitle(),
              Positioned(
                child: Icon(
                  Icons.arrow_drop_down,
                  size: 16,
                ),
                top: 12,
                right: 6,
              )
            ],
          ),
        ],
      ),
    );
  }

  // 整个Card部分
  dailyBillContent() {
    // shadow区域
    Widget content = Container(
      padding: EdgeInsets.fromLTRB(16, 24, 16, 24),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Color(0x0D000000),
            offset: Offset(0, 1),
            blurRadius: 10.5,
          )
        ],
        borderRadius: BorderRadius.circular(10.5),
      ),
      child: Column(
        children: <Widget>[
          WaimaiBill(
            monthOverview: dailyOverview,
            refundNum: dailyOverview?.refundNum,
            isDaily: true,
          ),
          SizedBox(height: 17),
          OtherBill(
            monthOverview: dailyOverview,
            isDaily: true,
          ),
          SizedBox(height: 17),
          CommissionRefund(
            monthOverview: dailyOverview,
            isDaily: true,
          ),
        ],
      ),
    );
    return content;
  }

  void billChargeTypeCodeChange(String code) {
    billChargeTypeCode = code;
    pageNo = 1;
    billChangeList = [];
    fetchBillChargeListData();
  }

  // 加载更多
  refreshWidget() {
    return EasyRefresh(
      footer: MaterialFooter(enableInfiniteLoad: false),
      child: SingleChildScrollView(
        child: Column(
          children: <Widget>[
            dailyTitle(),
            Visibility(
              visible: showAigcPanel && !showNewAigcPanel,
              child: Container(
                margin: EdgeInsets.only(bottom: 12),
                child: AigcCard(
                    AigcSceneType.dailyBill,
                    StringUtil.isNotEmpty(dailyBillDate)
                        ? DateFormat.changeSplitChar(dailyBillDate)
                        : DateFormat.formatXfYYYYMMDD(
                            DateTime.now().millisecondsSinceEpoch)),
              ),
            ),
            Summary(
              monthOverview: dailyOverview,
              isDaily: true,
            ),
            SizedBox(height: 10),
            dailyBillContent(),
            DailyChargeList(
                dailyBillDate: dailyBillDate,
                billChangeList: billChangeList,
                billChargeTypeDetails:
                    dailyOverview?.billChargeTypeDetails ?? [],
                billChargeTypeCodeChange: billChargeTypeCodeChange,
                typeKey: widget.params != null && widget.params['key'] == '2'
                    ? widget.params['key']
                    : null),
            more ? SizedBox() : NoDataWidget()
          ],
        ),
      ),
      onLoad: () async {
        return fetchBillChargeListData();
      },
    );
  }

  Widget _buildTabItem(
      bool isActive, Function onChange, String name, String amount, int index) {
    return Expanded(
        child: RadiusCard(
      margin: EdgeInsets.only(
        left: index == 0 ? 0 : 14,
      ),
      padding: EdgeInsets.symmetric(
        vertical: 18,
        horizontal: 30,
      ),
      isActive: isActive,
      onClick: onChange,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            child: Text(
              name ?? '',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Colors.black,
              ),
            ),
            padding: EdgeInsets.only(
              bottom: 10,
            ),
          ),
          Text(
            amount ?? '',
            style: TextStyle(
              fontSize: 27,
              fontWeight: FontWeight.w700,
              color: Color(0xFF222222),
            ),
          )
        ],
      ),
    ));
  }

  _handleTabChange(int category) {
    activeCategory = category;
    updateSecondTab();
    this.setState(() {});
  }

  _buildTab() {
    List<Widget> tabList = [];
    if (dailyOverview?.billChargeTypeDetails != null) {
      categoryItems.asMap().forEach((index, element) {
        int code = element.categoryCode;
        tabList.add(
          _buildTabItem(
            activeCategory == code,
            () => _handleTabChange(code),
            element.categoryName,
            MonthOverviewModel.getSummaryAmount(
                dailyOverview?.billChargeTypeDetails ?? [],
                category: code),
            index,
          ),
        );
      });
    }
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: tabList,
    );
  }

  Widget _buildSecondTab() {
    if (secondTabList.isNotEmpty == true) {
      List<String> titles = [];
      secondTabList.forEach((element) => {titles.add(element.name)});
      return RooTabs(
        tabHeight: 36,
        type: RooTabsType.fill,
        tabTitles: titles,
        onIndexChange: (int index) {
          if (secondTabList.length > index) {
            billChargeTypeCodeChange(secondTabList[index].code);
          }
        },
      );
    } else {
      return SizedBox.shrink();
    }
  }

  Widget _buildPC() {
    Widget mainContent = Container(
        width: showNewAigcPanel ? 1314 : 1014,
        child: CustomScrollView(
            scrollDirection: Axis.vertical,
            controller: ScrollController(),
            slivers: [
              SliverToBoxAdapter(
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  children: <Widget>[
                    _buildDailyHeader(),
                    Summary(
                      monthOverview: dailyOverview,
                      isDaily: true,
                    ),
                    Padding(
                      padding: EdgeInsets.symmetric(vertical: 10),
                      child: _buildTab(),
                    ),
                    RadiusCard(
                      padding: EdgeInsets.all(30),
                      margin: EdgeInsets.only(bottom: 30),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildSecondTab(),
                          SizedBox(
                            height: 20,
                          ),
                          isLoading == true
                              ? SizedBox.shrink()
                              : DailyChargeTable(
                                  categoryType: activeCategory,
                                  listContent: billChangeList,
                                  listTitle: billChargeTitleVoList,
                                  summaryDetails: summaryDetails,
                                  hideSummary: false,
                                  toWmPoiId: toWmPoiId,
                                ),
                          Padding(
                            padding: EdgeInsets.only(
                              top: 20,
                            ),
                            child: RooPagination(
                              onPageChanged: (int currentPage) async {
                                fetchBillChargeListData(
                                    paramsPageNo: currentPage);
                              },
                              currentPage: pageNo,
                              totalPage: (totalCount / pageSize).ceil(),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              )
            ]));
    if (showAigcPanel) {
      return CustomScrollView(
          scrollDirection: Axis.horizontal,
          controller: ScrollController(),
          slivers: <Widget>[
            SliverToBoxAdapter(child: mainContent),
            SliverToBoxAdapter(
                child: showNewAigcPanel
                    ? Container()
                    : AigcPanel(
                        AigcSceneType.dailyBill,
                        StringUtil.isNotEmpty(dailyBillDate)
                            ? DateFormat.changeSplitChar(dailyBillDate)
                            : DateFormat.formatXfYYYYMMDD(
                                DateTime.now().millisecondsSinceEpoch)))
          ]);
    } else {
      return mainContent;
    }
  }

  @override
  Widget build(BuildContext context) {
    return dailyOverview != null
        ? ResponsiveSystem(
            app: Container(child: refreshWidget(), padding: EdgeInsets.all(12)),
            pc: _buildPC(),
          )
        : SizedBox();
  }
}
