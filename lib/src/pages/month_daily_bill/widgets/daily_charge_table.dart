import 'package:flutter/material.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/billChargetListJumper.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/question_icon.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/billChargeListForOrder.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/monthBill/monthOverview.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';

/// 日账单页面的Table
class DailyChargeTable extends StatefulWidget {
  const DailyChargeTable({
    this.key,
    @required this.listContent,
    @required this.listTitle,
    this.summaryDetails,
    this.hideSummary = false,
    this.categoryType = 1,
    this.toWmPoiId = '-1',
  }) : super(key: key);

  final bool hideSummary;

  final Key key;
  // 列表详细内容
  final List<WmPoiBillChargeDynamicVoList> listContent;

  // 订单类明细头部提示内容
  final List<WmPoiBillChargeTitleVoList> listTitle;

  /// 总和对象
  final BillChargeTypeDetails summaryDetails;

  final int categoryType;

  final String toWmPoiId;

  @override
  State<DailyChargeTable> createState() => _DailyChargeTableState();
}

class _DailyChargeTableState extends State<DailyChargeTable> {
  TextStyle headerTextStyle = TextStyle(color: Colors.black);

  /// 订单类型标识
  isType(List<OrderTag> tags) {
    List<Widget> tagList = [];
    tags != null && tags.length > 0
        ? tags.forEach((element) {
            if (element.code == 102) {
              tagList.add(Image.network(
                  'https://p0.meituan.net/ingee/f439eb1482af4c0f7c412b1f9c3bda042674.png'));
            } else if (element.code == 101) {
              tagList.add(Image.network(
                  'https://p0.meituan.net/ingee/454ca8a434cbaa67ecdaf2663fb8bda23776.png'));
            }
          })
        : SizedBox.shrink();
    return tagList;
  }

  _buildTable() {
    List<WmPoiBillChargeDynamicVoList> orderContent = [];
    List<RooTableColumn<WmPoiBillChargeDynamicVoList>> colWidget = [];
    List<RooTableColumn<WmPoiBillChargeDynamicVoList>> endTwoCol = [
      RooTableColumn<WmPoiBillChargeDynamicVoList>(
        label: '结算金额(元)',
        width: 120,
        headerAlign: Alignment.centerRight,
        textAlign: Alignment.centerRight,
        headingTextStyle: headerTextStyle,
        cellRender: (WmPoiBillChargeDynamicVoList billItem) {
          String amountStr = billItem == null
              ? MoneyTool.formatMoneyWithPrex(
                  widget.summaryDetails?.billChargeAmountSum ?? 0)
              : MoneyTool.formatMoneyWithPrex(billItem?.chargeAmount ?? 0);
          return Container(
            alignment: Alignment.centerRight,
            child: Text(amountStr),
          );
        },
      ),
      RooTableColumn<WmPoiBillChargeDynamicVoList>(
        label: '操作',
        width: 70,
        headingTextStyle: headerTextStyle,
        cellRender: (WmPoiBillChargeDynamicVoList billItem) {
          return billItem != null && billItem.webDetailType == 3
              ? Container(
                  alignment: Alignment.center,
                  padding: EdgeInsets.symmetric(
                    horizontal: 15,
                  ),
                  child: BillChargeListJumper(
                    keys: '1',
                    bill: billItem,
                    dailyBillDate: DateFormat.formatYYYYMMDD(
                        billItem.dailyBillDateTimestamp),
                    toWmPoiId: widget.toWmPoiId,
                    child: Text(
                      '详情',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: Color(0xFFFF6A00),
                      ),
                    ),
                  ),
                )
              : SizedBox.shrink();
        },
      ),
    ];
    List<RooTableColumn<WmPoiBillChargeDynamicVoList>> leftTableCol = [];
    List<RooTableColumn<WmPoiBillChargeDynamicVoList>> rightTableCol = [];
    if (widget.categoryType == 1) {
      // 添加总和行占位，当列表为空的情况下，不添加占位
      if (widget?.listContent != null &&
          widget.listContent?.isNotEmpty == true &&
          widget.hideSummary == false) {
        orderContent.add(null);
      }
      orderContent.addAll(widget.listContent);
      // 订单类
      List<RooTableColumn<WmPoiBillChargeDynamicVoList>> startTowCol = [
        RooTableColumn<WmPoiBillChargeDynamicVoList>(
            label: '日期&订单编号',
            width: 120,
            headerAlign: Alignment.centerLeft,
            textAlign: Alignment.centerLeft,
            headerRender: (String label) {
              return Padding(
                padding: EdgeInsets.only(left: 10),
                child: Row(
                  children: [
                    Text(label),
                    RooTooltip(
                        target: QuestionIcon(), tip: '显示的日期为下单日期及当天的订单序号')
                  ],
                ),
              );
            },
            cellRender: (WmPoiBillChargeDynamicVoList billItem) {
              String text = '';
              // 总额一行内容
              if (billItem == null) {
                text = '共${widget.summaryDetails?.billChargeCount ?? "--"}单';
              } else {
                int seq = billItem?.poiOrderPushDayseq;
                String poiOrderPushDayseqPC =
                    seq != null && seq != -1 ? '#${seq}' : '';
                text =
                    '${DateFormat.formatYYYYMMDD(billItem?.dailyBillDateTimestamp ?? 0, splitStr: "-")}$poiOrderPushDayseqPC';
              }
              return Container(
                padding: EdgeInsets.only(
                  left: 10,
                ),
                child: Text(text),
              );
            }),
        RooTableColumn<WmPoiBillChargeDynamicVoList>(
          label: '类型',
          width: 160,
          headerAlign: Alignment.centerLeft,
          textAlign: Alignment.centerLeft,
          // headingTextStyle: headerTextStyle,
          headerRender: (String label) {
            return Padding(
              padding: EdgeInsets.only(left: 18),
              child: Row(
                children: [
                  Text(label),
                ],
              ),
            );
          },
          cellRender: (WmPoiBillChargeDynamicVoList billItem) {
            return billItem == null
                ? SizedBox.shrink()
                : Container(
                    //  padding: EdgeInsets.symmetric(vertical: billItem.chargeTypeName != '' ? 12 : 10),
                    margin: EdgeInsets.only(top: 10),
                    alignment: Alignment.centerLeft,
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Container(
                                width: 19,
                                margin: EdgeInsets.only(
                                    top:
                                        billItem.chargeTypeName != '' ? 0 : 10),
                                child: Column(
                                  children: isType(billItem.tags),
                                )),
                            Container(
                                margin: EdgeInsets.only(
                                    top:
                                        billItem.chargeTypeName != '' ? 0 : 10),
                                child: Text(
                                    '${billItem?.billChargeTypeName ?? ''}')),
                          ],
                        ),
                        billItem.chargeTypeName != ''
                            ? Row(
                                children: [
                                  Container(
                                    margin: EdgeInsets.only(top: 4, left: 19),
                                    child: Text(
                                      billItem.chargeTypeName,
                                      textAlign: TextAlign.center,
                                      style: TextStyle(
                                        color: Color(0xFF999999),
                                        fontWeight: FontWeight.w400,
                                        fontSize: 12,
                                      ),
                                    ),
                                  )
                                ],
                              )
                            : SizedBox()
                      ],
                    ));
          },
        ),
      ];

      colWidget.addAll(startTowCol);
      leftTableCol.addAll(startTowCol);
      rightTableCol.addAll(endTwoCol);
      if (widget.listTitle != null) {
        widget.listTitle.forEach((element) {
          colWidget.add(RooTableColumn<WmPoiBillChargeDynamicVoList>(
              label: element.title,
              headerRender: (String label) {
                return Row(
                  children: [
                    Text(label),
                    element.tips?.isNotEmpty == true
                        ? RooTooltip(target: QuestionIcon(), tip: element.tips)
                        : SizedBox.shrink()
                  ],
                );
              },
              cellRender: (WmPoiBillChargeDynamicVoList billItem) {
                String showMoney = '--';
                // 找到对应明细内容
                if (billItem == null) {
                  // 总金额明细
                  List<DailyBillFeeDetails> summaryDetail =
                      widget.summaryDetails?.dailyBillFeeDetails ?? [];
                  if (summaryDetail.isNotEmpty == true) {
                    int targetIndex = summaryDetail.indexWhere(
                        (ele) => '${ele.billFeeTypeCode}' == element.typeCode);
                    if (targetIndex >= 0) {
                      showMoney =
                          '${MoneyTool.formatMoney(summaryDetail[targetIndex]?.feeAmountSum ?? "--")}';
                    }
                  }
                } else {
                  List<WmPoiBillChargeFeeDynamicVoList> detailList =
                      billItem?.wmPoiBillChargeFeeDynamicVoList ?? [];
                  if (detailList.isNotEmpty == true) {
                    int targetIndex = detailList.indexWhere(
                        (ele) => '${ele.billFeeTypeCode}' == element.typeCode);
                    if (targetIndex >= 0) {
                      showMoney =
                          '${MoneyTool.formatMoney(detailList[targetIndex]?.feeAmount ?? "--")}';
                    }
                  }
                }
                return Text(showMoney);
              }));
        });
      }
    } else {
      colWidget = [
        RooTableColumn<WmPoiBillChargeDynamicVoList>(
            label: '日期',
            headingTextStyle: headerTextStyle,
            cellRender: (WmPoiBillChargeDynamicVoList billItem) {
              int seq = billItem?.poiOrderPushDayseq;
              String poiOrderPushDayseqPC =
                  seq != null && seq != -1 ? '#${seq}' : '';
              return Center(
                child: Text(
                    '${DateFormat.formatYYYYMMDD(billItem?.dailyBillDateTimestamp ?? 0, splitStr: "-")}$poiOrderPushDayseqPC'),
              );
            }),
        RooTableColumn<WmPoiBillChargeDynamicVoList>(
          label: '类型',
          headingTextStyle: headerTextStyle,
          cellRender: (WmPoiBillChargeDynamicVoList billItem) {
            return Container(
              alignment: Alignment.center,
              padding: EdgeInsets.symmetric(
                horizontal: 15,
              ),
              child: Text('${billItem?.billChargeTypeName ?? ''}'),
            );
          },
        ),
        RooTableColumn<WmPoiBillChargeDynamicVoList>(
          label: '详细类型',
          headingTextStyle: headerTextStyle,
          cellRender: (WmPoiBillChargeDynamicVoList billItem) {
            String chargeName = billItem?.chargeTypeName ?? '';
            String tips = billItem?.tips;
            return Container(
                alignment: Alignment.center,
                padding: EdgeInsets.symmetric(
                  horizontal: 15,
                ),
                child: Column(
                  children: [
                    Wrap(
                      children: [
                        Container(
                            width: 19,
                            height: 19,
                            margin: EdgeInsets.only(top: 20),
                            child: Column(
                              children: isType(billItem.tags),
                            )),
                        Container(
                          margin: EdgeInsets.only(top: 20),
                          child: tips?.isNotEmpty == true
                              ? RooTooltip(
                                  target: Text(chargeName),
                                  tip: tips,
                                  lineWordCount: 20,
                                )
                              : Text(chargeName),
                        ),
                      ],
                    ),
                  ],
                ));
          },
        ),
      ];
    }
    colWidget.addAll(endTwoCol);
    // 更新数据源，订单列表头部添加占位行
    List<WmPoiBillChargeDynamicVoList> dataSource =
        widget.categoryType == 1 ? orderContent : widget.listContent;
    Widget alltable = RooTable<WmPoiBillChargeDynamicVoList>(
      dataRowHeight: 60,
      headingRowHeight: 40,
      decoration: BoxDecoration(
          color: Color(0x66EEEEEE),
          border: Border.all(color: Color(0x4DCCCCCC)),
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(8), topRight: Radius.circular(8))),
      rowColorSelector: (int index) {
        return Colors.white;
      },
      emptyProperty: RooTableEmptyProperty(
          image: Image.network(
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/19e0ae7c1b9e3965/empty_roo_new.png',
        height: 126,
        width: 126,
      )),
      dataSource: dataSource,
      appRender: null,
      columns: colWidget,
    );

    Widget leftTable = RooTable<WmPoiBillChargeDynamicVoList>(
      dataRowHeight: 60,
      headingRowHeight: 40,
      decoration: BoxDecoration(
          color: Color(0x66EEEEEE),
          border: Border.all(color: Color(0x4DCCCCCC)),
          borderRadius: BorderRadius.only(topLeft: Radius.circular(8))),
      rowColorSelector: (int index) {
        return Colors.white;
      },
      emptyProperty: RooTableEmptyProperty(
        widget: SizedBox.shrink(),
      ),
      dataSource: dataSource,
      appRender: null,
      columns: leftTableCol,
    );
    Widget rightTable = RooTable<WmPoiBillChargeDynamicVoList>(
      dataRowHeight: 60,
      headingRowHeight: 40,
      decoration: BoxDecoration(
          color: Color(0x66EEEEEE),
          border: Border.all(color: Color(0x4DCCCCCC)),
          borderRadius: BorderRadius.only(topRight: Radius.circular(8))),
      rowColorSelector: (int index) {
        return Colors.white;
      },
      emptyProperty: RooTableEmptyProperty(
        widget: SizedBox.shrink(),
      ),
      dataSource: dataSource,
      appRender: null,
      columns: rightTableCol,
    );
    return widget.categoryType == 1
        ? Stack(
            children: [
              SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  // controller: controller,
                  child: Container(
                    child: alltable,
                    width: 1200,
                  )),
              Positioned(
                child: Container(
                  decoration: BoxDecoration(
                    boxShadow: [
                      BoxShadow(
                        color: Color(0x0D000000),
                        offset: Offset(0, 1),
                        blurRadius: 20,
                      )
                    ],
                    color: Colors.white,
                  ),
                  child: leftTable,
                  width: 250,
                ),
                left: 0,
                top: 0,
              ),
              Positioned(
                child: Container(
                    decoration: BoxDecoration(
                      boxShadow: [
                        BoxShadow(
                          color: Color(0x0D000000),
                          offset: Offset(0, -1),
                          blurRadius: 20,
                        )
                      ],
                      color: Colors.white,
                    ),
                    child: rightTable,
                    width: 220),
                right: 0,
                top: 0,
              )
            ],
          )
        : alltable;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 1200,
      child: _buildTable(),
    );
  }
}
