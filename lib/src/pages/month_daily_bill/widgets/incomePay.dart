import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/month_daily_bill/widgets/chart.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/platformTool.dart';

class IncomePay extends StatelessWidget {
  IncomePay({@required this.income, @required this.pay});
  final int income;
  final int pay;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        double maxWidth = constraints.maxWidth;

        return Column(
          children: <Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Row(
                  children: <Widget>[
                    Text(
                      '营业额',
                      strutStyle: PlatformTool.isWeb
                          ? null
                          : StrutStyle(
                              forceStrutHeight: true,
                              height: 1,
                            ),
                      style: TextStyle(
                        color: Color(0xFF222222),
                        fontWeight: FontWeight.w400,
                        fontSize: 12,
                      ),
                    ),
                    SizedBox(width: 4),
                    Text(
                      MoneyTool.formatMoneyWithPrex(income),
                      style: TextStyle(
                        color: Color(0xFF00BF7F),
                        fontWeight: FontWeight.w500,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
                // 支出
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Text(
                      MoneyTool.formatMoneyWithPrex(pay),
                      style: TextStyle(
                        color: Color(0xFFFF192D),
                        fontWeight: FontWeight.w500,
                        fontSize: 12,
                      ),
                    ),
                    SizedBox(width: 4),
                    Text(
                      '支出',
                      strutStyle: PlatformTool.isWeb
                          ? null
                          : StrutStyle(
                              forceStrutHeight: true,
                              height: 1,
                            ),
                      style: TextStyle(
                        color: Color(0xFF222222),
                        fontWeight: FontWeight.w500,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 12),
            // 柱状图
            Chart(maxWidth: maxWidth, income: income, pay: pay),
          ],
        );
      },
    );
  }
}
