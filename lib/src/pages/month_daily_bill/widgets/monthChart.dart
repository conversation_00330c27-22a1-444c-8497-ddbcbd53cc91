import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/line_chart/index.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/line_chart/modal/index.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';

class Month<PERSON>hart extends StatelessWidget {
  MonthChart(
      {@required this.list, @required this.isDaily, @required this.inputMonth});
  final List<LinchartData> list;
  final bool isDaily;
  final String inputMonth;
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return LineCharts(
          width: constraints.maxWidth,
          height: 200,
          lineType: isDaily ? 0 : 1,
          inputMonth: inputMonth,
          buildTip: (nowFocusItem) {
            return list != null && list.length > 0
                ? Container(
                    padding: EdgeInsets.symmetric(vertical: 4, horizontal: 3),
                    constraints: BoxConstraints(minWidth: 50),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      color: Color(0xFF222222),
                    ),
                    child: Row(
                      children: <Widget>[
                        Container(
                          width: 6,
                          height: 6,
                          margin: EdgeInsets.only(right: 4.25),
                          color: Color(0xFF0071FB),
                        ),
                        Text.rich(
                          TextSpan(style: TextStyle(fontSize: 12), children: [
                            TextSpan(
                              // text: DateFormat.formatYYYYMMDD(nowFocusItem?.tm),
                              text: nowFocusItem?.xAxisData ?? '',
                              style: TextStyle(
                                fontWeight: FontWeight.w400,
                                color: Color.fromRGBO(255, 255, 255, 0.6),
                              ),
                            ),
                            TextSpan(text: '  '),
                            TextSpan(
                              text: MoneyTool.formatMoneyWithPrex(
                                nowFocusItem?.value ?? 0,
                              ),
                              style: TextStyle(
                                fontWeight: FontWeight.w500,
                                color: Color(0XFFFFFFFF),
                              ),
                            )
                          ]),
                        ),
                      ],
                    ),
                  )
                : SizedBox();
          },
          linChartDatas: list,
        );
      },
    );
  }
}
