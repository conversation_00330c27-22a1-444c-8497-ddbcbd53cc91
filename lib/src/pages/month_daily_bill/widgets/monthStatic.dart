import 'package:flutter/material.dart';
import 'package:mt_flutter_web_utils/mt_flutter_web_utils.dart';
import 'package:toast_widget_flutter/toast_widget_flutter.dart';
import 'package:waimai_e_base_ui/src/date_picker/date_picker.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/arrow.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/empty.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/line_chart/modal/index.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/month_daily_bill/widgets/commissionRefund.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/month_daily_bill/widgets/monthChart.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/month_daily_bill/widgets/otherBill.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/month_daily_bill/widgets/summary.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/month_daily_bill/widgets/waimaiBill.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/monthBill.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/monthBill/monthOverview.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/monthBill/monthsIncomeOverview.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/uiTools.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/util/index.dart';

class MonthBill extends StatelessWidget {
  MonthBill({String pageName, this.params})
      : this.pageName =
            pageName ?? (params != null ? params['mtf_page'].toString() : '');
  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFF5F6FA),
      appBar: UITools.renderNavbar(context: context, title: '月账单'),
      body: MonthStatic(params: params),
    );
  }
}

// 某月账单统计
class MonthStatic extends StatefulWidget {
  MonthStatic({this.params});
  final Map<dynamic, dynamic> params;

  @override
  MonthStaticState createState() => MonthStaticState();
}

class MonthStaticState extends State<MonthStatic> with MonthBillMixin {
  final bool isPC = PlatformTools.isPC;
  bool isMultiple = false;
  MonthOverviewModel monthOverview;
  // 用于保存传进来是的 2020/10 -- 2020/10/01
  String monthDay = '';
  int monthIndex = 0;
  String inputMonth;
  List<LinchartData> chartList = [];
  List<LinchartData> fourMonthChartList = [];
  // PC支持6个月范围
  int pcMonthRange = 5;
  // App支持4个月范围
  int appMonthRange = 3;

  @override
  void initState() {
    super.initState();
    Util.isMultiple().then((value) {
      print('initState---isMultiple$value');
      isMultiple = value;
      if (value == false) {
        fetchData();
      } else {
        fetchMultiGray();
      }
    });
    // 2020/01/10
    if (widget.params != null) {
      inputMonth = widget.params['monthBillDate'];
    } else {
      inputMonth = DateFormat.formatYYYYMM(DateTime.now());
    }

    dealInputMonth(inputMonth);

    ReportLX.pv(pageKeyInfo, cid);
  }

  dealInputMonth(String input) {
    if (input != null && input != '') {
      // 2020/01
      monthDay = input + '/01';
      // 09
      monthIndex = DateFormat.formatStringToDateTime(monthDay).month;
      // inputMonth
      inputMonth = input;
    } else {
      inputMonth = DateFormat.formatYYYYMM(DateTime.now());
      monthDay =
          DateFormat.formatYYYYMMDD(DateTime.now().millisecondsSinceEpoch);
      monthIndex = DateTime.now().month;
    }
  }

  getFirstLastMap() {
    List<String> list =
        DateFormat.getFirstLastDay(DateFormat.formatStringToDateTime(monthDay));
    Map map = Map();
    map.putIfAbsent('startDate', () => list[0]);
    map.putIfAbsent('endDate', () => list[1]);
    return map;
  }

  fetchData() {
    print('isMultiple----------$isMultiple');
    if (isMultiple == true) return;
    Loading.showLoading();
    fetchMonthOverview(getFirstLastMap()).then((monthOverviewModel) {
      monthOverview = monthOverviewModel;
      setState(() {});
      Loading.dismissLoading();
    });
    // 查询报表数据
    fetchIncomeList(getFirstLastMap()).then((List<int> list) {
      // 当有多少天，进行补齐
      DateTime today = DateFormat.formatStringToDateTime(inputMonth + '/01');
      DateTime next = DateTime(today.year, today.month + 1, 1);
      DateTime thisMonthLastDay =
          DateTime.fromMillisecondsSinceEpoch(next.millisecondsSinceEpoch - 1);
      int days = thisMonthLastDay.day;
      int diff = days - list.length;
      for (int i = 0; i < diff; i++) {
        list.add(0);
      }
      chartList = [];
      list.asMap().forEach((index, value) {
        String date = inputMonth + '/${index + 1}';
        int tm = DateFormat.formatStringToTm(date);
        chartList.add(
          LinchartData(
            xAxisData: DateFormat.formatMMDD(tm),
            value: value,
            tm: tm,
          ),
        );
      });
      setState(() {});
      ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_gmg0btvv_mv');
    });

    // 查近四个月数据
    fetch4MonthsIncome(get4Months()).then((res) {
      List<MonthsIncomeOverviewModel> list = res ?? [];
      fourMonthChartList = [];
      list = list.reversed.toList();
      list?.forEach((e) {
        fourMonthChartList.add(
          LinchartData(
            xAxisData: e.date,
            value: e.amount,
            tm: 0,
          ),
        );
      });
      setState(() {});
    });
  }

  fetchMultiGray() {
    fetchMultiPoiGray().then((inGray) {
      if (inGray) {
        MTFlutterWebUtils.bridgeJump('/finance/web/financeStatistic');
      } else {
        setState(() {});
      }
    }).catchError((e) {
      setState(() {});
    });
  }

  get4Months() {
    int monthNum = isPC ? pcMonthRange : appMonthRange;
    DateTime input = DateTime.now();
    DateTime fourMonthAgo = DateTime(input.year, input.month - monthNum, 1);
    return {
      "startDate": DateFormat.formatYYYYMM(fourMonthAgo) + '/01',
      "endDate": DateFormat.formatYYYYMM(input) + '/01'
    };
  }

  /// PC上支持6个月数据
  _buildMonthChangeArrow({bool isPrev = false}) {
    // 最大时间
    DateTime now = DateTime.now();
    DateTime currentTime = DateFormat.formatStringToDateTime(monthDay);
    Widget changeMonth = GestureDetector(
      child: Container(
        width: 22,
        height: 22,
        child: Image.network(
          isPrev
              ? 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/fb079bf8755c1017/month_left_arrow.png'
              : 'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/00fa006f9114a7aa/month_right_arrow.png',
        ),
      ),
      onTap: () {
        DateTime newTime;
        if (isPrev && currentTime.month > now.month - pcMonthRange) {
          newTime = DateTime(now.year, currentTime.month - 1, 1);
        }
        if (!isPrev && currentTime.month < now.month) {
          newTime = DateTime(now.year, currentTime.month + 1, 1);
        }
        String newDate = DateFormat.formatYYYYMM(newTime);
        dealInputMonth(newDate);
        fetchData();
      },
    );
    return changeMonth;
  }

  // 9月账单统计 - 月份选择
  monthBillTitle() {
    Widget monthName = Text(
      '$monthIndex月账单统计',
      style: TextStyle(
        color: Color(0xFF222222),
        fontWeight: FontWeight.w600,
        fontSize: 20,
        height: 1.1,
      ),
    );
    // 最大时间
    DateTime now = DateTime.now();
    DateTime currentTime = DateFormat.formatStringToDateTime(monthDay);
    DateTime minTime = DateTime(now.year, now.month - appMonthRange, 1);
    bool hasPrev = currentTime.month > now.month - pcMonthRange;
    bool hasNext = currentTime.month < now.month;

    Widget pcTitle = Padding(
      padding: EdgeInsets.only(
        top: 30,
      ),
      child: Row(
        children: [
          Padding(
            padding: EdgeInsets.only(right: 8),
            child: hasPrev
                ? _buildMonthChangeArrow(isPrev: true)
                : SizedBox(
                    width: 22,
                  ),
          ),
          monthName,
          Padding(
            padding: EdgeInsets.only(left: 8),
            child: hasNext
                ? _buildMonthChangeArrow(isPrev: false)
                : SizedBox(
                    width: 22,
                  ),
          )
        ],
      ),
    );

    // 9月账单统计
    Widget title = Container(
      padding: EdgeInsets.all(12),
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          monthName,
          GestureDetector(
            child: Row(children: <Widget>[
              Text(
                inputMonth ?? '',
                style: TextStyle(
                  color: Color(0xFF222222),
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                ),
              ),
              ArrowIcon(
                direction: DirectionEnum.down,
              )
            ]),
            onTap: () {
              DateTime currentTime =
                  DateFormat.formatStringToDateTime(monthDay);
              SAKDatePicker.showDatePicker(
                type: SAKDatePickerType.yearMonth,
                context: context,
                minTime: minTime,
                maxTime: now,
                currentTime: currentTime,
                onConfirm: (DateTime d) {
                  String newDate = DateFormat.formatYYYYMM(d);
                  dealInputMonth(newDate);
                  fetchData();
                },
              );
              ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_gmg0btvv_mc');
            },
          )
        ],
      ),
    );
    return isPC ? pcTitle : title;
  }

  Widget _buildCard() {
    return Column(
      children: <Widget>[
        isPC
            ? SizedBox.shrink()
            : Summary(
                monthOverview: monthOverview,
                isDaily: false,
              ),
        SizedBox(height: 16),
        WaimaiBill(
          monthOverview: monthOverview,
          isDaily: isPC ? true : false,
          dateDuration: getFirstLastMap(),
        ),
        SizedBox(height: 16),
        // 其他类
        OtherBill(
          monthOverview: monthOverview,
          isDaily: isPC ? true : false,
          dateDuration: getFirstLastMap(),
        ),
        SizedBox(height: 16),

        CommissionRefund(
          monthOverview: monthOverview,
          isDaily: isPC ? true : false,
          dateDuration: getFirstLastMap(),
        ),
        isPC ? SizedBox.shrink() : monthDailyChart(true),
      ],
    );
  }

  monthBillContentPC() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 280,
          height: 299,
          padding: EdgeInsets.only(
            right: 50,
            top: 10,
          ),
          margin: EdgeInsets.only(
            right: 50,
          ),
          decoration: BoxDecoration(
            border: Border(
              right: BorderSide(width: 1, color: Color(0xFFEEEEEE)),
            ),
          ),
          child: Summary(
            monthOverview: monthOverview,
            isDaily: false,
          ),
        ),
        Expanded(
          child: _buildCard(),
        ),
      ],
    );
  }

  // 整个Card部分
  monthBillContent() {
    // shadow区域
    Widget content = Container(
      margin: EdgeInsets.fromLTRB(12, 0, 12, 12),
      padding: EdgeInsets.fromLTRB(16, 0, 16, 24),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Color(0x0D000000),
            offset: Offset(0, 1),
            blurRadius: 10.5,
          )
        ],
        borderRadius: BorderRadius.circular(10.5),
      ),
      child: _buildCard(),
    );
    return content;
  }

  chartTitle(bool dailyOrFourMonth) {
    double fontSizeTitle = !isPC && dailyOrFourMonth ? 16 : 20;
    return Container(
      color: Colors.white,
      padding: EdgeInsets.fromLTRB(0, 30, 0, 12),
      child: Text(
        dailyOrFourMonth ? '本月每日收入趋势' : '月度收入趋势',
        style: TextStyle(
          color: Color(0xFF222222),
          fontWeight: FontWeight.w600,
          fontSize: fontSizeTitle,
        ),
      ),
    );
  }

  // 本月每日收入趋势
  monthDailyChart(bool dailyOrFourMonth) {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          chartTitle(dailyOrFourMonth),
          MonthChart(
            list: dailyOrFourMonth ? chartList : fourMonthChartList,
            isDaily: dailyOrFourMonth,
            inputMonth: inputMonth,
          ),
        ],
      ),
    );
  }

  // 四个月的收入趋势

  Widget _buildContainerWrapper(Widget child) {
    return Container(
      margin: EdgeInsets.only(
        bottom: 20,
      ),
      padding: EdgeInsets.fromLTRB(30, 0, 30, 30),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(
          Radius.circular(
            8,
          ),
        ),
      ),
      child: child,
    );
  }

  Widget _buildPC() {
    return Column(children: [
      _buildContainerWrapper(
        monthDailyChart(false),
      ),
      _buildContainerWrapper(Column(
        children: [
          monthBillTitle(),
          monthBillContentPC(),
        ],
      )),
      _buildContainerWrapper(
        monthDailyChart(true),
      ),
    ]);
  }

  Widget _buildApp() {
    return Column(
      children: <Widget>[
        Container(
          padding: EdgeInsets.all(12),
          color: Colors.white,
          child: monthDailyChart(false),
        ),
        monthBillTitle(),
        Stack(
          children: <Widget>[
            Container(
              height: 32,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Color(0xFFFFFFFF),
                      Color(0xFFF5F6FA),
                    ]),
              ),
            ),
            monthBillContent(),
          ],
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return isMultiple
        ? Empty(
            title: '此功能暂不适用于全部门店，请切换单门店使用',
          )
        : monthOverview != null
            ? SingleChildScrollView(child: isPC ? _buildPC() : _buildApp())
            : SizedBox();
  }
}
