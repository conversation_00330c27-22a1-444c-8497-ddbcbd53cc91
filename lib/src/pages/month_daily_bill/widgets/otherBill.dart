import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/month_daily_bill/widgets/otherBillOneRow.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/month_daily_bill/widgets/title.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/monthBill/monthOverview.dart';

class OtherBill extends StatefulWidget {
  OtherBill(
      {@required this.monthOverview,
      @required this.isDaily,
      this.dateDuration});

  final MonthOverviewModel monthOverview;
  final bool isDaily;
  final Map dateDuration;

  @override
  OtherBillState createState() => OtherBillState();
}

class OtherBillState extends State<OtherBill> {
  @override
  void initState() {
    super.initState();
  }

  // 订单退款 - 4笔 - ¥1198.2
  otherBillDetail(String label, int billCnt, int moneyCnt,
      int billChargeTypeCode, int isShowDetail, String remark) {
    return OtherBillOneRow(
      label: label,
      billCnt: billCnt,
      moneyCnt: moneyCnt,
      billChargeTypeCode: billChargeTypeCode,
      isDaily: widget.isDaily,
      dateDuration: widget.dateDuration,
      isShowDetail: (isShowDetail ?? 0) == 1,
      remark: remark ?? '',
    );
  }

  // 其他类型的数据
  _buildOtherBill() {
    List<BillChargeTypeDetails> billChargeTypeDetails =
        widget.monthOverview?.billChargeTypeDetails ?? [];

    int total = 0;
    billChargeTypeDetails.forEach((e) {
      if (e.billChargeTypeCode == 16 ||
          e.billChargeTypeCode == 17 ||
          e.orderCategory != 1 && e.orderCategory != 3) {
        total += e.billChargeAmountSum;
      }
    });

    List<Widget> list = [];

    if (billChargeTypeDetails != null) {
      // 筛选非外卖订单
      billChargeTypeDetails = billChargeTypeDetails
          .where((element) =>
              element.billChargeTypeCode == 16 ||
              element.billChargeTypeCode == 17 ||
              element.orderCategory != 1 && element.orderCategory != 3)
          .toList();
      if (billChargeTypeDetails.length > 0) {
        // 增加title
        list.add(BillTitle(
          label: '其他类',
          count: total,
        ));
      }
      billChargeTypeDetails.forEach((e) {
        list.add(otherBillDetail(
          e.billChargeTypeName,
          e.billChargeCount,
          e.billChargeAmountSum,
          e.billChargeTypeCode,
          e.isShowDetail,
          e.remark,
        ));
      });
    }

    return Column(
      children: list,
    );
  }

  @override
  Widget build(BuildContext context) {
    return _buildOtherBill();
  }
}
