import 'package:flutter/material.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:waimai_e_fe_ffw_utils/platform_tool.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/arrow.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/modal.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/question.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/month_daily_bill/widgets/colorMoney.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/transactionType/index.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';

// 在外卖订单中也会包含一行和其他类中的数据 = 订单退款，把这个抽离出来
class OtherBillOneRow extends StatelessWidget with MonthBillMixin {
  OtherBillOneRow({
    @required this.label,
    @required this.billCnt,
    @required this.moneyCnt,
    @required this.billChargeTypeCode,
    @required this.dateDuration,
    @required this.isDaily,
    this.isShowDetail,
    this.remark,
  });

  final String label;
  final int billCnt;
  final int moneyCnt;
  final int billChargeTypeCode;
  final bool isDaily;
  final Map dateDuration;
  /**
   * 是否有问号提示内容
   */
  final bool isShowDetail;
  /**
   * 弹窗内的富文本内容
   */
  final String remark;

  final bool isPC = PlatformTools.isPC;

  _buildOneRow(BuildContext context) {
    bool hasShowDetail = isShowDetail ?? false;
    String dialogContent = remark ?? "";
    return GestureDetector(
      onTap: () {
        if (isDaily == true) {
          return;
        }
        // startDate=2021-06-01&endDate=2021-06-30&wmPoiId=6762916&billChargeTypeCode=2
        String startDate = dateDuration['startDate'];
        String endDate = dateDuration['endDate'];

        Map<String, dynamic> params = {
          "startDate": startDate,
          "endDate": endDate,
          "billChargeTypeCode": billChargeTypeCode,
        };
        Modal.openDialogPCAndPageApp(
          context,
          '/tradingType',
          TradingTypePage(
            params: params,
          ),
          title: '交易类型',
          params: params,
          width: 400,
          height: 500,
        );
        ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_fdna3aie_mc');
      },
      child: Container(
        margin: EdgeInsets.only(top: 12),
        child: Flex(
          direction: Axis.horizontal,
          // mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      label,
                      style: TextStyle(
                        color: Color(0xFF222222),
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  hasShowDetail
                      ? Question(
                          isDark: true,
                          onTap: () {
                            showDialog(
                                context: context,
                                builder: (context) {
                                  return RooDialog(
                                    context,
                                    showInput: false,
                                    content: RooRichText(
                                      content: dialogContent,
                                      padding: EdgeInsets.all(8.0),
                                    ),
                                    titleText: label,
                                    confirmText: "我知道了",
                                  );
                                });
                          },
                        )
                      : SizedBox.shrink(),
                ],
              ),
            ),
            Expanded(
              child: Text(
                '$billCnt笔',
                style: TextStyle(
                  color: Color(0xFF666666),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            Expanded(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: <Widget>[
                  ColorMoney(money: moneyCnt),
                  isDaily == true
                      ? SizedBox()
                      : ArrowIcon(
                          direction: DirectionEnum.right,
                        )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return _buildOneRow(context);
  }
}
