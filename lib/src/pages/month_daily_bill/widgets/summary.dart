import 'package:flutter/material.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:roo_flutter/tools/response_system.dart';
import 'package:waimai_e_fe_ffw_utils/platform_tool.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/card.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/question.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/monthBill/monthOverview.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/gradient.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/platformTool.dart';

// 整个月/日的汇总数据，信息有：多少单/多少钱/解释文案
class Summary extends StatelessWidget with DailyBillMixin {
  Summary({@required this.monthOverview, @required this.isDaily});
  final MonthOverviewModel monthOverview;
  final bool isDaily;
  final bool isPC = PlatformTools.isPC;

  _buildBillsWiget() {
    String monthTitle = isPC ? '累计账单金额' : '共${monthOverview?.orderNum}单，累计账单金额';
    TextStyle ts = isPC
        ? TextStyle(
            color: Color(0xFF222222),
            fontWeight: FontWeight.w500,
            fontSize: 18,
          )
        : TextStyle(
            color: Color(0xFF666666),
            fontWeight: FontWeight.w400,
            fontSize: 14,
          );
    return Text(
      isDaily ? '账单总金额' : monthTitle,
      style: ts,
    );
  }

  _buildBillsNumWiget() {
    return Text(
      '共${monthOverview?.orderNum}单',
      style: TextStyle(fontWeight: FontWeight.w500),
    );
  }

  _buildMoneyWidget() {
    Color color = isPC ? greenMoney : Color(0xFF222222);
    return FittedBox(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Text(
            '¥',
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w500,
              fontSize: ResponsiveSystem.bothAppPc(
                runApp: 18.0,
                runPc: 24.0,
              ),
            ),
          ),
          Text(
            MoneyTool.formatMoney(monthOverview?.totalAmount),
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w600,
              fontSize: ResponsiveSystem.bothAppPc(
                runApp: 28.0,
                runPc: 38.0,
              ),
            ),
          )
        ],
      ),
    );
  }

  _buildTipWiget(String text, BuildContext context, {String questionDesc}) {
    if (text == null || text.length <= 0) {
      return SizedBox();
    } else {
      return Padding(
          padding: EdgeInsets.only(
            top: 5,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                text,
                strutStyle: PlatformTool.isWeb
                    ? null
                    : StrutStyle(
                        forceStrutHeight: true,
                        height: 1,
                      ),
                style: TextStyle(
                  color: Color(0xFF666666),
                  fontWeight: FontWeight.w400,
                  fontSize: 12,
                ),
              ),
              questionDesc != null
                  ? Question(
                      isDark: true,
                      onTap: () {
                        showDialog(
                            context: context,
                            builder: (context) {
                              return RooDialog(
                                context,
                                showInput: false,
                                content: Text(questionDesc),
                                titleText: "详情",
                                confirmText: "我知道了",
                              );
                            });
                      },
                    )
                  // 6.55之前不支持
                  // RooTooltip(
                  //   title: '详情',
                  //   target: QuestionIcon(),
                  //   tip: questionDesc,
                  // )
                  : SizedBox.shrink(),
            ],
          ));
    }
  }

  // 结算标
  _buildSettleTagWidget(BuildContext context) {
    bool settled = monthOverview?.settleState == 1;
    return FittedBox(
      child: Text(
        settled ? '已汇入余额' : '预计${monthOverview?.settleDate ?? ''}结算',
        strutStyle: StrutStyle(
          forceStrutHeight: true,
        ),
        style: TextStyle(
          color: settled ? Color(0xFF222222) : Color(0xFFFF6A00),
          fontWeight: FontWeight.w500,
          fontSize: 14,
        ),
      ),
    );
  }

  _buildDailyWidget(BuildContext context) {
    Widget con = Column(
      crossAxisAlignment: ResponsiveSystem.bothAppPc(
          runApp: CrossAxisAlignment.center, runPc: CrossAxisAlignment.start),
      children: <Widget>[
        _buildBillsWiget(),
        SizedBox(height: 5),
        _buildMoneyWidget(),
        _buildTipWiget('账单金额 = 订单类金额 + 其他类金额 + 激励返还类', context),
        _buildTipWiget(monthOverview?.commissionDesc ?? '', context,
            questionDesc: '该佣金为当前生效的佣金比例，中间可能因为更换配送方式等原因导致佣金比例发生变化'),
        SizedBox(height: 16),
        _buildSettleTagWidget(context),
      ],
    );
    return Container(
      padding:
          EdgeInsets.all(ResponsiveSystem.bothAppPc(runApp: 24.0, runPc: 14.0)),
      child: con,
    );
  }

  _buildMonthWidget() {
    return Container(
      padding: EdgeInsets.fromLTRB(0, 24, 0, 24),
      width: double.infinity,
      decoration: BoxDecoration(
        border: isPC
            ? null
            : Border(
                bottom: BorderSide(width: 1, color: Color(0xFFEEEEEE)),
              ),
      ),
      child: Column(
        crossAxisAlignment:
            isPC ? CrossAxisAlignment.start : CrossAxisAlignment.center,
        children: <Widget>[
          _buildBillsWiget(),
          SizedBox(height: 5),
          _buildMoneyWidget(),
          SizedBox(height: 2),
          isPC ? _buildBillsNumWiget() : SizedBox.shrink(),
        ],
      ),
    );
  }

  Widget _buildQR() {
    return Column(
      children: [
        Text('微信扫描二维码，关注美团外卖商户通公众号'),
        Text('绑定账号，第一时间获取账单信息'),
        Container(
          width: 120,
          height: 120,
          child: Image.network(
              '//s3plus.meituan.net/v1/mss_c4375b35f5cb4e678b5b55a48c40cf9d/staticfile/waimaie-qrcode.jpg'),
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return isDaily
        ? RadiusCard(
            child: ResponsiveSystem(
            app: Container(
                child: _buildDailyWidget(context), width: double.infinity),
            pc: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildDailyWidget(context),
                _buildQR(),
              ],
            ),
          ))
        : _buildMonthWidget();
  }
}
