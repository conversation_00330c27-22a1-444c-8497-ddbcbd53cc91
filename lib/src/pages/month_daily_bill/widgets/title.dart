import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';

// 订单类和其他类的文案以及对齐金额
class BillTitle extends StatelessWidget {
  BillTitle({@required this.label, @required this.count});
  final String label;
  final int count;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: <Widget>[
        Text(
          label,
          style: TextStyle(
            color: Color(0xFF222222),
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
        Text(
          '${MoneyTool.formatMoneyWithPrex(count)}',
          style: TextStyle(
            color: Color(0xFF222222),
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
      ],
    );
  }
}
