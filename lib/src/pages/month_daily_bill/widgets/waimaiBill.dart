import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/month_daily_bill/widgets/colorMoney.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/month_daily_bill/widgets/incomePay.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/month_daily_bill/widgets/otherBillOneRow.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/month_daily_bill/widgets/title.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/monthBill/monthOverview.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';

class WaimaiBill extends StatefulWidget {
  WaimaiBill(
      {@required this.monthOverview,
      this.refundNum,
      @required this.isDaily,
      this.dateDuration});
  final MonthOverviewModel monthOverview;
  final int refundNum;
  final bool isDaily;
  final Map dateDuration;
  @override
  WaimaiBillState createState() => WaimaiBillState();
}

class WaimaiBillState extends State<WaimaiBill> {
  MonthOverviewModel monthOverview;
  BillChargeTypeDetails waimaiDetail;

  @override
  void initState() {
    super.initState();
  }

  // '商品原价', '¥312,039.92'
  waimaiBillDetail(String label, int money) {
    return Container(
      margin: EdgeInsets.only(top: 11.5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Text(
            label,
            style: TextStyle(
              color: Color(0xFF666666),
              fontWeight: FontWeight.w400,
              fontSize: 11,
            ),
          ),
          Text(
            MoneyTool.formatMoneyWithPrex(money),
            style: TextStyle(
              color: Color(0xFF666666),
              fontWeight: FontWeight.w400,
              fontSize: 11,
            ),
          ),
        ],
      ),
    );
  }

  // 外卖订单类
  _buildWaimaiBill() {
    List<BillChargeTypeDetails> oriBillChargeTypeDetails =
        widget.monthOverview?.billChargeTypeDetails ?? [];
    List<BillChargeTypeDetails> billChargeTypeDetails = [];
    // 筛选外卖订单
    billChargeTypeDetails = oriBillChargeTypeDetails
        .where((element) => element.billChargeTypeCode == 1)
        .toList();
    if (billChargeTypeDetails != null && billChargeTypeDetails.length > 0) {
      waimaiDetail = billChargeTypeDetails[0];
    } else {
      waimaiDetail = BillChargeTypeDetails(
          billChargeAmountSum: 0, billChargeTypeName: '外卖订单');
    }

    Widget title = waimaiDetail != null
        ? Flex(
            direction: Axis.horizontal,
            children: <Widget>[
              Expanded(
                child: Text(
                  // 外卖订单
                  waimaiDetail.billChargeTypeName,
                  style: TextStyle(
                    color: Color(0xFF222222),
                    fontWeight: FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
              ),
              Expanded(
                child: Container(
                  alignment: Alignment.center,
                  child: Text(
                    '${waimaiDetail.billChargeCount ?? 0}笔',
                    style: TextStyle(
                      color: Color(0xFF666666),
                      fontWeight: FontWeight.w500,
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
              Expanded(
                child: Container(
                  alignment: Alignment.centerRight,
                  child: ColorMoney(
                    money: waimaiDetail.billChargeAmountSum,
                  ),
                ),
              )
            ],
          )
        : SizedBox();

    List<DailyBillFeeDetails> incomeList = [];
    List<DailyBillFeeDetails> payList = [];
    if (waimaiDetail?.dailyBillFeeDetails != null) {
      waimaiDetail.dailyBillFeeDetails?.forEach((element) {
        if (element.feeAmountSum > 0) {
          incomeList.add(element);
        } else if (element.feeAmountSum < 0) {
          payList.add(element);
        }
      });
    }

    List<Widget> incomeWiget = [];
    List<Widget> payWiget = [];
    int incomeCnt = 0;
    int payCnt = 0;
    incomeList.forEach((e) {
      incomeCnt += e.feeAmountSum;
      incomeWiget.add(waimaiBillDetail(e.billFeeTypeName, e.feeAmountSum));
    });

    payList.forEach((e) {
      payCnt += e.feeAmountSum;
      payWiget.add(waimaiBillDetail(e.billFeeTypeName, e.feeAmountSum));
    });

    Widget content = Container(
      padding: EdgeInsets.fromLTRB(10.5, 13, 10.5, 13),
      child: Column(
        children: <Widget>[
          // 红绿柱图
          IncomePay(
            income: incomeCnt,
            pay: payCnt,
          ),
          // 灰色框内容
          Flex(
            direction: Axis.horizontal,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(children: <Widget>[...incomeWiget, ...payWiget]),
              ),
            ],
          )
        ],
      ),
      decoration: BoxDecoration(
        color: Color(0xFFF5F6FA),
        borderRadius: BorderRadius.circular(6.5),
      ),
    );
    return Column(
      children: <Widget>[title, SizedBox(height: 8), content],
    );
  }

  // 订单退款
  _buildWaimaiReturn() {
    List<BillChargeTypeDetails> oriBillChargeTypeDetails =
        widget.monthOverview?.billChargeTypeDetails ?? [];
    // 筛选订单退款
    List<BillChargeTypeDetails> billChargeTypeDetails = oriBillChargeTypeDetails
        .where((element) => element.billChargeTypeCode == 2)
        .toList();
    BillChargeTypeDetails returnDetail;
    if (billChargeTypeDetails != null && billChargeTypeDetails.length > 0) {
      returnDetail = billChargeTypeDetails[0];
    }
    String label = returnDetail?.billChargeTypeName;
    int billCnt = returnDetail?.billChargeCount;
    int moneyCnt = returnDetail?.billChargeAmountSum;
    return returnDetail != null
        ? OtherBillOneRow(
            label: label,
            billCnt: billCnt,
            moneyCnt: moneyCnt,
            billChargeTypeCode: returnDetail?.billChargeTypeCode,
            isDaily: widget.isDaily,
            dateDuration: widget.dateDuration,
            isShowDetail: (returnDetail?.isShowDetail ?? 0) == 1,
            remark: returnDetail?.remark ?? "",
          )
        : SizedBox();
  }

  getWaimaiCnt() {
    List<BillChargeTypeDetails> oriBillChargeTypeDetails =
        widget.monthOverview?.billChargeTypeDetails ?? [];
    // 筛选外卖订单+退款
    List<BillChargeTypeDetails> billChargeTypeDetails = oriBillChargeTypeDetails
        .where((element) => (element.orderCategory == 1 &&
            element.billChargeTypeCode != 16 &&

            /// 暂时方案：订单类不包含部分退 计算金额有误，后续优化
            element.billChargeTypeCode != 17))
        .toList();
    // 总钱数，外卖订单+订单退款
    int waimaiCnt = 0;
    billChargeTypeDetails?.forEach((element) {
      waimaiCnt += element.billChargeAmountSum;
    });
    return waimaiCnt;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        BillTitle(
          label: '订单类',
          count: getWaimaiCnt() ?? 0,
        ),
        SizedBox(height: 17),
        // 外卖订单
        _buildWaimaiBill(),
        // 订单退款
        _buildWaimaiReturn(),
      ],
    );
  }
}
