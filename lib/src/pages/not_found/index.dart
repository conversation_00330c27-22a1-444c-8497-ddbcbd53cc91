import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/common/flap.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/empty.dart';
// import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
//     if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';

@Flap('finance')
class NotFindPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
          child: Column(
        children: [
          Container(
              child: Empty(
            title: '您当前访问的页面地址有误',
            margin: EdgeInsets.only(top: 80, bottom: 20),
          )),
          // TextButton(
          //     onPressed: () {
          //       RouterTools.flutterPageUrl(context, '/home');
          //     },
          //     child: Text('去首页')),
        ],
      )),
    );
  }
}
