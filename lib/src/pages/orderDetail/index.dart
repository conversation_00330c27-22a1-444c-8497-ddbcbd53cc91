import 'package:flutter/material.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:waimai_e_fe_flutter_finance/src/common/flap.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/customWillPopScope.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/modal.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/toolTip.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/orderDetail/widgets/orderInfo.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/orderDetail/widgets/orderLevel1.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/orderDetail/widgets/orderLevel2.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/order.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/order/billChargeDetailDynamic.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/uiTools.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/util/index.dart';

@Flap('finance')
class OrderDetailPage extends StatelessWidget {
  OrderDetailPage({this.params, this.pageName});
  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  Widget build(BuildContext context) {
    String routeFrom = params != null ? params['from'] : null;
    return LoadingPage(
        child: CustomWillPopScope(
      child: Scaffold(
        backgroundColor: Color(0xFFF5F6FA),
        appBar: UITools.renderNavbar(
            context: context,
            title: '交易详情',
            onLeftIconTap: () {
              Util.back2Steps(context);
            }),
        body: OrderDetail(
          pageName: pageName,
          params: params,
        ),
      ),
      from: routeFrom,
      forbiddenPcBack: true,
    ));
  }
}

class OrderDetail extends StatefulWidget {
  OrderDetail({String pageName, this.params})
      : this.pageName =
            pageName ?? (params != null ? params['mtf_page'].toString() : '');
  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  OrderDetailState createState() => OrderDetailState();
}

class OrderDetailState extends State<OrderDetail> with OrderQueryMixin {
  BillChargeDetailDynamicModel billChargeDetailDynamic;

  /// 是否展示 Web 端计算公式的 Hover 提示
  bool showCalcFormula = false;

  /// Question 所在的 Offset
  Offset offset = Offset(0, 0);

  @override
  void initState() {
    super.initState();
    fetchData();
    ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_58c8c9wy_mv');
  }

  fetchData() {
    // 处理函数
    dealData(BillChargeDetailDynamicModel billChargeDetailDynamicModel) {
      if (this.mounted) {
        setState(() {
          billChargeDetailDynamic = billChargeDetailDynamicModel;
        });
      }
      Loading.dismissLoading();
    }

    Loading.showLoading();
    // 查询来源：xf-先富平台，用于区分接口
    String platform;
    Map<String, dynamic> map = widget?.params == null
        ? {}
        : {
            "chargeTypeCode": widget?.params['chargeTypeCode'],
            "billChargeId": widget?.params['billChargeId'],
            "wmOrderViewId": widget?.params['wmOrderViewId'],
            "dailyBillDate": widget?.params['dailyBillDate'],
            "toWmPoiId": widget?.params['toWmPoiId']
          };

    if (widget?.params != null) {
      platform = widget?.params['platform'];
    }
    if (platform != null) {
      map['platform'] = platform;
      map['wmPoiId'] = widget?.params['wmPoiId'];
      map['toWmPoiId'] = widget?.params['toWmPoiId'];
      // 先富平台
      if (platform == 'xf' || platform == 'xfs') {
        fetchXainfuBillCharge(map)
            .then((BillChargeDetailDynamicModel billChargeDetailDynamicModel) {
          dealData(billChargeDetailDynamicModel);
        });
      }
    } else {
      String dailyBillDate = map['dailyBillDate'];
      if (dailyBillDate != null) {
        String newDailyBillDate = dailyBillDate.replaceAll('-', '/');
        map['dailyBillDate'] = newDailyBillDate;
      }
      fetchNewBillCharge(map)
          .then((BillChargeDetailDynamicModel billChargeDetailDynamicModel) {
        dealData(billChargeDetailDynamicModel);
      });
    }
  }

  moneyWidget(int value, bool isPhf) {
    // 拼好饭业务不允许出现负数金额突出展示
    Color negativeMoneyColor =
        isPhf == true ? Color(0xFF222222) : Color(0xFFFF192D);
    return FittedBox(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Text(
            value >= 0 ? '¥' : '-¥',
            style: TextStyle(
              color: value > 0 ? Color(0xFF222222) : negativeMoneyColor,
              fontWeight: FontWeight.w700,
              fontSize: 18,
            ),
          ),
          Text(
            MoneyTool.formatMoneyNoPrex(value),
            style: TextStyle(
              color: value > 0 ? Color(0xFF222222) : negativeMoneyColor,
              fontWeight: FontWeight.w700,
              fontSize: 28,
            ),
          )
        ],
      ),
    );
  }

  buildPhfDetails() {
    final List<Widget> children = <Widget>[];

    billChargeDetailDynamic.phfData.subDataList?.forEach((element) {
      children.add(Container(
        padding: EdgeInsets.only(top: 16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              element.name,
              style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF222222),
                  fontWeight: FontWeight.w500),
            ),
            Expanded(child: SizedBox()),
            Text(
              element.info,
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF222222),
              ),
            )
          ],
        ),
      ));

      if (element.remark != null) {
        children.add(Container(
          padding: EdgeInsets.only(top: 9),
          child: Text(
            element.remark,
            style: TextStyle(fontSize: 12, color: Color(0xFF999999)),
            textAlign: TextAlign.left,
          ),
        ));
      }
    });

    children.add(Container(
      padding: EdgeInsets.only(top: 44),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '合计',
            style: TextStyle(
                fontSize: 16,
                color: Color(0xFF222222),
                fontWeight: FontWeight.w500),
          ),
          Expanded(child: SizedBox()),
          Text(
            billChargeDetailDynamic.phfData.totalAmount,
            style: TextStyle(
                fontSize: 16,
                color: Color(0xFF222222),
                fontWeight: FontWeight.w700),
          )
        ],
      ),
    ));

    return showRooBottomModal(
        context: context,
        builder: (context) {
          var modal = RooBottomModal(
              title: Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                child: Row(
                  children: [
                    Text(
                      billChargeDetailDynamic.phfData.title,
                      style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 16,
                          color: Color(0xFF222222)),
                    ),
                    SizedBox(width: 6),
                    Image(
                      width: 64,
                      height: 18,
                      image: NetworkImage(
                          'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/da5b2d8c385c51e6/icon.png'),
                    ),
                  ],
                ),
              ),
              height: 320,
              bottomMainText: "我知道了",
              contentContainer: Container(
                padding: EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: children,
                ),
              ),
              bottomMainCallback: () {
                Navigator.pop(context);
              });
          return modal;
        });
  }

  buildPhfTips() {
    return RooTopTip(
        padding: EdgeInsets.all(10),
        leftDrawable: Padding(
          padding: EdgeInsets.only(
            right: 6,
          ),
          child: Image(
            width: 64,
            height: 18,
            image: NetworkImage(
                'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/da5b2d8c385c51e6/icon.png'),
          ),
        ),
        content:
            '${billChargeDetailDynamic.phfData.title} ${billChargeDetailDynamic.phfData.totalAmount}',
        textStyle: TextStyle(color: Color(0xFF222222), fontSize: 12),
        rightButtonTxt: "详情",
        showRightButton: false,
        actionIcon: Row(
          children: [
            Text("查看详情",
                style: TextStyle(
                    color: Color(0xFF222222), fontSize: 12, height: 1)),
            Image(
              width: 12,
              height: 12,
              image: NetworkImage(
                  'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/c0caf619ad24d450/arrow_down.png'),
            ),
          ],
        ),
        radius: 2,
        onPressActionIcon: () {
          buildPhfDetails();
        });
  }

  // 汇总信息 - 结算金额 - 999,999
  summaryWidget() {
    if (billChargeDetailDynamic == null) {
      return SizedBox();
    }
    Widget title = FittedBox(
      child: Row(
        children: <Widget>[
          billChargeDetailDynamic.chargeAlias != null
              ? Text(
                  billChargeDetailDynamic.chargeAlias,
                  style: TextStyle(
                    color: Color(0xFF666666),
                    fontWeight: FontWeight.w400,
                    fontSize: 14,
                  ),
                )
              : Text(
                  billChargeDetailDynamic.dailyBillId == 0 ? '预计结算金额' : '结算金额',
                  // strutStyle: StrutStyle(
                  //   forceStrutHeight: true,
                  // ),
                  style: TextStyle(
                    color: Color(0xFF666666),
                    fontWeight: FontWeight.w400,
                    fontSize: 14,
                  ),
                ),
          billChargeDetailDynamic.other != null
              ? Container(
                  margin: EdgeInsets.only(top: UITools.isPc ? 2 : 0),
                  child: Row(
                    children: <Widget>[
                      SizedBox(width: 5),
                      ToolTipNew(
                        target: Image.network(
                          'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/16c344dc69d4f36d61ad2458443d508b/help_outline.png',
                          width: 14,
                          height: 14,
                        ),
                        tip: billChargeDetailDynamic.other ?? '',
                        width: 14,
                        height: 14,
                        onClick: () {
                          Modal.showModalDialog(
                            context,
                            title: '结算金额计算公式',
                            child: Text(
                              billChargeDetailDynamic.other ?? '',
                              style: TextStyle(
                                height: 1.5,
                                color: Color(0xFF666666),
                                fontWeight: FontWeight.w500,
                                fontSize: 14,
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                )
              : SizedBox(),
        ],
      ),
    );
    Widget money = moneyWidget(
        billChargeDetailDynamic.amount ?? 0, billChargeDetailDynamic.isPhf);
    String chargeName = billChargeDetailDynamic.chargeName ?? '';
    Widget type = chargeName != ''
        ? Text(
            '#${billChargeDetailDynamic.wmOrderDaySeq ?? ''} ${billChargeDetailDynamic.chargeName ?? ''}',
            style: TextStyle(
              color: Color(0xFF222222),
              fontSize: 14,
            ),
          )
        : SizedBox();
    String expStr = billChargeDetailDynamic?.settleNegativeTypeName ?? '';
    // 钱为负时的解释文案
    Widget negExp = Column(
      children: <Widget>[
        SizedBox(height: 10),
        Text(
          '$expStr',
          style: TextStyle(
            color: Color(0xFF999999),
            fontSize: 14,
          ),
        )
      ],
    );
    Widget processMoney = Column(
      children: <Widget>[
        SizedBox(height: 10),
        Text(
          '订单预计在送达后4-8h内完成入账',
          style: TextStyle(
            color: Color(0xFFFF6A00),
            fontSize: 14,
          ),
        )
      ],
    );
    return Stack(
      children: <Widget>[
        Container(
          padding: EdgeInsets.all(24),
          color: Color(0xFFFFFFFF),
          child: Container(
            child: Center(
              child: Column(
                children: <Widget>[
                  title,
                  SizedBox(height: 9),
                  money,
                  billChargeDetailDynamic.dailyBillId == 0
                      ? processMoney
                      : SizedBox.shrink(),
                  expStr != '' ? negExp : SizedBox(),
                  SizedBox(height: 5),
                  type
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  // 渲染：商品总价 / 活动支出 / 服务费 等
  renderMiddleArea(List<DataList> dataList) {
    List<Widget> list = [];
    bool isPHF = billChargeDetailDynamic?.isPhf ?? false;
    dataList?.forEach((e) {
      list.add(Container(
        margin: EdgeInsets.only(top: 8),
        padding: EdgeInsets.only(
          top: 16,
          bottom: 16,
          left: 16,
          right: 16,
        ),
        color: Color(0xFFFFFFFF),
        child: Column(
          children: <Widget>[
            OrderLevel1(
                title: e.title ?? '',
                totalAmount: e.totalAmount ?? '',
                version: e.version,
                isPhf: isPHF,
                tags: e?.tags,
                remark: e.remark,
                isShowDetail: e.isShowDetail,
                couponDetail: e.couponDetail,
                originTotalAmount: e.originTotalAmount ?? ''),
            OrderLevel2(
                parentName: e.title ?? '',
                subDataList: e?.subDataList,
                chargeModel: billChargeDetailDynamic.chargeModel,
                version: e.version,
                isPhf: isPHF,
                subRelatedInfo: e?.subRelatedInfo,
                keys: widget?.params['platform'] == 'xf'
                    ? '1'
                    : widget?.params['keys'],
                platform: widget?.params['platform'] == 'xf'
                    ? widget?.params['platform']
                    : null,
                wmPoiId: widget?.params['wmPoiId'] != null
                    ? widget?.params['wmPoiId']
                    : null),
          ],
        ),
      ));
    });
    return Column(
      children: list,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (billChargeDetailDynamic?.phfData != null) buildPhfTips(),
        Expanded(
            child: ListView.builder(
          itemCount: 1,
          itemBuilder: (context, idx) => Container(
            color: Color(0xFFF5F6FA),
            // padding: EdgeInsets.all(12),
            child: Column(
              children: <Widget>[
                summaryWidget(),
                // 最重要的变化区域
                renderMiddleArea(billChargeDetailDynamic?.dataList),
                SizedBox(height: 10),
                // 订单信息
                OrderInfo(
                  billChargeDetailDynamic: billChargeDetailDynamic,
                ),
              ],
            ),
          ),
        ))
      ],
    );
  }
}
