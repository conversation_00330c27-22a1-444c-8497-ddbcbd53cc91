import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/modal.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/question.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/order/billChargeDetailDynamic.dart';

class TagsContent extends StatelessWidget {
  TagsContent(
      {@required this.type,
      @required this.tags,
      @required this.keys,
      @required this.isShowDetail,
      @required this.couponDetail});
  final String type;
  final List<Tags> tags;
  final String keys;
  final int isShowDetail;
  final CouponDetail couponDetail;

// 神抢手优惠劵/佣金减免优惠劵标识/费率封顶标识 --- 弹窗内容
  _buildCouponDetailDialog(BuildContext context) {
    return Modal.showModalDialog(
      context,
      title: '${couponDetail.couponName}',
      child: Text(
        couponDetail.couponDesc ?? '',
        textAlign: TextAlign.center,
      ),
    );
  }

// 神抢手优惠劵/佣金减免优惠劵标识/费率封顶标识 --- 标识中小问号
  _buildTagsContentQuestion(BuildContext context) {
    return Question(
      isDark: true,
      onTap: () {
        _buildCouponDetailDialog(context);
      },
    );
  }

  // 神抢手优惠劵/佣金减免优惠劵标识/费率封顶标识 --- 内容
  _buildTagsContent(String type, String icon, String text, String color,
      String bgColor, BuildContext context, double width, double height) {
    if (icon != null) {
      return Container(
        // 神枪手图标使用默认 50*13 大小，神枪手一口价图标使用接口传递大小（应为 90*20）
        width: width == 0 ? 50 : width,
        height: height == 0 ? 13 : height,
        margin: EdgeInsets.only(right: 6),
        child: Image.network(
          icon,
        ),
      );
    }
    return Container(
        margin: type == 'full'
            ? EdgeInsets.only(left: 5, bottom: keys == 'orderLevel2' ? 3 : 0)
            : EdgeInsets.only(right: 5, bottom: keys == 'orderLevel2' ? 3 : 0),
        padding: EdgeInsets.symmetric(horizontal: 5, vertical: 2),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(3),
            color:
                type == 'full' ? Color(int.parse(bgColor)) : Color(0x00FF7700),
            border: type == 'empty'
                ? Border.all(
                    width: 0.5,
                    color: Color(0xFFFF7700),
                  )
                : null),
        child: Row(children: <Widget>[
          Text(
            text,
            style: TextStyle(
              fontSize: 12,
              color:
                  type == 'full' ? Color(int.parse(color)) : Color(0xFFFF7700),
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(width: isShowDetail == 2 && type == 'full' ? 2 : 0),
          isShowDetail == 2 && type == 'full'
              ? _buildTagsContentQuestion(context)
              : SizedBox.shrink()
        ]));
  }

  // 神抢手优惠劵/佣金减免优惠劵标识/费率封顶标识
  _buildFrontTagsContent(String type, List<Tags> tags, BuildContext context) {
    if (tags != null && tags.length > 0) {
      List<Widget> resArr = [];
      tags.forEach((element) {
        if (element.position == 'front' && type == 'front') {
          resArr.add(_buildTagsContent(
              'empty',
              element.icon,
              element.name,
              element.color,
              element.bgColor,
              context,
              element.width,
              element.height));
        }
        if (element.position == 'back' && type == 'back') {
          resArr.add(_buildTagsContent(
              'full',
              element.icon,
              element.name,
              element.color,
              element.bgColor,
              context,
              element.width,
              element.height));
        }
      });
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: resArr,
      );
    } else {
      return SizedBox.shrink();
    }
  }

  @override
  Widget build(BuildContext context) {
    return _buildFrontTagsContent(type, tags, context);
  }
}
