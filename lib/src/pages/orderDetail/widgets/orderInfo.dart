import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:toast_widget_flutter/toast_widget_flutter.dart';
import 'package:waimai_e_fe_ffw_utils/platform_tool.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/order/billChargeDetailDynamic.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';

class OrderInfo extends StatelessWidget {
  OrderInfo({@required this.billChargeDetailDynamic});
  final BillChargeDetailDynamicModel billChargeDetailDynamic;

  copyBtn(String conent) {
    return GestureDetector(
      onTap: () {
        Clipboard.setData(ClipboardData(text: conent ?? ''))
            .then((value) => Loading.showToast(message: '复制成功'))
            .catchError(
          (Object error, StackTrace stack) {
            Loading.showToast(message: '复制失败');
          },
        );
      },
      child: PlatformTools.isPC
          ? SizedBox()
          : Container(
              margin: EdgeInsets.only(left: 8),
              child: MouseRegion(
                cursor: SystemMouseCursors.click,
                child: Text(
                  '复制',
                  strutStyle: StrutStyle(
                    forceStrutHeight: true,
                  ),
                  style: TextStyle(
                    color: Color(0xFFFF6A00),
                    fontWeight: FontWeight.w400,
                    fontSize: 14,
                  ),
                ),
              ),
            ),
    );
  }

  // 每一行数据
  orderOneRow(String label, String value, bool addCopy) {
    return Container(
      margin: EdgeInsets.only(bottom: 10),
      child: Flex(
        direction: Axis.horizontal,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          // 左边
          Container(
            child: Text(
              label,
              style: TextStyle(
                color: Color(0xFF666666),
                fontSize: 14,
              ),
            ),
          ),
          // 右边
          Expanded(
            flex: 1,
            child: Flex(
              direction: Axis.horizontal,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.end,
              children: <Widget>[
                Expanded(
                  child: Text(
                    value ?? '',
                    textAlign: TextAlign.end,
                    style: TextStyle(
                      color: Color(0xFF222222),
                      fontSize: 14,
                    ),
                  ),
                ),
                // 复制按钮
                addCopy ? copyBtn(value) : SizedBox()
              ],
            ),
          )
        ],
      ),
    );
  }

  isNotNull(String str) {
    if (str != null && str != '') {
      return true;
    }
    return false;
  }

  getOtherInfo() {
    int chargeTypeCode = billChargeDetailDynamic.chargeTypeCode;
    Map<String, String> map = Map();

    // 分单相关
    bool splitBillRelate = chargeTypeCode == 6192 || chargeTypeCode == 6193;
    // 退款相关
    bool refundRelate =
        chargeTypeCode == 2200 || chargeTypeCode == 2300 || splitBillRelate;

    /**
     * chargeTypeCode 解释
     * 外卖订单 2000
     * 餐损赔付 2100
     * 订单退款 2200
     * 订单部分退款 2300
     * 配送费用 2400
     * 配送费用退款 2500
     * 配送订单取消扣款 2600
     * 广告自动充值 6050
     * 临期药过期药罚款退款 6180
     * 分单退配送费(分单退配送费) 6192
     * 分单退配送费退款(分单退配送费退款）6193
     */
    if (billChargeDetailDynamic.orderType != 4) {
      if (chargeTypeCode == 2000) {
        int wmOrderCompletedDateTimestamp =
            billChargeDetailDynamic.wmOrderCompletedDateTimestamp;
        String wmOrderCompletedDate =
            billChargeDetailDynamic.wmOrderCompletedDate;
        if (wmOrderCompletedDateTimestamp != null &&
            wmOrderCompletedDateTimestamp > 0) {
          wmOrderCompletedDate = DateFormat.formatFull(
              billChargeDetailDynamic.wmOrderCompletedDateTimestamp);
        }
        if (isNotNull(wmOrderCompletedDate)) {
          map.putIfAbsent('订单完成时间', () => wmOrderCompletedDate);
        }
        String wmOrderShippingType =
            billChargeDetailDynamic.wmOrderShippingType;
        if (isNotNull(wmOrderShippingType)) {
          map.putIfAbsent('配送方式', () => wmOrderShippingType);
        }
      } else if (refundRelate) {
        int wmOrderRefundedDateTimestamp =
            billChargeDetailDynamic.wmOrderRefundedDateTimestamp;
        String wmOrderRefundedDate =
            billChargeDetailDynamic.wmOrderRefundedDate;
        if (wmOrderRefundedDateTimestamp != null &&
            wmOrderRefundedDateTimestamp > 0) {
          wmOrderRefundedDate =
              DateFormat.formatFull(wmOrderRefundedDateTimestamp);
        }
        if (isNotNull(wmOrderRefundedDate)) {
          map.putIfAbsent('退款时间', () => wmOrderRefundedDate);
        }
        String wmOrderShippingType =
            billChargeDetailDynamic.wmOrderShippingType;
        if (isNotNull(wmOrderShippingType)) {
          map.putIfAbsent('配送方式', () => wmOrderShippingType);
        }
      } else if (chargeTypeCode == 2100) {
        int wmOrderCanceledDateTimestamp =
            billChargeDetailDynamic.wmOrderCanceledDateTimestamp;
        String wmOrderCanceledDate =
            billChargeDetailDynamic.wmOrderCanceledDate;
        if (wmOrderCanceledDateTimestamp != null &&
            wmOrderCanceledDateTimestamp > 0) {
          wmOrderCanceledDate =
              DateFormat.formatFull(wmOrderCanceledDateTimestamp);
        }
        if (isNotNull(wmOrderCanceledDate)) {
          map.putIfAbsent('订单取消时间', () => wmOrderCanceledDate);
        }

        int wmOrderCompensatedDateTimestamp =
            billChargeDetailDynamic.wmOrderCompensatedDateTimestamp;
        String wmOrderCompensatedDate =
            billChargeDetailDynamic.wmOrderCompensatedDate;
        if (wmOrderCompensatedDateTimestamp != null &&
            wmOrderCompensatedDateTimestamp > 0) {
          wmOrderCompensatedDate =
              DateFormat.formatFull(wmOrderCompensatedDateTimestamp);
        }
        if (isNotNull(wmOrderCompensatedDate)) {
          map.putIfAbsent('餐损赔付时间', () => wmOrderCompensatedDate);
        }
        String wmOrderShippingType =
            billChargeDetailDynamic.wmOrderShippingType;
        if (isNotNull(wmOrderShippingType)) {
          map.putIfAbsent('配送方式', () => wmOrderShippingType);
        }
      } else if (chargeTypeCode == 2400) {
        int wmOrderCompletedDateTimestamp =
            billChargeDetailDynamic.wmOrderCompletedDateTimestamp;
        String wmOrderCompletedDate =
            billChargeDetailDynamic.wmOrderCompletedDate;
        if (wmOrderCompletedDateTimestamp != null &&
            wmOrderCompletedDateTimestamp > 0) {
          wmOrderCompletedDate =
              DateFormat.formatFull(wmOrderCompletedDateTimestamp);
        }
        if (isNotNull(wmOrderCompletedDate)) {
          map.putIfAbsent('订单完成时间', () => wmOrderCompletedDate);
        }
      } else if (chargeTypeCode == 2500 || chargeTypeCode == 2600) {
        int wmOrderRefundedDateTimestamp =
            billChargeDetailDynamic.wmOrderRefundedDateTimestamp;
        String wmOrderRefundedDate =
            billChargeDetailDynamic.wmOrderRefundedDate;
        if (wmOrderRefundedDateTimestamp != null &&
            wmOrderRefundedDateTimestamp > 0) {
          wmOrderRefundedDate =
              DateFormat.formatFull(wmOrderRefundedDateTimestamp);
        }
        if (isNotNull(wmOrderRefundedDate)) {
          map.putIfAbsent('退款时间', () => wmOrderRefundedDate);
        }
      } else if (chargeTypeCode == 6050) {
        map.putIfAbsent('说明', () => '您开通了推广费自动充值功能，此扣款会如数充值到当前门店的推广账户中');
      }

      // 关联拼好饭拼单一起送订单
      String associateOrderSeq = billChargeDetailDynamic.associateOrderSeq;
      int chargeModel = billChargeDetailDynamic.chargeModel;
      if (isNotNull(associateOrderSeq) && chargeModel == 102) {
        map.putIfAbsent('关联拼单一起送订单', () => associateOrderSeq);
      }

      if (splitBillRelate) {
        String comment = billChargeDetailDynamic.comment;
        if (isNotNull(comment)) {
          map.putIfAbsent('原因', () => comment);
        }
      }
    }
    return map;
  }

  // 订单信息
  orderInfoWidget() {
    Widget title = Container(
      margin: EdgeInsets.fromLTRB(0, 0, 0, 15),
      child: Text(
        '订单信息',
        style: TextStyle(
          color: Color(0xFF222222),
          fontWeight: FontWeight.w600,
          fontSize: 16,
        ),
      ),
    );
    Widget orderSeq = billChargeDetailDynamic.orderType == 4
        ? Container()
        : orderOneRow(
            '订单编号', '#${billChargeDetailDynamic.wmOrderDaySeq ?? ''}', false);
    Widget orderNum = billChargeDetailDynamic.orderType == 4
        ? Container()
        : orderOneRow('订单号', billChargeDetailDynamic.wmOrderViewId, true);
    String wmOrderSubmittedDate = billChargeDetailDynamic.orderType == 4
        ? billChargeDetailDynamic.ctime
        : billChargeDetailDynamic.wmOrderSubmittedDate;
    int wmOrderSubmittedDateTimestamp = billChargeDetailDynamic.orderType == 4
        ? billChargeDetailDynamic.ctimestamp
        : billChargeDetailDynamic.wmOrderSubmittedDateTimestamp;
    if (wmOrderSubmittedDateTimestamp != null &&
        wmOrderSubmittedDateTimestamp > 0) {
      wmOrderSubmittedDate = DateFormat.formatFull(
        billChargeDetailDynamic.orderType == 4
            ? billChargeDetailDynamic.ctimestamp
            : billChargeDetailDynamic.wmOrderSubmittedDateTimestamp,
      );
    }

    Widget wmOrderSubmittedDateWidget = SizedBox();
    if (isNotNull(wmOrderSubmittedDate)) {
      wmOrderSubmittedDateWidget =
          orderOneRow('下单时间', wmOrderSubmittedDate, false);
    }
    List<Widget> list = [];
    list.add(orderSeq);
    list.add(orderNum);
    list.add(wmOrderSubmittedDateWidget);
    // 遍历其他类型的订单信息
    Map<String, String> map = getOtherInfo();
    map.forEach((key, value) {
      list.add(orderOneRow(key, value, false));
    });
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Container(
          padding: EdgeInsets.all(16),
          color: Color(0xFFFFFFFF),
          child: Container(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                title,
                Container(
                  padding: EdgeInsets.fromLTRB(12, 10, 12, 10),
                  child: Column(children: list),
                  decoration: BoxDecoration(
                    color: Color(0xFFF5F6FA),
                    borderRadius: BorderRadius.circular(6.5),
                  ),
                )
              ],
            ),
          ),
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return billChargeDetailDynamic != null
        ? Column(
            children: <Widget>[orderInfoWidget()],
          )
        : SizedBox();
  }
}
