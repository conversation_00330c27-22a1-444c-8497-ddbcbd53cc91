import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/order/billChargeDetailDynamic.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';

import 'components/tagsContent.dart';

// 数据结构的第一层，样式为 title + 钱数
class OrderLevel1 extends StatelessWidget {
  OrderLevel1({
    @required this.title,
    @required this.totalAmount,
    @required this.version,
    @required this.tags,
    @required this.remark,
    @required this.isShowDetail,
    @required this.couponDetail,
    @required this.originTotalAmount,
    this.isPhf = false,
  });
  final String title;
  final String totalAmount;
  final int version; //后端控制灰度用，0表示旧结构，1表示新结构
  final bool isPhf;
  final List<Tags> tags;
  final String remark;
  final int isShowDetail;
  final CouponDetail couponDetail;
  final String originTotalAmount;

  /// 拥挤按最低商业服务费收取标签
  bottomTags() {
    if (tags != null && tags.length > 0) {
      List<Widget> resArr = [];
      tags.forEach((e) => {
            if (e.position == 'amount_bottom')
              {
                resArr.add(Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(),
                    Container(
                        width: 114,
                        child: Column(
                          children: [
                            Container(
                              width: 14,
                              height: 0,
                              margin: EdgeInsets.all(0),
                              decoration: BoxDecoration(
                                border: Border(
                                  bottom: BorderSide(
                                      color: Color(0xFF000000),
                                      width: 6,
                                      style: BorderStyle.solid),
                                  right: BorderSide(
                                      color: Colors.transparent,
                                      width: 6,
                                      style: BorderStyle.solid),
                                  left: BorderSide(
                                      color: Colors.transparent,
                                      width: 6,
                                      style: BorderStyle.solid),
                                ),
                              ),
                            ),
                            Container(
                              padding: EdgeInsets.only(
                                left: 2,
                                top: 2,
                                bottom: 2,
                              ),
                              decoration: BoxDecoration(
                                  color: Color(0xFFFFFFFF),
                                  borderRadius: BorderRadius.circular(2),
                                  border: Border.all(
                                    width: 1,
                                    color: Color(0xFF999999),
                                  )),
                              child: Text(
                                e.name ?? '',
                                style: TextStyle(fontSize: 11),
                              ),
                            )
                          ],
                        )),
                  ],
                ))
              }
          });
      return Column(children: resArr);
    } else {
      return SizedBox.shrink();
    }
  }

  @override
  Widget build(BuildContext context) {
    int money = MoneyTool.formatStringToInt(totalAmount);
    Color negativeMoneyColor =
        isPhf == true ? Color(0xFF222222) : Color(0xFFFF192D);
    return version == 1
        ? Container()
        : Visibility(
            visible: title != null &&
                title.isNotEmpty &&
                totalAmount != null &&
                totalAmount.isNotEmpty,
            child: Container(
                child: Column(
              children: [
                Flex(
                    direction: Axis.horizontal,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Expanded(
                          child: Wrap(
                              crossAxisAlignment: WrapCrossAlignment.start,
                              children: <Widget>[
                            Text.rich(TextSpan(children: [
                              WidgetSpan(
                                  alignment: PlaceholderAlignment.middle,
                                  child: TagsContent(
                                      type: 'front',
                                      tags: tags,
                                      keys: 'orderLevel1',
                                      isShowDetail: isShowDetail,
                                      couponDetail: couponDetail)),
                              TextSpan(
                                text: title ?? '',
                                style: TextStyle(
                                    color: Color(0xFF222222),
                                    fontWeight: FontWeight.w600,
                                    fontSize: 16,
                                    height: 2),
                              ),
                              WidgetSpan(
                                  alignment: PlaceholderAlignment.middle,
                                  child: TagsContent(
                                      type: 'back',
                                      tags: tags,
                                      keys: 'orderLevel1',
                                      isShowDetail: isShowDetail,
                                      couponDetail: couponDetail)),
                            ]))
                          ])),
                      //       // 钱数
                      DefaultTextStyle(
                          style: TextStyle(
                              color: version == 10001
                                  ? Color(0xFF222222)
                                  : (money > 0
                                      ? Color(0xFF222222)
                                      : negativeMoneyColor),
                              fontWeight: FontWeight.w700,
                              height: 2),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: <Widget>[
                              Text(
                                originTotalAmount ?? '',
                                strutStyle: StrutStyle(
                                    forceStrutHeight: true, height: 2),
                                style: TextStyle(
                                    color: Color(0xFF999999),
                                    fontWeight: FontWeight.w500,
                                    fontSize: 15,
                                    decoration: TextDecoration.lineThrough,
                                    height: 2),
                              ),
                              SizedBox(
                                width: 5,
                              ),
                              Text(
                                totalAmount,
                                strutStyle: StrutStyle(
                                  forceStrutHeight: true,
                                ),
                                style: TextStyle(fontSize: 16, height: 2),
                              ),
                            ],
                          )),
                    ]),
                // Row(
                //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //   children: [
                //     Container(),
                //     Container(
                //       width: 114,
                //       child: Tag(text: bottomTags()),
                //     ),
                //   ],
                // ),
                bottomTags(),
                SizedBox(height: remark != null ? 4 : 0),
                Visibility(
                    visible: remark != null && remark.isNotEmpty,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          remark ?? '',
                          style: TextStyle(
                            color: Color(0xFF999999),
                            fontWeight: FontWeight.w400,
                            fontSize: 12,
                          ),
                        )
                      ],
                    )),
                // SizedBox(height: remark != null ? 14 : 0),
              ],
            )),
          );
  }
}
