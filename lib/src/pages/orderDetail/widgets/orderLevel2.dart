import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/arrow.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/billChargetListJumpers.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/modal.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/question.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/showMore.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/orderDetail/widgets/orderLevel3.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/order/billChargeDetailDynamic.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/platformTool.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/uiTools.dart';

import 'components/tagsContent.dart';

// 主要为明细项目，左侧为文案以下注释，右侧为金额，其中商品的信息，会默认收起
class OrderLevel2 extends StatefulWidget {
  OrderLevel2(
      {@required this.parentName,
      @required this.subDataList,
      @required this.chargeModel,
      @required this.version,
      @required this.keys,
      @required this.subRelatedInfo,
      this.isPhf = false,
      this.platform,
      this.wmPoiId});
  final List<SubDataList> subDataList;
  // 商品总价 / 活动支出 / 履约服务费等
  final String parentName;

  final int chargeModel;

  final int version; //后端控制灰度用，0表示旧结构，1表示新结构

  final bool isPhf;

  final String keys;
  final SubRelatedInfo subRelatedInfo;
  final String platform;
  final String wmPoiId;

  @override
  OrderLevel2State createState() => OrderLevel2State();
}

class OrderLevel2State extends State<OrderLevel2> {
  // 是否为商品总价
  bool isGoods = false;

  // 是否展示更多，默认商品收起
  bool showMore = false;
  // 是否有父标题
  bool isTitleHiden = true;
  final TapGestureRecognizer recognizer = TapGestureRecognizer();
  @override
  void initState() {
    super.initState();
    isGoods = widget.parentName.indexOf('商品总价', 0) > -1;
    isTitleHiden = widget.version == 1;
  }

  // 在弹窗中 基础收费/季节收费/时段收费 等文案
  _buildAgreeAllowance(List<AgreeAllowanceDetail> agreeAllowanceDetailList) {
    _buildItem(AgreeAllowanceDetail agreeAllowanceDetail, bool isLast) {
      String comment = agreeAllowanceDetail.comment;
      String title = agreeAllowanceDetail.title;
      String amount = agreeAllowanceDetail.amount;

      print('comment $comment title $title amount $amount');

      return Container(
        padding: EdgeInsets.only(bottom: isLast ? 0 : 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '$title',
                  style: TextStyle(fontSize: 12, color: Color(0xFF222426)),
                ),
                Text(
                  '$amount',
                  style: TextStyle(fontSize: 12, color: Color(0xFF222426)),
                ),
              ],
            ),
            comment != null && comment != ''
                ? Text(
                    '$comment',
                    style: TextStyle(fontSize: 12, color: Color(0xFF999999)),
                  )
                : SizedBox(),
          ],
        ),
      );
    }

    // 先处理空的情况
    agreeAllowanceDetailList = agreeAllowanceDetailList ?? [];
    int count = agreeAllowanceDetailList.length;
    List<Widget> list = [];
    agreeAllowanceDetailList
        .asMap()
        .forEach((int index, AgreeAllowanceDetail item) {
      bool isLast = count == 0 ? true : index == count - 1;
      list.add(_buildItem(item, isLast));
    });
    return count > 0
        ? Container(
            margin: EdgeInsets.fromLTRB(0, 8, 0, 8),
            padding: EdgeInsets.fromLTRB(12, 10, 12, 10),
            decoration: BoxDecoration(
              color: Color(0xFFF5F6FA),
              borderRadius: BorderRadius.circular(6.5),
            ),
            child: Column(children: list),
          )
        : SizedBox();
  }

  // 点击问号的弹窗
  _buildDialog(List<CommissionDataList> dataList) {
    List<Widget> list = [];
    dataList?.forEach((CommissionDataList data) {
      list.add(Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                data.title ?? '',
                style: TextStyle(
                  color: Color(0xFF222222),
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              ),
              Text(
                data.amount ?? '',
                style: TextStyle(
                  color: Color(0xFF222222),
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          Text(
            data.info ?? '',
            style: TextStyle(
              color: Color(0xFF999999),
              fontWeight: FontWeight.w400,
              fontSize: 12,
            ),
          ),
          // 基础收费等项目
          _buildAgreeAllowance(data.agreeAllowanceDetailList),
        ],
      ));
    });
    return Column(children: list);
  }

  // 点击问号的弹窗
  _buildCouponDetailDialog(CouponDetail couponDetail) {
    return Modal.showModalDialog(
      context,
      title: '${couponDetail.couponName}',
      child: (StringUtil.isNotEmpty(couponDetail.appUrl) && !UITools.isPc)
          ? Text.rich(TextSpan(children: [
              TextSpan(text: couponDetail.couponDesc),
              TextSpan(text: '如需调整活动可点击'),
              TextSpan(
                text: '去设置>',
                style: TextStyle(color: Color(0xFFFFCC33)),
                recognizer: recognizer
                  ..onTap = () {
                    print(couponDetail.appUrl);
                    RouterTools.openWebPageUrl(couponDetail.appUrl);
                  },
              )
            ]))
          : Text(
              couponDetail.couponDesc,
              textAlign: TextAlign.center,
            ),
    );
  }

  getDialogTitle(String title) {
    if (title == '配送服务费收费（均摊后）') {
      title = '配送服务费收费均摊规则';
    } else if (title == '配送服务费补贴（均摊后）') {
      title = '配送服务费补贴均摊规则';
    }
    return title;
  }

  // level2中可点击的小问号
  _buildQuestionIcon(SubDataList subData) {
    Widget icon = SizedBox.shrink();
    // 1 - 展示问号，弹窗内容为commissionDataList
    if (subData.isShowDetail == 1) {
      icon = Question(
        isDark: true,
        onTap: () {
          // tipModalContent(context, commission);
          Modal.showModalDialog(context,
              title: getDialogTitle(subData.name),
              child: _buildDialog(subData.commissionDataList));
        },
      );
    } else if (subData.isShowDetail == 2) {
      // 2/4 - 展示问号，弹窗内容为couponDetail
      icon = Question(
        isDark: true,
        onTap: () {
          _buildCouponDetailDialog(subData.couponDetail);
        },
      );
    } else if (subData.isShowDetail == 5 && !UITools.isPc) {
      icon = Question(
        isDark: true,
        onTap: () {
          RouterTools.openWebPageUrl(subData.couponDetail.appUrl);
        },
      );
    }
    return icon;
  }

  _buildRemarkQuestionIcon(SubDataList subData) {
    Widget icon = SizedBox.shrink();
    if (subData.isShowDetail == 4) {
      // 4 - 展示问号，弹窗内容为couponDetail
      icon = Question(
        isDark: true,
        onTap: () {
          _buildCouponDetailDialog(subData.couponDetail);
        },
      );
    }
    return icon;
  }

  _buildRemark(String remark, SubDataList subDataList) {
    return remark?.isNotEmpty == true
        ? Container(
            child: Wrap(
            children: [
              Text.rich(TextSpan(children: [
                TextSpan(
                  text: remark ?? '',
                  style: TextStyle(
                      color: Color(0xFF999999),
                      fontWeight: FontWeight.w400,
                      fontSize: isTitleHiden ? 13 : 12,
                      height: 2),
                ),
                WidgetSpan(
                    alignment: PlaceholderAlignment.middle,
                    child: _buildRemarkQuestionIcon(subDataList))
              ]))
            ],
          ))
        : SizedBox();
  }

  /// 神抢手sdk 背景色
  cartColor(List<Tags> tags) {
    String newRowBgColor;
    tags.forEach((element) {
      if (element.rowBgColor != null) {
        newRowBgColor = element.rowBgColor;
      }
    });
    if (widget.version == 0) {
      return Color(newRowBgColor != null && newRowBgColor != ''
          ? int.parse(newRowBgColor)
          : 0xFFF5F6FA);
    }
  }

  // 每一行数据
  _buildRowWidget(
      String name,
      String remark,
      String info,
      String originalInfo,
      int count,
      bool isLast,
      bool isFirst,
      SubDataList subDataList,
      List<Tags> tags) {
    count = count ?? -1;
    Color negativeMoneyColor =
        widget.isPhf == true ? Color(0xFF222222) : Color(0xFFFF192D);
    return Container(
      margin: isGoods
          ? EdgeInsets.only(top: 0)
          : EdgeInsets.only(bottom: isLast ? 0 : 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Flex(
            direction: Axis.horizontal,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              // 左文字
              Expanded(
                child: Wrap(
                  crossAxisAlignment: WrapCrossAlignment.start,
                  children: <Widget>[
                    // 标准文案
                    Text.rich(TextSpan(children: [
                      WidgetSpan(
                          alignment: PlaceholderAlignment.middle,
                          child: TagsContent(
                              type: 'front',
                              tags: tags,
                              keys: 'orderLevel2',
                              isShowDetail: subDataList.isShowDetail,
                              couponDetail: subDataList.couponDetail)),
                      TextSpan(
                        text: name ?? '',
                        style: TextStyle(
                            color: Color(0xFF222222),
                            fontWeight: isTitleHiden
                                ? FontWeight.w500
                                : FontWeight.w400,
                            fontSize: isTitleHiden ? 16 : 14,
                            height: 2),
                      ),
                      WidgetSpan(
                          alignment: PlaceholderAlignment.middle,
                          child: Container(
                            child: _buildQuestionIcon(subDataList),
                          )),
                      WidgetSpan(
                          alignment: PlaceholderAlignment.middle,
                          child: TagsContent(
                              type: 'back',
                              tags: tags,
                              keys: 'orderLevel2',
                              isShowDetail: subDataList.isShowDetail,
                              couponDetail: subDataList.couponDetail)),
                    ])),
                  ],
                ),
              ),
              SizedBox(width: 10),
              // 中间为商品数量
              count > -1
                  ? Container(
                      width: 40,
                      child: Text(
                        'x$count',
                        strutStyle: PlatformTool.isWeb
                            ? null
                            : StrutStyle(
                                forceStrutHeight: true,
                                height: 2,
                              ),
                        style: TextStyle(
                            color: Color(0xFF666666), fontSize: 14, height: 2),
                      ),
                    )
                  : SizedBox(),
              // 右钱数
              ConstrainedBox(
                constraints: BoxConstraints(minWidth: 80),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: <Widget>[
                    Text(
                      originalInfo ?? '',
                      textAlign: TextAlign.end,
                      strutStyle: PlatformTool.isWeb
                          ? null
                          : StrutStyle(
                              forceStrutHeight: true,
                              height: 1,
                            ),
                      style: TextStyle(
                          color: Color(0xFF999999),
                          fontWeight: FontWeight.w500,
                          fontSize: isTitleHiden ? 15 : 14,
                          decoration: TextDecoration.lineThrough,
                          height: 2),
                    ),
                    Text(
                      info ?? '',
                      textAlign: TextAlign.end,
                      strutStyle: PlatformTool.isWeb
                          ? null
                          : StrutStyle(
                              forceStrutHeight: true,
                              height: 2,
                            ),
                      style: TextStyle(
                        color: isTitleHiden
                            ? negativeMoneyColor
                            : Color(0xFF222222),
                        fontWeight: FontWeight.w500,
                        fontSize: 16,
                        height: 2,
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
          _buildRemark(remark, subDataList),
          // 折后描述
          _buildRemark(subDataList?.originalRemark ?? '', subDataList),
        ],
      ),
    );
  }

  subTag(List<Tags> tags) {
    List<Widget> tagList = [];
    if (tags != null && tags.length > 0) {
      tags.forEach((element) {
        if (element.code == 102) {
          tagList.add(Image.network(
              'https://p0.meituan.net/ingee/028365895d9f61de6441f4e2d97f93fc5988.png'));
        }
      });
    }
    return tagList;
  }

  Widget _buildSubRelatedInfoRow() {
    final hasTags = subTag(widget.subRelatedInfo?.tags).isNotEmpty;
    final descText = Text(
      widget.subRelatedInfo?.desc ?? '',
      style: TextStyle(
        fontWeight: FontWeight.w500,
        color: Color(0xFF3D3D3D),
        fontSize: 12,
      ),
    );

    if (hasTags) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            width: 53,
            height: 16,
            child: Column(
              children: subTag(widget.subRelatedInfo?.tags),
            ),
          ),
          Row(
            children: [
              descText,
              SizedBox(width: 5),
              ArrowIcon(color: ArrowIconColorEnum.grey),
            ],
          )
        ],
      );
    }
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [descText, ArrowIcon(color: ArrowIconColorEnum.grey)],
    );
  }

  @override
  Widget build(BuildContext context) {
    if (widget.subDataList == null && widget.subRelatedInfo == null) {
      return SizedBox.shrink();
    }
    List<Widget> list = [];
    List<Widget> lists = [];

    if ((showMore || isGoods == false)) {
      widget?.subDataList?.asMap()?.forEach((index, SubDataList e) {
        e.name != null
            ? list.add(_buildRowWidget(
                e.name,
                e.remark,
                e.info,
                e.originalInfo,
                e.count,
                index == widget.subDataList.length - 1,
                index == 0,
                e,
                e.tags))
            : SizedBox.shrink();
        // 在level2中，只有 “配送服务费收费（均摊后）” 和 “配送服务费补贴（均摊后）” 会有问号和弹窗
        // if (e.name != '配送服务费补贴（均摊后）' &&
        //     e.name != '配送服务费收费（均摊后）' &&
        //     e.isShowDetail == 0) {
        if (e.isShowDetail == 0) {
          list.add(OrderLevel3(
              commissionDataList: e.commissionDataList,
              chargeModel: widget.chargeModel ?? 0,
              name: e.name,
              version: widget.version));
        }
      });

      /// 订单类：2000
      /// 佣金类：9900,6205
      bool isType = widget.subRelatedInfo?.chargeTypeCode != 2000 &&
          widget.subRelatedInfo?.chargeTypeCode != 9900 &&
          widget.subRelatedInfo?.chargeTypeCode != 6205;
      if (widget.subRelatedInfo != null && (widget.keys == '1' || isType)) {
        lists.add(BillChargeListJumpers(
          subRelatedInfo: widget.subRelatedInfo,
          platform: widget?.platform != null ? 'xfs' : null,
          wmPoiId: widget?.wmPoiId != null ? widget?.wmPoiId : null,
          child: Container(
              height: 38,
              padding: EdgeInsets.only(left: 12, right: 12),
              decoration: BoxDecoration(
                color: Color(0xFFFEE9EE),
                borderRadius: BorderRadius.circular(6.5),
              ),
              child: _buildSubRelatedInfoRow()),
        ));
      }
    }
    return Container(
      child: Column(
        children: <Widget>[
          // 数据部分
          Container(
            padding: widget.version == 0 && list.length > 0
                ? EdgeInsets.fromLTRB(12, 10, 12, 10)
                : EdgeInsets.fromLTRB(0, 0, 0, 0),
            margin: EdgeInsets.only(
                top: widget.version == 0 &&
                        list.length > 0 &&
                        widget.parentName != null &&
                        widget.parentName?.isNotEmpty == true
                    ? 16
                    : 0),
            child: Column(children: list),
            decoration: BoxDecoration(
              color:
                  widget.version == 0 ? Color(0xFFF5F6FA) : Color(0xFFFFFFFF),
              borderRadius: BorderRadius.circular(6.5),
            ),
          ),
          Wrap(
            children: lists,
          ),
          // 展示更多
          isGoods
              ? ShowMoreWidget(
                  fold: !showMore,
                  isFromOrderDetail: true,
                  cb: () {
                    setState(() {
                      showMore = !showMore;
                    });
                  },
                )
              : SizedBox()
        ],
      ),
    );
  }
}
