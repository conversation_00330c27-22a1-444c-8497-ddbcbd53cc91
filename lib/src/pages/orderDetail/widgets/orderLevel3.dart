import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/modal.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/question.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/order/billChargeDetailDynamic.dart';

import 'components/tagsContent.dart';

class OrderLevel3 extends StatelessWidget {
  OrderLevel3(
      {@required this.commissionDataList,
      @required this.chargeModel,
      @required this.name,
      @required this.version});
  final List<CommissionDataList> commissionDataList;
  final int chargeModel;
  final String name;
  final int version;

  // 距离收费计价规则
  _buildStepPriceDetailList(List<StepPriceDetailList> stpdList) {
    List<Widget> list = [];
    bool hasData = stpdList.length > 0;
    stpdList?.forEach((element) {
      Widget item = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          element.title != null
              ? Text(
                  element.title,
                  style: TextStyle(
                    color: Color(0xFF222426),
                    fontWeight: FontWeight.w400,
                    fontSize: 12,
                  ),
                )
              : SizedBox.shrink(),
          SizedBox(height: 3.5),
          element.comment != null
              ? Text(
                  element.comment,
                  style: TextStyle(
                    color: Color(0xFF999999),
                    fontWeight: FontWeight.w400,
                    fontSize: 12,
                  ),
                )
              : SizedBox.shrink(),
        ],
      );
      list.add(item);
      list.add(SizedBox(height: 10));
    });
    Widget content = Container(
      width: double.infinity,
      margin: EdgeInsets.only(top: 8),
      padding: EdgeInsets.fromLTRB(12, 10, 12, 10),
      decoration: BoxDecoration(
        color: Color(0xFFF5F6FA),
        borderRadius: BorderRadius.circular(6.5),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: list,
      ),
    );
    return hasData ? content : SizedBox.shrink();
  }

  // 包括价格收费和距离收费，展示字段基本一致，使用同一方法
  _buildPriceDialog(CommissionDataList commission) {
    List<Widget> list = [];
    // 起步价
    Widget startPrice = commission.startPrice != null
        ? Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Text(
                '起步价',
                style: TextStyle(
                  color: Color(0xFF222222),
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              ),
              Text(
                commission.startPrice ?? '',
                style: TextStyle(
                  color: Color(0xFF222222),
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              )
            ],
          )
        : SizedBox();
    // 距离阶梯加价
    Widget stepPrice = commission.stepPrice != null
        ? Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Text(
                '距离阶梯加价',
                style: TextStyle(
                  color: Color(0xFF222222),
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              ),
              Text(
                commission.stepPrice ?? '',
                style: TextStyle(
                  color: Color(0xFF222222),
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              )
            ],
          )
        : SizedBox();
    // startPriceComment : 20.0~30.0元区间每单收0.50元
    Widget startPriceCommentWidget = commission.startPriceComment != null
        ? Text(
            commission.startPriceComment,
            style: TextStyle(
              color: Color(0xFF999999),
              fontWeight: FontWeight.w400,
              fontSize: 12,
            ),
          )
        : SizedBox.shrink();
    Widget stepPriceCommentWidget = commission.stepPriceComment != null
        ? Text(
            commission.stepPriceComment,
            style: TextStyle(
              color: Color(0xFF999999),
              fontWeight: FontWeight.w400,
              fontSize: 12,
            ),
          )
        : SizedBox.shrink();
    // 加价明细
    Widget stepPriceDetail =
        _buildStepPriceDetailList(commission.stepPriceDetailList ?? []);
    // 特殊节假日
    list.add(startPrice);
    list.add(startPriceCommentWidget);
    list.add(stepPriceCommentWidget);
    list.add(SizedBox(height: 5));
    list.add(stepPrice);
    list.add(stepPriceDetail);
    return list;
  }

  _buildTwoTypeContent(String title, String amount) {
    return Container(
      margin: EdgeInsets.only(bottom: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Text(
            title ?? '',
            style: TextStyle(
              color: Color(0xFF222222),
              fontWeight: FontWeight.w500,
              fontSize: 14,
            ),
          ),
          Text(
            amount ?? '',
            style: TextStyle(
              color: Color(0xFF222222),
              fontWeight: FontWeight.w500,
              fontSize: 14,
            ),
          )
        ],
      ),
    );
  }

  getTitle(CommissionDataList commission) {
    String title = commission?.title ?? '';
    if (commission.title == '距离收费') {
      title = '距离收费计价规则';
    } else if (commission.title == '价格收费') {
      title = '价格收费计价规则';
    }
    return title;
  }

  _buildDialogChildren(CommissionDataList commission) {
    List<Widget> list = [];
    if (commission.title == '距离收费' ||
        commission.title == '价格收费' ||
        commission.title == '重量收费' ||
        commission.title == '品类收费' ||
        commission.title == '时段收费' ||
        commission.title == '特殊日期收费') {
      list = _buildPriceDialog(commission);
    } else if (commission.title == '封顶比例' || commission.title == '配送服务费补贴') {
      commission.stepPriceDetailList?.forEach((e) {
        Widget item = Container(
          margin: EdgeInsets.only(bottom: 10),
          child: Text(
            e.title ?? '',
            style: TextStyle(
              color: Color(0xFF222222),
              fontWeight: FontWeight.w500,
              fontSize: 14,
            ),
          ),
        );
        list.add(item);
      });
    } else if (commission.title == '技术服务费补贴' ||
        commission.title == '技术与运营服务费补贴') {
      // 技术与运营服务费补贴 是为了兼容拼好饭名称的修改兼容
      commission.techAllowanceDetailList?.forEach((e) {
        Widget item = _buildTwoTypeContent(e.title, e.amount);
        list.add(item);
      });
    } else if (commission.title == '履约服务费补贴') {
      commission.agreeAllowanceDetailList?.forEach((e) {
        Widget item = _buildTwoTypeContent(e.title, e.amount);
        list.add(item);
      });
    }
    return list;
  }

  // 特殊日期收费明细 - 同行显示标题和金额
  _buildSpDateDetailList(List<SpDateCommentList> spList) {
    List<Widget> list = [];
    bool hasData = spList.length > 0;
    spList?.forEach((element) {
      Widget item = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                width: 200, // 设置固定宽度
                child: Text(
                  element.title ?? '',
                  style: TextStyle(
                    color: Color(0xFF222426),
                    fontWeight: FontWeight.w400,
                    fontSize: 12,
                  ),
                  softWrap: true, // 启用软换行
                ),
              ),
              Text(
                element.amount ?? '',
                style: TextStyle(
                  color: Color(0xFF222426),
                  fontWeight: FontWeight.w400,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          SizedBox(height: 3.5),
          element.comment != null
              ? Text(
                  element.comment,
                  style: TextStyle(
                    color: Color(0xFF999999),
                    fontWeight: FontWeight.w400,
                    fontSize: 12,
                  ),
                )
              : SizedBox.shrink(),
        ],
      );
      list.add(item);
      list.add(SizedBox(height: 5));
    });

    Widget content = Container(
      width: double.infinity,
      margin: EdgeInsets.only(top: 8),
      padding: EdgeInsets.fromLTRB(12, 10, 12, 0),
      decoration: BoxDecoration(
        color: Color(0xFFF5F6FA),
        borderRadius: BorderRadius.circular(6.5),
      ),
      child: LayoutBuilder(
        builder: (context, constraints) {
          bool isScrollable = spList.length > 10;
          return isScrollable
              ? Container(
                  height: 200, // 设置固定高度
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: list,
                    ),
                  ),
                )
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: list,
                );
        },
      ),
    );
    return hasData ? content : SizedBox.shrink();
  }

  // 渲染 spDateDetailList，只展示 title 和 amount
  _buildSimpleSpDateDetailList(List<SpDateDetailList> spList) {
    if (spList == null || spList.isEmpty) {
      return SizedBox.shrink();
    }

    List<Widget> list = [];
    spList.forEach((element) {
      list.add(
        Container(
          padding: EdgeInsets.only(left: 12, right: 12),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                element.title ?? '',
                style: TextStyle(
                  color: Color(0xFF999999),
                  fontWeight: FontWeight.w400,
                  fontSize: 12,
                  height: 2,
                ),
              ),
              Text(
                element.amount ?? '',
                style: TextStyle(
                  color: Color(0xFF999999),
                  fontWeight: FontWeight.w400,
                  fontSize: 12,
                  height: 2,
                ),
              ),
            ],
          ),
        ),
      );
    });

    return Column(
      children: list,
    );
  }

  // 点小问号要弹出的内容
  tipModalContent(BuildContext context, CommissionDataList commission) {
    if (commission.isShowDetail == 4) {
      return Modal.showModalDialog(
        context,
        title: '${commission.couponDetail?.couponName ?? ''}',
        child: Text(
          commission.couponDetail?.couponDesc ?? '',
          textAlign: TextAlign.center,
        ),
      );
    }

    // 特殊日期收费的弹窗内容
    if (commission.title == '特殊日期收费') {
      return Modal.showModalDialog(
        context,
        title: '特殊日期计价规则',
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSpDateDetailList(commission.spDateCommentList ?? [])
          ],
        ),
      );
    }

    List<Widget> list = _buildDialogChildren(commission);
    Modal.showModalDialog(
      context,
      title: getTitle(commission),
      child:
          Column(crossAxisAlignment: CrossAxisAlignment.start, children: list),
    );
  }

  // 如果已知的弹窗中有内容，就展示出来
  showQuestionIcon(CommissionDataList commission) {
    // 控制问号展示逻辑
    return commission.isShowDetail == 1 ||
        commission.isShowDetail == 2 ||
        commission.isShowDetail == 6;
    //   List<Widget> list = _buildDialogChildren(commission);
    //   return list.length != 0;
  }

  // dataList-->subDataList-->commissionDataList
  renderCommissionDataDetail(
      BuildContext context, CommissionDataList commission) {
    Widget textIcon = Row(children: <Widget>[
      Transform.translate(
          offset: Offset(version == 0 ? -12 : 0, 0),
          child: Row(
            children: [
              TagsContent(
                  type: 'front',
                  tags: commission.tags,
                  keys: 'orderLevel1',
                  isShowDetail: commission.isShowDetail,
                  couponDetail: commission.couponDetail),
              Text(
                commission.title ?? '',
                strutStyle: StrutStyle(forceStrutHeight: true, height: 1.2),
                style: TextStyle(
                  color: Color(0xFF222222),
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                ),
              )
            ],
          )),
      SizedBox(width: 2),
      showQuestionIcon(commission)
          ? Question(
              isDark: true,
              onTap: () {
                tipModalContent(context, commission);
              },
            )
          : SizedBox(),
    ]);
    List<Widget> moneyList = [];
    String originalAmount = commission.originalAmount;
    if (originalAmount != null && originalAmount != '') {
      // 原金额
      Widget oriMoney = Text(
        commission.originalAmount ?? '',
        style: TextStyle(
          color: Color(0xFF858692),
          fontWeight: FontWeight.w400,
          fontSize: 12,
          decoration: TextDecoration.lineThrough,
        ),
      );
      moneyList.add(oriMoney);
      moneyList.add(SizedBox(
        width: 5,
      ));
    }
    Widget money = Text(
      commission.amount ?? '',
      style: TextStyle(
        color: Color(0xFF222222),
        fontWeight: FontWeight.w400,
        fontSize: 14,
      ),
    );
    moneyList.add(money);
    Widget moneyRow = Row(children: moneyList);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        // 第一行，距离收费 - 钱数
        Container(
          padding: EdgeInsets.only(left: 12, right: 12),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[textIcon, moneyRow],
          ),
        ),

        SizedBox(height: 3.5),
        // 第二行，注释
        commission.info != null
            ? Container(
                padding: EdgeInsets.only(left: 12, right: 12),
                child: Text.rich(TextSpan(children: [
                  TextSpan(
                    text: commission.info,
                    style: TextStyle(
                        color: Color(0xFF999999),
                        fontWeight: FontWeight.w400,
                        fontSize: 12,
                        height: 2),
                  ),
                  WidgetSpan(
                    alignment: PlaceholderAlignment.middle,
                    child: commission.isShowDetail == 4
                        ? Question(
                            isDark: true,
                            onTap: () {
                              tipModalContent(context, commission);
                            },
                          )
                        : SizedBox(),
                  )
                ])))
            : SizedBox(),
        // 特殊日期 spDateDetailList
        _buildSimpleSpDateDetailList(commission.spDateDetailList ?? []),
      ],
    );
  }

  /// 分割线
  linTags(List<Tags> tags) {
    if (tags != null && tags.length > 0) {
      List<Widget> resArr = [];
      tags.forEach((e) => {
            if (e.position == 'middle_line')
              {
                resArr.add(Transform.translate(
                    offset: Offset(0, -4),
                    child: Container(
                      margin: EdgeInsets.only(bottom: 16),
                      height: 0.5,
                      color: Color(0xFF999999),
                    )))
              }
          });
      return Column(children: resArr);
    } else {
      return SizedBox.shrink();
    }
  }

  // dataList-->subDataList-->commissionDataList
  // 距离收费/价格收费/时段收费
  renderCommissionDataDetailList(
      BuildContext context, List<CommissionDataList> commissionDataList) {
    if (commissionDataList == null) {
      return SizedBox();
    }
    List<Widget> list = [];
    commissionDataList = commissionDataList ?? [];
    commissionDataList.asMap().forEach((index, e) {
      list.add(renderCommissionDataDetail(context, e));
      list.add(linTags(e.tags));
      if (index != commissionDataList.length - 1) {
        list.add(SizedBox(height: 10));
      }
    });
    return Container(
      margin: EdgeInsets.only(top: version == 0 ? 0 : 10),
      padding: EdgeInsets.only(
          top: version == 0 ? 0 : 12, left: 0, right: 0, bottom: 12),
      decoration: BoxDecoration(
        color: Color(0xFFF5F6FA),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        children: list,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return renderCommissionDataDetailList(context, commissionDataList);
  }
}
