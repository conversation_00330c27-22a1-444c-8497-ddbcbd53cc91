import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';

class AmountSelect extends StatefulWidget {
  AmountSelect(
      {@required this.restAmount,
      @required this.selectAmount,
      @required this.selectionDownAmountController,
      @required this.selectionLowAmountController,
      @required this.tagLists});
  final Function restAmount;
  final Function selectAmount;
  final TextEditingController selectionDownAmountController;
  final TextEditingController selectionLowAmountController;
  final List tagLists;
  @override
  AmountSelectState createState() => AmountSelectState();
}

class AmountSelectState extends State<AmountSelect> with OrderListMixin {
  String minValue;
  String maxValue;
  String errorMsg;
  String proMsg;
  RegExp numberReg = RegExp('[1-9]');
  List tagList = [];
  List newTagList = [];

  @override
  void initState() {
    super.initState();

    if (widget.selectionLowAmountController.text != '' ||
        widget.selectionDownAmountController.text != '') {
      minValue = widget.selectionLowAmountController.text;
      maxValue = widget.selectionDownAmountController.text;
    }
    if (widget.tagLists != null && widget.tagLists.length > 0) {
      tagList = widget.tagLists.toSet().toList();
    }
  }

  /// 筛选金额 校验
  _isDis() {
    if (StringUtil.isEmpty(minValue) && StringUtil.isEmpty(maxValue)) {
      return false;
    }
    if (StringUtil.isNotEmpty(minValue) && StringUtil.isNotEmpty(maxValue)) {
      proMsg = '¥${minValue}-¥${maxValue}';
    } else {
      proMsg = '';
    }
    if (minValue == maxValue) {
      errorMsg = '最高金额不能等于最低金额，请重新输入';
      return true;
    } else {
      errorMsg = '';
    }
    return false;
  }

  /// 订单状态
  orderStatus() {
    ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_6beq7zby_mv');
    ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_6k7yfhl2_mv');
    ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_pcibfmri_mv');
    return Column(children: [
      Row(
        children: [
          Text('订单状态',
              style: TextStyle(
                  color: Color(0xFF666666),
                  fontWeight: FontWeight.w400,
                  fontFamily: 'PingFangSC-Regular',
                  fontSize: 14))
        ],
      ),
      SizedBox(
        height: 16,
      ),
      Row(children: [
        Expanded(
          child: RooSelect(
              label: "进行中",
              type: RooSelectType.plane,
              status: tagList.contains(201)
                  ? RooSelectStatus.active
                  : RooSelectStatus.normal,
              width: 78.0,
              height: 32.0,
              onPress: (bool seleced) {
                if (seleced) {
                  tagList.add(201);
                } else {
                  tagList.remove(201);
                }
              }),
        ),
        Expanded(
            child: RooSelect(
                label: "已取消",
                type: RooSelectType.plane,
                status: tagList.contains(203)
                    ? RooSelectStatus.active
                    : RooSelectStatus.normal,
                width: 78.0,
                height: 32.0,
                onPress: (bool seleced) {
                  if (seleced) {
                    tagList.add(203);
                  } else {
                    tagList.remove(203);
                  }
                })),
        Expanded(
            child: RooSelect(
                label: "部分退款",
                type: RooSelectType.plane,
                width: 78.0,
                height: 32.0,
                status: tagList.contains(202)
                    ? RooSelectStatus.active
                    : RooSelectStatus.normal,
                onPress: (bool seleced) {
                  if (seleced) {
                    tagList.add(202);
                  } else {
                    tagList.remove(202);
                  }
                })),
        Expanded(
            child: RooSelect(
                label: "已完单",
                type: RooSelectType.plane,
                width: 78.0,
                height: 32.0,
                status: tagList.contains(204)
                    ? RooSelectStatus.active
                    : RooSelectStatus.normal,
                onPress: (bool seleced) {
                  if (seleced) {
                    tagList.add(204);
                  } else {
                    tagList.remove(204);
                  }
                })),
      ])
    ]);
  }

  /// 售后状态
  afterSalesstatus() {
    ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_00it694z_mv');
    ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_2jbvqdb1_mv');
    ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_r36s6n1d_mv');
    ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_9gtzj28h_mv');
    return Column(
      children: [
        Row(
          children: [
            Text('售后状态',
                style: TextStyle(
                    color: Color(0xFF666666),
                    fontWeight: FontWeight.w400,
                    fontFamily: 'PingFangSC-Regular',
                    fontSize: 14))
          ],
        ),
        SizedBox(
          height: 16,
        ),
        Transform.translate(
            offset: Offset(-6, 0),
            child: Wrap(
              spacing: 13.0, // 水平间距
              runSpacing: 8.0,
              children: <Widget>[
                Container(
                    width: MediaQuery.of(context).size.width / 5,
                    child: RooSelect(
                        label: "餐损已赔",
                        type: RooSelectType.plane,
                        height: 32.0,
                        width: 78.0,
                        status: tagList.contains(301)
                            ? RooSelectStatus.active
                            : RooSelectStatus.normal,
                        onPress: (bool seleced) {
                          if (seleced) {
                            tagList.add(301);
                          } else {
                            tagList.remove(301);
                          }
                        })),
                Container(
                    width: MediaQuery.of(context).size.width / 5,
                    child: RooSelect(
                        label: "商服已赔",
                        type: RooSelectType.plane,
                        height: 32.0,
                        width: 78.0,
                        status: tagList.contains(302)
                            ? RooSelectStatus.active
                            : RooSelectStatus.normal,
                        onPress: (bool seleced) {
                          if (seleced) {
                            tagList.add(302);
                          } else {
                            tagList.remove(302);
                          }
                        })),
                Container(
                    width: MediaQuery.of(context).size.width / 5,
                    child: RooSelect(
                        label: "客服扣款",
                        type: RooSelectType.plane,
                        height: 32.0,
                        width: 78.0,
                        status: tagList.contains(303)
                            ? RooSelectStatus.active
                            : RooSelectStatus.normal,
                        onPress: (bool seleced) {
                          if (seleced) {
                            tagList.add(303);
                          } else {
                            tagList.remove(303);
                          }
                        })),
                Container(
                    width: MediaQuery.of(context).size.width / 5,
                    child: RooSelect(
                        label: "已返佣",
                        type: RooSelectType.plane,
                        height: 32.0,
                        width: 78.0,
                        status: tagList.contains(304)
                            ? RooSelectStatus.active
                            : RooSelectStatus.normal,
                        onPress: (bool seleced) {
                          if (seleced) {
                            tagList.add(304);
                          } else {
                            tagList.remove(304);
                          }
                        })),
                Container(
                    width: MediaQuery.of(context).size.width / 5,
                    child: RooSelect(
                        label: "配送取消费",
                        type: RooSelectType.plane,
                        height: 32.0,
                        width: 78.0,
                        status: tagList.contains(305)
                            ? RooSelectStatus.active
                            : RooSelectStatus.normal,
                        onPress: (bool seleced) {
                          if (seleced) {
                            tagList.add(305);
                          } else {
                            tagList.remove(305);
                          }
                        })),
              ],
            ))
      ],
    );
  }

  /// 订单类型
  orderType() {
    ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_oq28h7mc_mv');
    ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_3u294tum_mv');
    return Column(
      children: [
        Row(
          children: [
            Text('订单类型',
                style: TextStyle(
                    color: Color(0xFF666666),
                    fontWeight: FontWeight.w400,
                    fontFamily: 'PingFangSC-Regular',
                    fontSize: 14))
          ],
        ),
        SizedBox(
          height: 16,
        ),
        Row(children: [
          Expanded(
              flex: 1,
              child: RooSelect(
                  label: "神抢手",
                  type: RooSelectType.plane,
                  width: 78.0,
                  height: 32.0,
                  status: tagList.contains(102)
                      ? RooSelectStatus.active
                      : RooSelectStatus.normal,
                  onPress: (bool seleced) {
                    if (seleced) {
                      tagList.add(102);
                    } else {
                      tagList.remove(102);
                    }
                  })),
          Expanded(
              flex: 3,
              child: RooSelect(
                  label: "拼好饭",
                  type: RooSelectType.plane,
                  width: 78.0,
                  height: 32.0,
                  status: tagList.contains(101)
                      ? RooSelectStatus.active
                      : RooSelectStatus.normal,
                  onPress: (bool seleced) {
                    if (seleced) {
                      tagList.add(101);
                    } else {
                      tagList.remove(101);
                    }
                  })),
        ])
      ],
    );
  }

  /// 其他类型
  otherType() {
    ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_ps4blh3y_mv');
    ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_cc9m0rou_mv');
    ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_ct5fvn6v_mv');
    return Column(
      children: [
        Row(
          children: [
            Text('其他分类',
                style: TextStyle(
                    color: Color(0xFF666666),
                    fontWeight: FontWeight.w400,
                    fontFamily: 'PingFangSC-Regular',
                    fontSize: 14))
          ],
        ),
        SizedBox(
          height: 16,
        ),
        Row(
          children: [
            Expanded(
                child: RooSelect(
                    label: "神会员红包",
                    type: RooSelectType.plane,
                    height: 32.0,
                    width: 78.0,
                    status: tagList.contains(401)
                        ? RooSelectStatus.active
                        : RooSelectStatus.normal,
                    onPress: (bool seleced) {
                      if (seleced) {
                        tagList.add(401);
                      } else {
                        tagList.remove(401);
                      }
                    })),
            Expanded(
                child: RooSelect(
                    label: "全城送",
                    type: RooSelectType.plane,
                    height: 32.0,
                    width: 78.0,
                    status: tagList.contains(402)
                        ? RooSelectStatus.active
                        : RooSelectStatus.normal,
                    onPress: (bool seleced) {
                      if (seleced) {
                        tagList.add(402);
                      } else {
                        tagList.remove(402);
                      }
                    })),
            Expanded(
                flex: 2,
                child: RooSelect(
                    label: "加过小费",
                    type: RooSelectType.plane,
                    height: 32.0,
                    width: 78.0,
                    status: tagList.contains(403)
                        ? RooSelectStatus.active
                        : RooSelectStatus.normal,
                    onPress: (bool seleced) {
                      if (seleced) {
                        tagList.add(403);
                      } else {
                        tagList.remove(403);
                      }
                    }))
          ],
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_ebaoq6t5_mv');
    return Padding(
        padding:
            EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
        child: RooBottomModal(
          showRightCloseIcon: true,
          titleText: '筛选',
          height: 400,
          bottomContainer: Flex(
            direction: Axis.horizontal,
            children: [
              SizedBox(
                width: 12,
              ),
              Expanded(
                child: Container(
                  height: 44,
                  width: 172,
                  child: RooShapeButton(
                    child: Text("重置"),
                    onPressed: () {
                      widget.restAmount();
                    },
                  ),
                ),
              ),
              SizedBox(
                width: 8,
              ),
              Expanded(
                child: Container(
                  height: 44,
                  width: 172,
                  child: RooGradientButton(
                    child: Text("完成"),
                    onPressed: () {
                      widget.selectAmount(tagList);
                    },
                    disabled: _isDis(),
                  ),
                ),
              ),
              SizedBox(
                width: 12,
              ),
            ],
          ),
          bottomMainText: "",
          bottomSecText: "",
          contentContainer: Container(
              padding: EdgeInsets.only(left: 12, right: 12),
              child: Transform.translate(
                  offset: Offset(0, -22),
                  child: ListView(
                    children: [
                      Row(
                        children: [
                          Text(
                            '结算金额',
                            style: TextStyle(
                              fontWeight: FontWeight.w400,
                              fontSize: 14,
                              color: Color(0xFF666666),
                            ),
                          )
                        ],
                      ),
                      Row(
                        children: [
                          Expanded(
                            child: Container(
                                width: 113.5,
                                height: 27,
                                margin: EdgeInsets.only(top: 12),
                                decoration: BoxDecoration(
                                    color: Color(0xFFEEEEEE),
                                    border: Border.all(
                                        color: Color(0xFFEEEEEE), width: 0.5),
                                    borderRadius: BorderRadius.circular((4.0))),
                                child: TextField(
                                  controller:
                                      widget.selectionLowAmountController,
                                  onChanged: (v) {
                                    minValue = v;
                                    setState(() {});
                                  },
                                  keyboardType: TextInputType.numberWithOptions(
                                      decimal: true),
                                  style: TextStyle(
                                    color: Color(0xFF222222),
                                    fontSize: 14,
                                  ),
                                  inputFormatters: [
                                    // FilteringTextInputFormatter.allow(
                                    //     numberReg),
                                    PrecisionLimitFormatter(2),
                                    LengthLimitingTextInputFormatter(11),
                                  ],
                                  decoration: InputDecoration(
                                      prefix: Text(
                                        '¥',
                                        style: TextStyle(
                                          color: Color(0xFF222222),
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                          height: 2,
                                        ),
                                      ),
                                      labelStyle: TextStyle(
                                          color: Color(0xFF222222),
                                          fontSize: 14,
                                          height: 0,
                                          fontWeight: FontWeight.w400),
                                      hintText: '最低金额',
                                      hintStyle: TextStyle(
                                        color: Color(0xFF999999),
                                        fontSize: 14,
                                        height: 0,
                                      ),
                                      contentPadding: EdgeInsets.symmetric(
                                        vertical: 8,
                                        horizontal: 12,
                                      ),
                                      border: InputBorder.none),
                                )),
                          ),
                          SizedBox(
                            width: 15,
                          ),
                          Text(
                            '至',
                            style: TextStyle(
                                height: 3,
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF222222)),
                          ),
                          SizedBox(
                            width: 15,
                          ),
                          Expanded(
                            child: Container(
                                width: 113.5,
                                height: 27,
                                margin: EdgeInsets.only(top: 12),
                                decoration: BoxDecoration(
                                    color: Color(0xFFEEEEEE),
                                    border: Border.all(
                                        color: Color(0xFFEEEEEE), width: 0.5),
                                    borderRadius: BorderRadius.circular((4.0))),
                                child: TextField(
                                  controller:
                                      widget.selectionDownAmountController,
                                  onChanged: (v) {
                                    maxValue = v;
                                    setState(() {});
                                  },
                                  keyboardType: TextInputType.numberWithOptions(
                                      decimal: true),
                                  style: TextStyle(
                                    color: Color(0xFF222222),
                                    fontSize: 14,
                                  ),
                                  inputFormatters: [
                                    // FilteringTextInputFormatter.allow(
                                    //     numberReg),
                                    PrecisionLimitFormatter(2),
                                    LengthLimitingTextInputFormatter(11),
                                  ],
                                  decoration: InputDecoration(
                                      prefix: Text(
                                        '¥',
                                        style: TextStyle(
                                          color: Color(0xFF222222),
                                          fontSize: 14,
                                          fontWeight: FontWeight.w400,
                                          height: 2,
                                        ),
                                      ),
                                      labelStyle: TextStyle(
                                          color: Color(0xFF222222),
                                          fontSize: 14,
                                          height: 0,
                                          fontWeight: FontWeight.w400),
                                      hintText: '最高金额',
                                      hintStyle: TextStyle(
                                        color: Color(0xFF999999),
                                        fontSize: 14,
                                        height: 0,
                                      ),
                                      contentPadding: EdgeInsets.symmetric(
                                        vertical: 8,
                                        horizontal: 12,
                                      ),
                                      border: InputBorder.none),
                                )),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: StringUtil.isNotEmpty(proMsg) ||
                                StringUtil.isNotEmpty(errorMsg)
                            ? 8
                            : 0,
                      ),
                      Row(
                        children: [
                          Text(
                            proMsg ?? '',
                            style: TextStyle(color: Colors.blue, fontSize: 14),
                          )
                        ],
                      ),
                      Row(
                        children: [
                          Text(
                            errorMsg ?? '',
                            style: TextStyle(color: Colors.red, fontSize: 14),
                          )
                        ],
                      ),
                      SizedBox(
                        height: StringUtil.isNotEmpty(proMsg) ||
                                StringUtil.isNotEmpty(errorMsg)
                            ? 8
                            : 0,
                      ),

                      /// 订单状态
                      orderStatus(),
                      SizedBox(
                        height: 30,
                      ),

                      /// 售后状态
                      afterSalesstatus(),
                      SizedBox(
                        height: 30,
                      ),

                      /// 订单类型
                      orderType(),
                      SizedBox(
                        height: 30,
                      ),

                      /// 其他类型
                      otherType()
                    ],
                  ))),
        ));
  }
}
