import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:roo_flutter/tools/response_system.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_fe_flutter_finance/src/common/flap.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/arrow.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/empty.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/scrollText.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/orderList/amountSelect.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/orderList/orderCart.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/orderList/orderEmpty.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/orderList/orderUtils.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/progressOrder/model/card_model.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/progressOrder/widgets/header.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/billChargeListForOrder.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/requests.dart';
import 'package:waimai_e_fe_flutter_finance/waimai_e_fe_flutter_finance.dart';

import '../../components/date_picker_adapted/roo_date_picker_adapted.dart';

@Flap('finance')
class OrderListPage extends StatelessWidget {
  OrderListPage({this.param, this.pageName});
  final Map<dynamic, dynamic> param;
  final String pageName;

  @override
  Widget build(BuildContext context) {
    return LoadingPage(
      child: OrderListWidget(
        pageName: pageName,
        param: param,
      ),
    );
  }
}

class OrderListWidget extends StatefulWidget {
  OrderListWidget({String pageName, this.param})
      : this.pageName =
            pageName ?? (param != null ? param['mtf_page'].toString() : '');
  final Map<dynamic, dynamic> param;
  final String pageName;

  @override
  OrderListWidgetState createState() => OrderListWidgetState();
}

class OrderListWidgetState extends State<OrderListWidget>
    with OrderListMixin, TickerProviderStateMixin {
  int unFinishIndex = 0;
  String _dailyBillDate = DateFormat.formatYYYYMMs(DateTime.now());
  TextEditingController selectionLowAmountController = TextEditingController();
  TextEditingController selectionDownAmountController = TextEditingController();
  OrderCardModel orderList;
  List<WmPoiBillChargeDynamicVoList> wmPoiBillChargeDynamicVoList = [];
  TabController tabController;
  int type = 0;
  int pageNo = 1;
  int get pageSize => ResponsiveSystem.bothAppPc(runApp: 20, runPc: 10);
  bool showLoadMore = false;
  bool isLoading = false;
  List<OrgizeListModel> orgizeListModelList = [];
  final ScrollController _controller = ScrollController();
  bool isSelectAmount = false;
  bool isSelectDate = false;
  String startDate;
  String endDate;
  String minAmount;
  String maxAmount;
  RangeDatePickerControllers rangeController = RangeDatePickerControllers();
  List tagLists;

  /// ！！！！此处不是三个月，是90天
  int limitDays = DateTime.now()
      .difference(DateTime(
          DateTime.now().year, DateTime.now().month, DateTime.now().day - 89))
      .inDays;
  int ranLimitDays = 0;
  DateTime firstDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    ReportLX.pv(pageKeyInfo, cid);

    /// asg引导
    // OrderUtils.tryPlayOrderStatisticAsg();
    if (widget.param != null && widget?.param['tabIndex'] != null) {
      unFinishIndex = 2;
    }

    /// 请求初始化订单数据
    getDatas(true);
    tabController = TabController(
        length: _getOrderStatus().length,
        vsync: this,
        initialIndex:
            widget.param != null && widget?.param['tabIndex'] != null ? 1 : 0);
    List<DateTime> _rangeDate = [];
    rangeController.rangeDate = _rangeDate;
    OrderUtils.tryPlayOrderTimeStatisticAsg();
  }

  @override
  void dispose() {
    super.dispose();
  }

  /// 根据所选不同标签进行埋点
  /// 埋点逻辑 ： 点击完成按钮时埋点生效
  toMcMv(List tagLists) {
    if (tagLists.contains(203)) {
      ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_pcibfmri_mc');
    }
    if (tagLists.contains(201)) {
      ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_6beq7zby_mc');
    }
    if (tagLists.contains(202)) {
      ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_6k7yfhl2_mc');
    }
    if (tagLists.contains(301)) {
      ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_00it694z_mc');
    }
    if (tagLists.contains(302)) {
      ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_2jbvqdb1_mc');
    }
    if (tagLists.contains(303)) {
      ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_r36s6n1d_mc');
    }
    if (tagLists.contains(304)) {
      ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_9gtzj28h_mc');
    }
    if (tagLists.contains(102)) {
      ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_oq28h7mc_mc');
    }
    if (tagLists.contains(101)) {
      ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_3u294tum_mc');
    }
    if (tagLists.contains(401)) {
      ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_ps4blh3y_mc');
    }
    if (tagLists.contains(402)) {
      ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_ct5fvn6v_mc');
    }
    if (tagLists.contains(403)) {
      ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_cc9m0rou_mc');
    }
  }

  /// 获取订单数据
  getDatas(bool init) {
    ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_968dm8yc_mc');
    if (isLoading == true) {
      return;
    }
    if (init == true) {
      wmPoiBillChargeDynamicVoList = [];
      showLoadMore == false;
      pageNo = 1;
    }
    isLoading = true;
    orgizeListModelList = [];
    Loading.showLoading();
    Map params = {
      'type': unFinishIndex,
      'pageNo': pageNo,
      'pageSize': pageSize,
      'minAmount': !StringUtil.isEmpty(minAmount) ? num.parse(minAmount) : '',
      'maxAmount': !StringUtil.isEmpty(maxAmount) ? num.parse(maxAmount) : '',
      'startDate': startDate,
      'endDate': endDate,
    };
    if (tagLists != null && tagLists.length > 0) {
      params['tags'] = tagLists.join(',');
      toMcMv(tagLists);
    }
    comGetApi(
      path: '/finance/waimai/poiBillCharge/api/v2/orderList',
      params: params,
    ).then((value) {
      if (value?.data != null) {
        orderList = OrderCardModel.fromJson(value.data);
        if (orderList.wmPoiBillChargeDynamicVoList.isNotEmpty) {
          pageNo = pageNo + 1;
          calcData(orderList);
        }
        ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_968dm8yc_mv');
      }
    }).whenComplete(() {
      isLoading = false;
      Loading.dismissLoading();
    });
  }

  /// 订单状态
  _orderStatus() {
    return ProgressOrderHeader(
        title: orderList?.unFinishOrderTip ?? '',
        content: orderList?.unFinishOrderTipDetail ?? '',
        statusColors: [Color(0xFFD1E8FF), Color(0xFFE4F1FF)]);
  }

  /// 卡片数据处理
  calcData(OrderCardModel orderList) {
    if (orderList.wmPoiBillChargeDynamicVoList.length < pageSize) {
      showLoadMore = true;
    }
    wmPoiBillChargeDynamicVoList.addAll(orderList.wmPoiBillChargeDynamicVoList);
    Map<int, List<WmPoiBillChargeDynamicVoList>> timeMap = {};
    wmPoiBillChargeDynamicVoList?.forEach((WmPoiBillChargeDynamicVoList item) {
      DateTime dateTime =
          DateTime.fromMillisecondsSinceEpoch(item.outCreateTimestamp * 1000);
      int _stamp = DateTime(dateTime.year, dateTime.month, dateTime.day)
          .millisecondsSinceEpoch;
      if (timeMap[_stamp] == null) {
        timeMap[_stamp] = [];
      }
      timeMap[_stamp].add(item);
    });
    timeMap.forEach((k, v) {
      orgizeListModelList.add(
        OrgizeListModel(
            orderList: v, timeStamp: k, timeString: DateFormat.formatMMDDs(k)),
      );
    });
    orgizeListModelList.sort((a, b) => b.timeStamp - a.timeStamp);
    setState(() {});
  }

  /// 订单状态数据
  List<String> _getOrderStatus() {
    List<String> res = ['全部'];

    ///...请求接口状态数据 然后添加到 res中
    res.add('进行中 ${orderList?.unFinishCount ?? 0}');
    return res;
  }

  /// 数字键盘 浮点数处理
  reserveFloat(String value) {
    if (!StringUtil.isEmpty(value)) {
      List<String> res = value.toString().split('.');
      if (res.length == 1) {
        value = value.toString() + '.00';
        return value;
      }
      if (res.length > 1) {
        if (res[1].length < 2) {
          value = value.toString() + '0';
        }
        return value;
      }
    }
    return '';
  }

  /// 筛选金额
  _selectAmount(List tagList) {
    /// 最低金额处理
    if (selectionLowAmountController.text != '' &&
        double.tryParse(selectionLowAmountController.text) != null) {
      minAmount = double.parse(selectionLowAmountController.text).toString();
    } else if (selectionLowAmountController.text != '' &&
        int.tryParse(selectionLowAmountController.text) != null) {
      minAmount = int.parse(selectionLowAmountController.text).toString();
    }

    /// 最高金额处理
    if (selectionDownAmountController.text != '' &&
        double.tryParse(selectionDownAmountController.text) != null) {
      maxAmount = double.parse(selectionDownAmountController.text).toString();
    } else if (selectionDownAmountController.text != '' &&
        int.tryParse(selectionDownAmountController.text) != null) {
      maxAmount = int.parse(selectionDownAmountController.text).toString();
    }

    /// 金额转换 ： 当最小金额大于最大金额时 将根据大小金额进行对调
    num min = 0;
    num max = 0;
    if (!StringUtil.isEmpty(minAmount) && !StringUtil.isEmpty(maxAmount)) {
      if (num.parse(minAmount) > num.parse(maxAmount)) {
        min = num.parse(maxAmount);
        max = num.parse(minAmount);
      } else {
        min = num.parse(minAmount);
        max = num.parse(maxAmount);
      }
      minAmount = min.toString();
      maxAmount = max.toString();
    }

    /// 如果填写最大金额为0的时候 自动转化为最小金额为0
    /// 目的：为了支持商家可以输入0
    if (!StringUtil.isEmpty(maxAmount) && maxAmount == '0.0') {
      minAmount = '0';
      maxAmount = '';
    }
    selectionLowAmountController.text = reserveFloat(minAmount);
    selectionDownAmountController.text = reserveFloat(maxAmount);
    tagLists = tagList;
    setState(() {});
    getDatas(true);
    Navigator.pop(context);

    /// 判断是否进行筛选
    if (StringUtil.isEmpty(selectionLowAmountController.text) &&
        StringUtil.isEmpty(selectionDownAmountController.text) &&
        tagList.length == 0) {
      setState(() {
        isSelectAmount = false;
      });
    } else {
      setState(() {
        isSelectAmount = true;
      });
    }

    /// 结算金额埋点
    if (!StringUtil.isEmpty(minAmount) || !StringUtil.isEmpty(maxAmount)) {
      ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_ebaoq6t5_mc');
    }
  }

  /// 重置金额
  _restAmount() {
    selectionLowAmountController.text = '';
    selectionDownAmountController.text = '';
    minAmount = '';
    maxAmount = '';
    tagLists = [];
    setState(() {});
    getDatas(true);
    Navigator.pop(context);
    setState(() {
      isSelectAmount = false;
    });
  }

  _restButton() {
    selectionLowAmountController.text = '';
    selectionDownAmountController.text = '';
    minAmount = '';
    maxAmount = '';
    tagLists = [];
    setState(() {});
    getDatas(true);
    setState(() {
      isSelectAmount = false;
    });
  }

  /// 订单金额筛选条件
  _buildTabFilter() {
    ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_o4853iv0_mv');
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        RooTabs(
          controller: tabController,
          unselectedTabContainerDecoration: BoxDecoration(
              color: Color(0xFFFFFFFF),
              borderRadius: BorderRadius.all(Radius.circular(4))),
          backgroundColor: Color(0x33F5F6FA),
          tabHeight: 32.0,
          tabMargin: EdgeInsets.only(
            right: 10.0,
          ),
          type: RooTabsType.card,
          tabBarPaddingLeft: 0,
          selectedTextStyle: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 14,
              color: Color(0xFF222222),
              fontFamily: 'PingFangSC-Medium'),
          unselectedTextStyle: TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 14,
              color: Color(0xFF666666),
              fontFamily: 'PingFangSC-Regular'),
          onIndexChange: (int index) {
            ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_s8gvkzmo_mc');
            if (index == 0) {
              setState(() {
                unFinishIndex = 0;
              });
            } else if (index == 1) {
              ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_s8gvkzmo_mv');
              setState(() {
                unFinishIndex = 2;
              });
            }
            getDatas(true);
          },
          tabTitles: _getOrderStatus(),
        ),
        GestureDetector(
          onTap: () {
            ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_o4853iv0_mc');
            showRooBottomModal(
                context: context,
                builder: (context) {
                  return AmountSelect(
                      restAmount: _restAmount,
                      selectAmount: (tagLists) => _selectAmount(tagLists),
                      tagLists: tagLists,
                      selectionLowAmountController:
                          selectionLowAmountController,
                      selectionDownAmountController:
                          selectionDownAmountController);
                });
          },
          child: Container(
              child: Row(
            children: [
              Image.network(
                'https://p0.meituan.net/ingee/f8c91deecb13e8b5eb14c4bca3326e271383.png',
                width: 20,
                height: 20,
                color: isSelectAmount ? Color(0xFFFF6A00) : Color(0xFF666666),
              ),
              Text(
                '筛选',
                style: TextStyle(
                  color: isSelectAmount ? Color(0xFFFF6A00) : Color(0xFF666666),
                  fontWeight: FontWeight.w400,
                  fontSize: 14,
                ),
              ),
            ],
          )),
        )
      ],
    );
  }

  /// 选中日期
  onRangeChange(List<DateTime> dtList) {
    ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_2ow14xjx_mc');
    ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_2ow14xjx_mv');
    startDate = DateFormat.formatYYYYMMDDs(dtList[0]);
    endDate = DateFormat.formatYYYYMMDDs(dtList[1]);
    if (startDate == endDate) {
      _dailyBillDate = startDate;
    } else {
      _dailyBillDate = '${startDate} 至 ${endDate}';
    }
    isSelectDate = true;
    setState(() {});
    getDatas(true);
  }

  /// 选中日期后限制状态
  onFirseRangeChange(DateTime dts) {
    int indays1 = dts
        .difference(DateTime(
            DateTime.now().year, DateTime.now().month, DateTime.now().day - 89))
        .inDays;
    limitDays = indays1 < 31 ? indays1 : 31;
    ranLimitDays = 31;
    firstDate = dts;
    setState(() {});
  }

  /// 重置日期
  _restDate() {
    _dailyBillDate = DateFormat.formatYYYYMMs(DateTime.now());
    firstDate = DateTime.now();
    limitDays = DateTime.now()
        .difference(DateTime(
            DateTime.now().year, DateTime.now().month, DateTime.now().day - 89))
        .inDays;
    ranLimitDays = 0;
    rangeController.rangeDate = [];
    startDate = '';
    endDate = '';
    isSelectDate = false;
    orderList = null;
    setState(() {});
    getDatas(true);
  }

  Widget _buildApp() {
    return (orderList == null)
        ? Empty()
        : Container(
            color: Color(0xFFF5F6FA),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Container(
                  color: Color(0xFFFFF9EA),
                  height: 36,
                  child: ScrollText(
                    child: Text(
                      '右侧金额为外卖订单的结算金额（不包含保险、部分退等其他费用）   ',
                      style: TextStyle(
                          height: 2.3,
                          fontSize: 12,
                          color: Color(0xFF000000),
                          fontWeight: FontWeight.w400),
                    ),
                  ),
                ),
                Container(
                    padding: EdgeInsets.only(left: 12, right: 12, bottom: 12),
                    child: _buildTabFilter()),
                unFinishIndex == 2
                    ? Container(
                        padding:
                            EdgeInsets.only(left: 12, right: 12, bottom: 12),
                        child: _orderStatus(),
                      )
                    : SizedBox.shrink(),
                Transform.translate(
                  offset: Offset(2, 0),
                  child: Row(children: [
                    RooDatePickerAdapteds(
                      isCalendar: true,
                      isRange: true,
                      rangeFirstLastDate: [
                        firstDate.subtract(Duration(days: limitDays)),
                        firstDate.add(Duration(
                            days: DateTime.now().difference(firstDate).inDays <
                                    31
                                ? DateTime.now().difference(firstDate).inDays
                                : ranLimitDays)),
                      ],
                      onFirseRangeChange: onFirseRangeChange,
                      rangeController: rangeController,
                      onRangeChange: onRangeChange,
                      customDisplayWidget: Row(
                        children: [
                          Text(_dailyBillDate ?? '',
                              strutStyle: StrutStyle(
                                forceStrutHeight: true,
                                height: 1,
                              ),
                              style: TextStyle(
                                color: isSelectDate
                                    ? Color(0xFFFF6A00)
                                    : Color(0xFF222222),
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              )),
                          ArrowIcon(
                              direction:
                                  isSelectDate ? null : DirectionEnum.down,
                              color: isSelectDate
                                  ? ArrowIconColorEnum.orange
                                  : ArrowIconColorEnum.black)
                        ],
                      ),
                    ),
                    isSelectDate
                        ? GestureDetector(
                            onTap: () {
                              _restDate();
                            },
                            child: Text('重置',
                                strutStyle: StrutStyle(
                                  forceStrutHeight: true,
                                  height: 1,
                                ),
                                style: TextStyle(
                                  color: Color(0xFFFF6A00),
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                )))
                        : SizedBox.shrink()
                  ]),
                ),
                Expanded(
                    flex: 1,
                    child: Scaffold(
                        body: Scrollbar(
                            controller: _controller,
                            child: EasyRefresh(
                                header:
                                    PlatformTool.isPC ? null : MaterialHeader(),
                                footer: PlatformTool.isPC
                                    ? null
                                    : MaterialFooter(enableInfiniteLoad: false),
                                onRefresh: () async {
                                  if (PlatformTool.isPC) {
                                    return null;
                                  }
                                  return getDatas(true);
                                },
                                onLoad: () async {
                                  if (PlatformTool.isPC) {
                                    return null;
                                  }
                                  return getDatas(false);
                                },
                                child: SingleChildScrollView(
                                    controller: _controller,
                                    child: Container(
                                      padding:
                                          EdgeInsets.only(left: 12, right: 12),
                                      child: Column(
                                        children: <Widget>[
                                          wmPoiBillChargeDynamicVoList.length ==
                                                  0
                                              ? OrderEmpty(
                                                  imgSize: 120,
                                                  title: '无匹配结果订单',
                                                  restButtonText: '清空筛选条件',
                                                  restButton: _restButton)
                                              : Column(
                                                  children: [
                                                    ...orgizeListModelList
                                                        .map((e) => OrderCard(
                                                              orgizeListModel:
                                                                  e,
                                                              unFinishIndex:
                                                                  unFinishIndex,
                                                              pageKeyInfo:
                                                                  pageKeyInfo,
                                                              cid: cid,
                                                            ))
                                                        .toList(),
                                                  ],
                                                ),
                                          SizedBox(height: 10),
                                          showLoadMore == true
                                              ? Text(
                                                  isLoading == false
                                                      ? '订单已加载完成'
                                                      : '',
                                                  style: TextStyle(
                                                    fontSize: 12,
                                                    color: Color(0xFF999999),
                                                    fontWeight: FontWeight.w300,
                                                  ),
                                                )
                                              : SizedBox.shrink(),
                                          SizedBox(height: 20),
                                        ],
                                      ),
                                    ))))))
              ],
            ));
  }

  @override
  Widget build(BuildContext context) {
    return _buildApp();
  }
}
