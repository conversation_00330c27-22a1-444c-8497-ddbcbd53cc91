import 'package:flutter/material.dart';
import 'package:roo_flutter/basic_components/tag/roo_tag.dart';
import 'package:roo_flutter/roo_common_themes/roo_flutter_themes.dart';
import 'package:sticky_headers/sticky_headers.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/arrow.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/progressOrder/model/card_model.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/billChargeListForOrder.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart';

class OrderCard extends StatelessWidget {
  const OrderCard(
      {Key key,
      this.orgizeListModel,
      this.unFinishIndex,
      this.cid,
      this.pageKeyInfo})
      : super(key: key);
  final OrgizeListModel orgizeListModel;
  final int unFinishIndex;
  final String cid;
  final String pageKeyInfo;

  /// 订单状态标签内容
  tagsContent(int code, String name) {
    if ([201, 202, 203, 204].contains(code)) {
      return name + ' ';
    }
    return '';
  }

  /// 订单标签颜色
  orderStatusColor(int code) {
    final codeColorMap = {
      201: Color(0xFF999999),
      202: Color(0xFFFF192D),
      203: Color(0xFFFF192D),
    };
    return codeColorMap[code] ?? Color(0xFF222222);
  }

  /// 订单状态
  _orderStatus(List<OrderTag> tags) {
    List<Widget> tagList = [];
    tags != null && tags.length > 0
        ? tags.forEach((element) {
            tagList.add(Container(
              child: Text(
                tagsContent(element.code, element.name),
                style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: orderStatusColor(element.code),
                    fontSize: 14,
                    height: 1.1),
              ),
            ));
          })
        : SizedBox.shrink();
    return tagList;
  }

  /// 售后状态 + 其他类型标签内容
  afterSaleStatusContent(int code, String name) {
    if ([301, 302, 303, 304, 305, 401, 402, 403].contains(code)) {
      return name ?? '';
    }
    return SizedBox.shrink();
  }

  /// 售后状态 + 其他类型标签背景颜色
  afterSaleStatusBgColor(int code) {
    final codeColorMap = {
      301: Color(0x1A12B331),
      302: Color(0x1A12B331),
      304: Color(0x1A12B331),
      303: Color(0x1AFF192D),
      403: Color(0x1AFF192D),
      305: Color(0x1AFF192D),
      401: Color(0xFFF5F6FA),
    };
    return codeColorMap[code];
  }

  /// 售后状态 + 其他类型内容文字颜色
  afterSaleStatusColor(int code) {
    final codeColorMap = {
      301: Color(0xFF12B331),
      302: Color(0xFF12B331),
      304: Color(0xFF12B331),
      303: Color(0xFFFF192D),
      403: Color(0xFFFF192D),
      305: Color(0xFFFF192D),
      401: Color(0xFF222222),
      402: Color(0xFFFF6A00),
    };
    return codeColorMap[code];
  }

  /// 售后状态 + 其他类型
  _afterSaleStatus(List<OrderTag> tags) {
    List<Widget> list = [];
    tags != null && tags.length > 0
        ? tags.forEach((element) {
            if ([301, 302, 303, 304, 305, 401, 403].contains(element.code)) {
              list.add(Container(
                  margin: EdgeInsets.only(
                      left: 10, bottom: tags.length > 5 ? 10 : 0),
                  child: RooTag(
                    textContents:
                        afterSaleStatusContent(element.code, element.name),
                    padding: EdgeInsets.symmetric(vertical: 1, horizontal: 5),
                    tagDecoration: BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(1.5)),
                        border: Border.all(
                            color: RooColors.transparent, width: 0.5),
                        color: afterSaleStatusBgColor(element.code)),
                    tagTextStyle: TextStyle(
                        color: afterSaleStatusColor(element.code),
                        fontSize: 12,
                        fontWeight: FontWeight.w400),
                  )));
            } else if (element.code == 402) {
              list.add(
                Container(
                  padding: EdgeInsets.only(left: 4, right: 4, top: 1),
                  margin: EdgeInsets.only(
                      left: 10, bottom: tags.length > 5 ? 10 : 0),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(1.5)),
                      border: Border.all(color: Color(0x1AFF6A00))),
                  child: Text(element.name ?? '',
                      style: TextStyle(
                          color: afterSaleStatusColor(element.code),
                          fontSize: 12,
                          fontWeight: FontWeight.w400)),
                ),
              );
            }
          })
        : SizedBox.shrink();
    return list;
  }

  orderImage(int code) {
    if (code == 101) {
      return Container(
          width: 80,
          height: 16,
          margin: EdgeInsets.only(bottom: 1.5),
          child: Image.network(
              'https://p0.meituan.net/ingee/1072b09fcc7fa2203397f7b90e313dfd7610.png'));
    }
    if (code == 102) {
      return Container(
        width: 50,
        height: 16,
        margin: EdgeInsets.only(bottom: 1.5),
        child: Image.network(
            'https://p0.meituan.net/ingee/7a57dc739de387f7e0b60c4f7271d9387002.png'),
      );
    }
  }

  /// 神抢手/拼好饭 icon
  _orderIcon(List<OrderTag> tags) {
    List<Widget> list = [];
    tags != null && tags.length > 0
        ? tags.forEach((element) {
            list.add(Container(
              child: orderImage(element.code),
            ));
          })
        : SizedBox.shrink();
    return list;
  }

  Widget renderList(BuildContext context) {
    List<Widget> arr = [];
    orgizeListModel.orderList.asMap().forEach((index, ele) {
      arr.add(
        GestureDetector(
          onTap: () {
            ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_vf5qiq3x_mc');
            Map<String, dynamic> orderParams = {
              "chargeTypeCode": ele.chargeTypeCode,
              "billChargeId": ele.billChargeId,
              "wmOrderViewId": ele.wmOrderViewId,
              "dailyBillDate":
                  DateFormat.formatYYYYMMDD(ele.dailyBillDateTimestamp),
              "poiOrderPushDayseq": ele.poiOrderPushDayseq,
              "status": 2,
            };

            RouterTools.flutterPageUrl(
              context,
              '/orderQuery',
              params: orderParams,
            );
          },
          child: Container(
              margin: EdgeInsets.only(
                  top: index == 0 ? 10 : 40,
                  bottom:
                      index == orgizeListModel.orderList.length - 1 ? 10 : 0),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text.rich(TextSpan(children: [
                        TextSpan(
                            text: '#${ele.poiOrderPushDayseq} ',
                            style: TextStyle(
                              fontSize: 16,
                              color: ele.tags != null &&
                                      ele.tags.length > 0 &&
                                      ele.tags.any(
                                          ((element) => element.code == 201))
                                  ? Color(0xFF999999)
                                  : Color(0xFF222222),
                              fontWeight: FontWeight.w500,
                            )),
                        WidgetSpan(
                            alignment: PlaceholderAlignment.middle,
                            child: Wrap(
                              children: _orderStatus(ele.tags),
                            )),
                        WidgetSpan(
                            alignment: PlaceholderAlignment.middle,
                            child: Wrap(
                              children: _orderIcon(ele.tags),
                            ))
                      ])),
                      Wrap(
                        children: [
                          Row(
                            children: [
                              Text(
                                '¥',
                                style: TextStyle(
                                    fontWeight: FontWeight.w500,
                                    fontSize: 12,
                                    color: ele.tags != null &&
                                            ele.tags.length > 0 &&
                                            ele.tags.any(((element) =>
                                                element.code == 201))
                                        ? Color(0xFF999999)
                                        : Color(0xFF222222),
                                    height: 2),
                              ),
                              SizedBox(
                                width: 2,
                              ),
                              Text(
                                MoneyTool.formatMoney(ele.chargeAmount) ?? '',
                                style: TextStyle(
                                  fontSize: 18,
                                  color: ele.tags != null &&
                                          ele.tags.length > 0 &&
                                          ele.tags.any(((element) =>
                                              element.code == 201))
                                      ? Color(0xFF999999)
                                      : Color(0xFF222222),
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              ArrowIcon(
                                color: ele.tags != null &&
                                        ele.tags.length > 0 &&
                                        ele.tags.any(
                                            ((element) => element.code == 201))
                                    ? ArrowIconColorEnum.grey
                                    : ArrowIconColorEnum.black,
                              ),
                            ],
                          ),
                        ],
                      )
                    ],
                  ),
                  SizedBox(height: 15),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                          DateFormat.formatFull(ele.outCreateTimestamp * 1000)
                                  .split(' ')[1] ??
                              '',
                          style: TextStyle(
                            fontSize: 14,
                            color: Color(0xFF999999),
                          )),
                      Expanded(
                          child: Wrap(
                        alignment: WrapAlignment.end,
                        children: _afterSaleStatus(ele.tags),
                      ))
                    ],
                  )
                ],
              )),
        ),
      );
    });
    return Column(children: arr);
  }

  @override
  Widget build(BuildContext context) {
    String getDay = DateFormat.getIsday(orgizeListModel.timeStamp);
    return Container(
      child: StickyHeader(
        header: Container(
            padding: EdgeInsets.only(top: 12, bottom: 12),
            color: Color(0xFFF5F6FA),
            child: Row(
              children: [
                Text(
                  '${getDay}${orgizeListModel.timeString} 下单',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF999999),
                  ),
                ),
              ],
            )),
        content: Container(
          padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.5),
          ),
          child: renderList(context),
        ),
      ),
    );
  }
}
