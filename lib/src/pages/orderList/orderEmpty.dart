import 'package:flutter/material.dart';
import 'package:roo_flutter/basic_components/button/roo_shape_button.dart';

class OrderEmpty extends StatelessWidget {
  OrderEmpty({
    this.title = '无符合结果订单',
    this.margin = const EdgeInsets.all(10),
    this.imgSize = 80,
    this.restButton,
    this.restButtonText,
  });
  final String title;
  final EdgeInsets margin;
  final Function restButton;
  final String restButtonText;

  /// 图片大小
  final double imgSize;

  @override
  Widget build(BuildContext context) {
    return Center(
        child: Container(
      margin: margin,
      child: Column(
        children: <Widget>[
          Container(
            margin: EdgeInsets.only(top: 50, bottom: 20),
            child: Image.network(
              // 更换新袋鼠链接
              'https://p0.meituan.net/ingee/af1c8a89b1295e74f00fa14390776ef38208.png',
              width: imgSize,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              color: Color(0xFF999999),
            ),
          ),
          SizedBox(
            height: 12,
          ),
          restButtonText != null && restButtonText != ''
              ? Container(
                  width: 125,
                  height: 40,
                  child: RooShapeButton(
                    child: Text(restButtonText),
                    onPressed: () {
                      restButton();
                    },
                    disabled: false,
                  ),
                )
              : SizedBox.shrink()
        ],
      ),
    ));
  }
}
