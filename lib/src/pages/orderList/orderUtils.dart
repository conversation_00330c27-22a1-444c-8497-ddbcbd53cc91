import 'package:mt_flutter_knb/mt_flutter_knb.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/util/index.dart';

class OrderUtils {
  static String orderStatisticGuideAsgid = 'asg__waimaie__finace_order';

  static void tryPlayOrderStatisticAsg() {
    KNB.getStorage(key: orderStatisticGuideAsgid).then((flag) {
      String value = flag['value'];
      if (value == null || value.isEmpty) {
        playOrderStatisticAsg();
      }
    });
  }

  static void playOrderStatisticAsg() {
    KNB.use('asg.playASG', {
      'asgId': orderStatisticGuideAsgid,
      'max_play_count': -1
    }).then((value) {
      print('play asg__waimaie__finace_order result: ' + value.toString());
      KNB.setStorage(key: orderStatisticGuideAsgid, value: "true", level: 1);
    }).catchError((error) {
      print('play asg__waimaie__finace_order fail !');
    });
  }

  static void tryPlayOrderTimeStatisticAsg() {
    Util.getPoiId().then((values) {
      KNB
          .getStorage(key: '${values}asg__waimai_e__default_20231013140056')
          .then((flag) {
        String value = flag['value'];
        if (value == null || value.isEmpty) {
          playOrderTimeStatisticAsg();
        }
        return Future.value(null);
      });
    });
  }

  static void playOrderTimeStatisticAsg() {
    Util.getPoiId().then((values) {
      KNB.use('asg.playASG', {
        'asgId': 'asg__waimai_e__default_20231013140056',
        'max_play_count': -1
      }).then((value) {
        print('play asg__waimai_e__default_20231013140056 result: ' +
            value.toString());
        KNB.setStorage(
            key: '${values}asg__waimai_e__default_20231013140056',
            value: "true",
            level: 1);
      }).catchError((error) {
        print('play asg__waimai_e__default_20231013140056 fail !');
      });
    });
  }
}
