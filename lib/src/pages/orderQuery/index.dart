import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:waimai_e_fe_ffw_utils/platform_tool.dart';
import 'package:waimai_e_fe_flutter_finance/src/common/flap.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/arrow.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/card.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/modal.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/searchBar.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/orderDetail/index.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/orderList/orderEmpty.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/orderQuery.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/billChargeListForOrder.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/uiTools.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/util/index.dart';

@Flap('finance')
class OrderQueryPage extends StatelessWidget {
  OrderQueryPage({this.params, this.pageName});
  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  Widget build(BuildContext context) {
    return LoadingPage(
      child: OrderQueryWidget(
        pageName: pageName,
        params: params,
      ),
    );
  }
}

class OrderQueryWidget extends StatefulWidget {
  OrderQueryWidget({String pageName, this.params})
      : this.pageName =
            pageName ?? (params != null ? params['mtf_page'].toString() : '');
  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  OrderQueryWidgetState createState() => OrderQueryWidgetState();
}

class OrderQueryWidgetState extends State<OrderQueryWidget>
    with OrderQueryMixin {
  List<WmPoiBillChargeDynamicVoList> billList;
  // 门店信息
  String poiInfo;
  final bool isPC = PlatformTools.isPC;
  String wmPoiId;
  String orderViewIds;
  bool isCompleteBeforeCancelOrder;

  @override
  void initState() {
    super.initState();
    Util.getPoiId().then((value) {
      wmPoiId = value;
    });
    ReportLX.pv(pageKeyInfo, cid);
    if (widget.params != null && widget.params['status'] != null) {
      _fetchData(widget.params['wmOrderViewId'].toString());
      ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_vf5qiq3x_mv');
    } else {
      ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_e8sutnxf_mv');
    }
  }

  _fetchData(String orderViewId) {
    if (orderViewId == null || orderViewId.isEmpty) {
      EasyLoading.showToast('请输入订单展示ID');
      return;
    }
    Loading.showLoading();
    return fetchBillChargeListByOrderId(
            {'orderViewId': orderViewId, "source": 1})
        .then((BillChargeListForOrderModel response) {
      setState(() {
        billList = response?.wmPoiBillChargeDynamicVoList;
        poiInfo = response?.poiInfo;
        orderViewIds = orderViewId;
        isCompleteBeforeCancelOrder = response?.isCompleteBeforeCancelOrder;
      });
      Loading.dismissLoading();
    }).catchError((error) {
      Loading.dismissLoading();
    });
  }

  _buildTable() {
    return RooTable<WmPoiBillChargeDynamicVoList>(
      dataRowHeight: 160,
      headingRowHeight: 40,
      decoration: BoxDecoration(
          color: Color(0x66EEEEEE),
          border: Border.all(color: Color(0x4DCCCCCC)),
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(8), topRight: Radius.circular(8))),
      rowColorSelector: (int index) {
        // return index % 2 == 0 ? Colors.white : const Color(0xFFF5F5F5);
        return Colors.white;
      },
      emptyProperty: RooTableEmptyProperty(
          image: Image.network(
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/19e0ae7c1b9e3965/empty_roo_new.png',
        height: 126,
        width: 126,
      )),
      dataSource: billList,
      appRender: null,
      columns: [
        RooTableColumn<WmPoiBillChargeDynamicVoList>(
          label: '交易类型',
          cellRender: (WmPoiBillChargeDynamicVoList billItem) {
            String chargeName = billItem?.chargeTypeName ?? '';
            String ctn = chargeName.length > 0 ? '($chargeName)' : '';
            return Container(
              alignment: Alignment.center,
              padding: EdgeInsets.symmetric(
                horizontal: 15,
              ),
              child: Text('${billItem?.billChargeTypeName ?? ''}$ctn'),
            );
          },
        ),
        RooTableColumn<WmPoiBillChargeDynamicVoList>(
            label: '交易时间',
            cellRender: (WmPoiBillChargeDynamicVoList billItem) {
              String poiOrderPushDayseqPC = billItem?.chargeTypeCode == 2000
                  ? '#${billItem?.poiOrderPushDayseq}'
                  : '';
              return Center(
                child: Text(
                    '${billItem.outCreateTime}$poiOrderPushDayseqPC${billItem.orderSeqComment ?? ''}'),
              );
            }),
        RooTableColumn<WmPoiBillChargeDynamicVoList>(
            label: '费用明细',
            width: 350,
            cellRender: (WmPoiBillChargeDynamicVoList billItem) {
              List<WmPoiBillChargeFeeDynamicVoList> dynamicVoList =
                  billItem?.wmPoiBillChargeFeeDynamicVoList ?? [];
              if (dynamicVoList.length > 0) {
                return Center(
                  child: Wrap(
                    children: _buildDynamicList(dynamicVoList),
                    spacing: 14,
                    runSpacing: 16,
                  ),
                );
              } else {
                return SizedBox.shrink();
              }
            }),
        RooTableColumn<WmPoiBillChargeDynamicVoList>(
          label: '结算金额(元)',
          cellRender: (WmPoiBillChargeDynamicVoList billItem) {
            // 入账金额是否大于0
            bool isChargeAmountOver0 = billItem.chargeAmount > 0;
            // 小于0则显示红色
            Color amountColor =
                isChargeAmountOver0 ? Color(0xFF222222) : Color(0xFFFF1D31);
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    billItem?.settleStatus ?? '',
                    style: TextStyle(
                      fontSize: 12,
                    ),
                  ),
                  SizedBox(
                    height: 8,
                  ),
                  Text(
                    MoneyTool.formatMoneyWithPrex(billItem?.chargeAmount ?? 0),
                    style: TextStyle(color: amountColor),
                  ),
                ],
              ),
            );
          },
        ),
        RooTableColumn<WmPoiBillChargeDynamicVoList>(
          label: '结算日期',
          cellRender: (WmPoiBillChargeDynamicVoList billItem) {
            String dailyBillDate = DateFormat.formatYYYYMMDD(
                billItem?.dailyBillDateTimestamp ?? 0,
                splitStr: "-");
            return GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                Map<String, dynamic> params = {
                  "dailyBillDate": dailyBillDate,
                };
                if (billItem.settleStatusCode == 2) {
                  RouterTools.flutterPageUrl(context, '/progessOrder', params: {
                    "dailyBillDate": DateFormat.formatYYYYMMDD(
                        billItem?.dailyBillDateTimestamp)
                  });
                } else {
                  RouterTools.flutterPageUrl(context, '/dailyBills',
                      params: params);
                }
              },
              child: Center(
                child: Text(
                  dailyBillDate,
                  style: TextStyle(
                    decoration: TextDecoration.underline,
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: Color(0xFFFF6A00),
                  ),
                ),
              ),
            );
          },
        ),
        RooTableColumn<WmPoiBillChargeDynamicVoList>(
          label: '操作',
          cellRender: (WmPoiBillChargeDynamicVoList billItem) {
            bool noDetail = billItem.appDetailType == -1;
            return GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                _handleJumpDetail(billItem);
              },
              child: Container(
                alignment: Alignment.center,
                padding: EdgeInsets.symmetric(
                  horizontal: 15,
                ),
                child: Text(
                  noDetail ? '' : '详情',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: Color(0xFFFF6A00),
                  ),
                ),
              ),
            );
          },
        )
      ],
    );
  }

  _buildPCTableList() {
    return Container(
      width: double.infinity,
      child: SingleChildScrollView(
        child: _buildTable(),
      ),
    );
  }

  _buildDynamicList(List<WmPoiBillChargeFeeDynamicVoList> dynamicVoList) {
    List<Widget> list = [];
    dynamicVoList
        .asMap()
        .forEach((int index, WmPoiBillChargeFeeDynamicVoList item) {
      List<Widget> itemArr = [
        Text(
          '${item.billFeeTypeName ?? ""}',
          style: TextStyle(
            fontSize: isPC ? 12 : 14,
            color: isPC ? Color(0xFF999999) : Color(0xFF666666),
          ),
        ),
        SizedBox(
          height: 2,
        ),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Text('${item.direction ?? ""}￥'),
            Text(
              MoneyTool.formatMoney(item.feeAmount),
            ),
          ],
        ),
      ];
      if (isPC) {
        list.add(Column(
          children: itemArr,
        ));
      } else {
        list.add(
          Row(
            children: itemArr,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
          ),
        );
        list.add(SizedBox(height: index == dynamicVoList.length - 1 ? 12 : 16));
      }
    });
    return list;
  }

  void _handleJumpDetail(WmPoiBillChargeDynamicVoList billItem) {
    // billItem.appDetailType 为 -1 时表示没有详情操作
    // 点击时不跳转 没有右侧箭头
    int billItemAppDetailType = billItem?.appDetailType ?? -1;
    if (billItemAppDetailType == -1) {
      return;
    }
    // 跳转后端下发的外部链接
    if (billItemAppDetailType == 2) {
      if (billItem.specialType == 1) {
        if (billItem.isKa != 0) {
          Loading.showToast(message: '活动由总部提报，查询明细请联系总部');
        } else {
          // 跳转
          String path = isPC ? billItem.webUrl : billItem.appUrl;
          RouterTools.openWebPageUrl(path ?? '');
        }
      } else if (isPC && (billItem?.webUrl ?? '').length > 0) {
        RouterTools.openWebPageUrl(billItem.webUrl);
      } else if (!isPC && (billItem?.appUrl ?? '').length > 0) {
        RouterTools.openWebPageUrl(billItem.appUrl);
      }
      return;
    }
    Map<String, dynamic> orderParams = {
      "chargeTypeCode": billItem.chargeTypeCode,
      "billChargeId": billItem.billChargeId,
      "wmOrderViewId": billItem.wmOrderViewId,
      "dailyBillDate":
          DateFormat.formatYYYYMMDD(billItem.dailyBillDateTimestamp),
      "keys": '1',
      "cid": cid,
      "pageKeyInfo": pageKeyInfo
    };
    Modal.openDialogPCAndPageApp(
      context,
      '/orderDetail',
      OrderDetailPage(
        params: orderParams,
      ),
      params: orderParams,
      title: '交易详情',
      width: 550,
      height: 650,
      backgroundColor: Color(0xFFF5F6FA),
    );

    ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_58c8c9wy_mc');
  }

  numText(WmPoiBillChargeDynamicVoList billItem) {
    if (widget.params != null &&
        widget.params['status'] != null &&
        widget.params['poiOrderPushDayseq'] > 0) {
      return '# ${widget.params['poiOrderPushDayseq']}  ${billItem?.chargeTypeName != '' ? billItem?.chargeTypeName : billItem?.billChargeTypeName}';
    }
    if (billItem.poiOrderPushDayseq > 0) {
      return '# ${billItem.poiOrderPushDayseq} ${billItem?.chargeTypeName != '' ? billItem?.chargeTypeName : billItem?.billChargeTypeName}';
    }
    return billItem?.chargeTypeName != ''
        ? billItem?.chargeTypeName
        : billItem?.billChargeTypeName;
  }

  List<Widget> _buildChargeList() {
    ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_g9g0bk81_mv');
    List<Widget> list = [];
    billList?.forEach((billItem) {
      // 是否为进行中订单
      bool isUnfinishOrder = billItem?.settleStatus == '未入账';
      // 入账金额是否大于0
      bool isChargeAmountOver0 = billItem.chargeAmount > 0;
      // 小于0则显示红色
      Color amountColor =
          isChargeAmountOver0 ? Color(0xFF222222) : Color(0xFFFF1D31);
      List<WmPoiBillChargeFeeDynamicVoList> dynamicVoList =
          billItem?.wmPoiBillChargeFeeDynamicVoList ?? [];
      list.add(RadiusCard(
        margin: EdgeInsets.only(bottom: 12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_g9g0bk81_mc');
                  _handleJumpDetail(billItem);
                },
                child: Container(
                    decoration: BoxDecoration(
                      border: Border(
                        bottom:
                            BorderSide(color: Color(0xFFE2E2E2), width: 0.5),
                      ),
                    ),
                    child: Container(
                        margin: EdgeInsets.only(bottom: 12, top: 2),
                        child: Flex(
                          direction: Axis.horizontal,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            Expanded(
                              child: Text(

                                  /// 暂时隐藏 无法兼容
                                  // billItem?.billChargeTypeName ?? '',
                                  billItem?.chargeTypeName != ''
                                      ? billItem?.chargeTypeName
                                      : billItem?.billChargeTypeName,
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16.0,
                                  )),
                            ),
                            Align(
                              child: billItem.appDetailType == -1
                                  ? SizedBox()
                                  : Row(children: <Widget>[
                                      Text(
                                        // '${billItem?.wmOrderViewId}',
                                        '查看详情',
                                        style: TextStyle(
                                            // backgroundColor: Color(0xFFFFE699)
                                            fontWeight: FontWeight.w600,
                                            fontSize: 14,
                                            color: Color(0xFF222222)),
                                      ),
                                      ArrowIcon(color: ArrowIconColorEnum.grey)
                                    ]),
                            )
                          ],
                        )))),
            SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Align(
                    child: Text('交易时间',
                        style: TextStyle(color: Color(0xFF666666)))),
                Align(
                    child: Row(children: <Widget>[
                  Text(
                      '${DateFormat.formatYYYYMMDD(billItem?.dailyBillDateTimestamp ?? 0)}'),
                  // billItem.chargeTypeCode == 2000
                  //     ? Text(' #${billItem?.poiOrderPushDayseq ?? ""}')
                  //     : SizedBox.shrink(),
                ]))
              ],
            ),
            SizedBox(height: 16),
            dynamicVoList != null && dynamicVoList.length > 0
                ? Column(children: _buildDynamicList(dynamicVoList))
                : SizedBox.shrink(),
            Container(
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE2E2E2), width: 0.5),
                ),
              ),
              child: Container(
                margin: EdgeInsets.only(top: 12),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Text(billItem?.settleStatus ?? '',
                        style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: isUnfinishOrder
                                ? Color(0xFF999999)
                                : Color(0xFF222222))),
                    Align(
                        child: Row(children: <Widget>[
                      Text(
                        '${isChargeAmountOver0 ? "" : "-"}￥',
                        style: TextStyle(
                            fontSize: 12,
                            color: isUnfinishOrder
                                ? Color(0xFF999999)
                                : amountColor),
                      ),
                      Text(
                        MoneyTool.formatMoney(isChargeAmountOver0
                            ? billItem.chargeAmount
                            : -billItem.chargeAmount),
                        style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: isUnfinishOrder
                                ? Color(0xFF999999)
                                : amountColor),
                      )
                    ]))
                  ],
                ),
              ),
            ),
            SizedBox(height: 16),
            GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  if (billItem.settleStatusCode == 2) {
                    return;
                    //   RouterTools.flutterPageUrl(context, '/progessOrder',
                    //       params: {
                    //         "dailyBillDate": DateFormat.formatYYYYMMDD(
                    //             billItem?.dailyBillDateTimestamp)
                    //       });
                  } else {
                    RouterTools.flutterPageUrl(context, '/dailyBills', params: {
                      "dailyBillDate": DateFormat.formatYYYYMMDD(
                          billItem?.dailyBillDateTimestamp)
                    });
                  }
                  ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_voct7kcy_mc');
                },
                child: Container(
                    child: billItem.settleStatusCode == 2
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: <Widget>[
                              Align(
                                  child: Text('进行中订单',
                                      style: TextStyle(
                                        fontWeight: FontWeight.w400,
                                        fontSize: 14,
                                        color: Color(0xFF999999),
                                      ))),
                              // Align(child: Row(children: <Widget>[ArrowIcon()]))
                            ],
                          )
                        : Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: <Widget>[
                              Align(
                                  child: Text(
                                      '所属账单${DateFormat.formatYYYYMMDD(billItem?.dailyBillDateTimestamp ?? 0)}',
                                      style: TextStyle(
                                        fontWeight: FontWeight.w400,
                                        fontSize: 14,
                                        color: Color(0xFF999999),
                                      ))),
                              Align(child: Row(children: <Widget>[ArrowIcon()]))
                            ],
                          ))),
          ],
        ),
      ));
    });
    return list;
  }

  Widget _buildOrderInfo() {
    bool isOrderSearchMessage = orderViewIds != null &&
        widget.params != null &&
        widget.params['status'] != null;
    return Expanded(
      child: Container(
          color: Color(0xFFF5F6FA),
          margin: EdgeInsets.only(top: isOrderSearchMessage ? 10 : 0),
          padding: EdgeInsets.symmetric(
            horizontal: 12.0,
          ),
          child: (billList != null &&
                  billList.isEmpty &&
                  isCompleteBeforeCancelOrder != null &&
                  isCompleteBeforeCancelOrder)
              ? OrderEmpty(title: '该订单完单前被取消，无订单结算数据')
              : SingleChildScrollView(
                  child: Column(
                  children: [
                    SizedBox(
                      height: isOrderSearchMessage ? 0 : 12,
                    ),
                    isCompleteBeforeCancelOrder != null &&
                            isCompleteBeforeCancelOrder
                        ? Container(
                            margin: EdgeInsets.only(bottom: 12),
                            child: Text('该订单完单前被取消，无订单结算数据',
                                style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontSize: 16,
                                    color: Color(0xFF91949E))),
                          )
                        : SizedBox.shrink(),
                    isOrderSearchMessage
                        ? SizedBox.shrink()
                        : Row(
                            children: [
                              Text(
                                orderViewIds != null
                                    ? '查询订单${orderViewIds}结果'
                                    : '',
                                style: TextStyle(
                                    fontWeight: FontWeight.w400,
                                    fontSize: 14,
                                    color: Color(0xFF999999)),
                              )
                            ],
                          ),
                    SizedBox(
                      height: isOrderSearchMessage ? 0 : 12,
                    ),
                    Column(
                      children: _buildChargeList(),
                    ),
                  ],
                ))
          // : Empty(title: '暂无内容'),
          ),
    );
  }

  Widget _buildOrderPCInfo() {
    return Expanded(
        child: Container(
      margin: EdgeInsets.only(
        top: 20,
      ),
      padding: EdgeInsets.fromLTRB(30, 30, 30, 40),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8))),
      child: Column(children: [
        poiInfo == null
            ? SizedBox.shrink()
            : Container(
                alignment: Alignment.centerLeft,
                padding: EdgeInsets.only(
                  bottom: 24,
                ),
                child: Text(
                  poiInfo ?? '',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
                ),
              ),
        Expanded(child: _buildPCTableList())
      ]),
    ));
  }

  @override
  Widget build(BuildContext context) {
    Widget searchbar = SearchBar(
      onchangeValue: (String orderViewId) {
        ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_uqwlqync_mc');
        _fetchData(orderViewId);
      },
    );
    return Scaffold(
      appBar: UITools.renderNavbar(
          context: context,
          title: widget.params != null && widget.params['status'] != null
              ? '# ${widget.params['poiOrderPushDayseq']} 外卖订单'
              : '订单查询'),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          widget.params != null && widget.params['status'] != null
              ? SizedBox.shrink()
              : searchbar,
          SizedBox(height: isPC ? 2 : 0),
          isPC ? _buildOrderPCInfo() : _buildOrderInfo(),
        ],
      ),
    );
  }
}
