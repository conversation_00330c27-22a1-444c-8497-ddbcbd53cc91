import 'dart:async';

import 'package:flutter/material.dart';
// import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:mt_flutter_web_utils/mt_flutter_web_utils.dart';
import 'package:toast_widget_flutter/toast_widget_flutter.dart';
// import 'package:waimai_e_fe_ffw_utils/platform_tool.dart';
import 'package:waimai_e_fe_flutter_finance/src/common/flap.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/newButton.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/balanceWithdraw.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/balanceWithdraw.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/uiTools.dart';
import 'package:wef_network/wef_request.dart';

/// 手机验证码页面

@Flap('finance')
class PhoneVerifyPage extends StatelessWidget {
  const PhoneVerifyPage({this.params});
  final Map<dynamic, dynamic> params;

  @override
  Widget build(BuildContext context) {
    return LoadingPage(
      child: PhoneVerify(
        params: params,
      ),
    );
  }
}

class PhoneVerify extends StatefulWidget {
  const PhoneVerify({this.params});

  final Map<dynamic, dynamic> params;

  @override
  State<PhoneVerify> createState() => _PhoneVerifyPageState();
}

class _PhoneVerifyPageState extends State<PhoneVerify> {
  final bool isPC = PlatformTools.isPC;
  Timer timer = null;
  final Color disabledBgColor = Color(0xFFEEEEEE);
  final Color disabledTextColor = Color(0xFF999999);
  TextEditingController selectionController = TextEditingController();

  /// 提现按钮是否可点击
  bool confirmActive = false;

  /// 验证码是否可点击
  bool verifyActive = true;

  /// 验证码内容
  String verifyCode = '';

  /// 验证码发送倒计时时间
  int leftTime = 0;

  /// 是否是首次获取验证码
  bool isFirstClick = true;

  /// 验证码发送中的状态
  bool pending = false;

  /// 提现接口请求状态
  bool withdrawing = false;

  String hintText = '请输入验证码';

  final InputBorder inputBorder = OutlineInputBorder(
    borderSide: BorderSide(
      color: Color(0xFFCCCCCC),
      width: 1.0,
      style: BorderStyle.solid,
    ),
    borderRadius: BorderRadius.circular(2),
  );
  final InputBorder focusBorder = OutlineInputBorder(
    borderSide: BorderSide(
      color: Color(0xFF999999),
      width: 1.0,
      style: BorderStyle.solid,
    ),
    borderRadius: BorderRadius.circular(2),
  );

  @override
  void initState() {
    super.initState();
    selectionController.addListener(() {
      if ((selectionController?.text ?? '').length > 0) {
        confirmActive = true;
      } else {
        confirmActive = false;
      }
      this.setData();
    });
  }

  @override
  void dispose() {
    super.dispose();
    selectionController?.dispose();
    timer?.cancel();
  }

  /// 确认提现按钮
  void handleConfirm() {
    if (!confirmActive || !mounted) {
      return;
    }
    if (withdrawing) {
      Loading.showToast(message: '有提现中的任务，禁止再次提交提现任务');
      return;
    }
    withdrawing = true;
    // this.setData();
    // 获取验证码内容
    String msg = selectionController.text ?? '';
    confirmWidthdraw({
      "validCode": msg,
      'acctType': widget?.params['acctType'] ?? '',
      'phone': widget?.params['phone'] ?? '',
      'withdrawAmount': widget?.params['withdrawAmount'] ?? '',
    })
        .then((ResponseData res) {
          if ((res?.code ?? 1) == 0) {
            WithdrawConfirmModal data =
                WithdrawConfirmModal.fromJson(res?.data);

            // 跳转新的交易详情页面
            RouterTools.flutterPageUrl(context, '/exportDetail', params: {
              'outId': data.outId,
              'from': 'flutter', // flutter内部路由跳转
            }, callback: (value) {
              if (mounted) {
                /// 先关闭验证码页面，再跳走
                Navigator.pop(context);
              }
            });
            // PlatformTools.isPC
            //     ? MTFlutterWebUtils.bridgeJump(
            //         '/finance/web/exportDetail?outId=${data.outId}')
            //     : RouteUtils.open(
            //         'https://waimaieapp.meituan.com/finance/fe/exportDetail?outId=${data.outId}');
          } else {
            Loading.showDialogToast(context,
                mounted: mounted, message: res?.msg);
          }
        })
        .catchError((err) {})
        .whenComplete(() {
          withdrawing = false;
          // this.setData();
        });
  }

  void setData() {
    if (mounted) {
      this.setState(() {});
    }
  }

  void handleVerify() {
    if (leftTime > 0 || pending == true) {
      return;
    }
    pending = true;
    verifyActive = false;

    this.setData();
    sendMsg({
      'phone': widget?.params['phone'] ?? '',
    }).then((ResponseData res) {
      if ((res?.code ?? 1) == 0) {
        isFirstClick = false;
        pending = false;
        leftTime = 60;
        this.setData();
        startTime();
      } else {
        pending = false;
        verifyActive = true;
        this.setData();
        Loading.showDialogToast(context, message: res?.msg, mounted: mounted);
      }
    }).catchError((e) => {});
  }

  /// 发送验证码后的计数器
  void startTime() {
    timer = Timer.periodic(const Duration(milliseconds: 1000), (t) {
      if (leftTime <= 0) {
        t.cancel();
        timer = null;
        verifyActive = true;
        this.setData();
        return;
      }
      leftTime--;
      this.setData();
    });
  }

  String buttonText() {
    if (pending) {
      return '发送中';
    }
    if (!pending && leftTime > 0) {
      return '已发送 ${leftTime}s';
    }
    return isFirstClick ? '获取验证码' : '重新发送';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: UITools.renderNavbar(
        context: context,
        title: '手机验证',
      ),
      backgroundColor: Colors.white,
      body: Padding(
        child: Column(
          children: [
            Container(
              alignment: Alignment.centerLeft,
              child: Text(
                '为了账户安全，我们将发送验证码至手机${widget?.params['phone'] ?? ''}，如需更换绑定手机，请联系业务经理',
                style: TextStyle(
                  color: Color(0xFF222222),
                  fontSize: 14,
                  height: 1.5,
                ),
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: 18,
                vertical: 26,
              ),
              margin: EdgeInsets.symmetric(
                vertical: 20,
              ),
              decoration: BoxDecoration(
                color: Color(0xFFF5F6FA),
                borderRadius: BorderRadius.all(
                  Radius.circular(
                    13,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Expanded(
                    child: Container(
                      color: Colors.white,
                      height: isPC ? 36 : 40,
                      child: Material(
                        color: Colors.white,
                        child: TextField(
                          controller: selectionController,
                          style: TextStyle(
                            color: Color(0xFF222222),
                            fontSize: 14,
                          ),
                          decoration: InputDecoration(
                            hintText: hintText,
                            hintStyle: TextStyle(
                              color: Color(0xFF999999),
                              fontSize: 14,
                              height: 1.4,
                            ),
                            enabledBorder: inputBorder,
                            focusedBorder: focusBorder,
                            contentPadding: EdgeInsets.symmetric(
                              vertical: 8,
                              horizontal: 12,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(
                      left: 8,
                    ),
                    child: ButtonNew(
                      width: 110,
                      hasColor: false,
                      disabled: !verifyActive,
                      text: buttonText(),
                      onClick: handleVerify,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              alignment: Alignment.centerRight,
              child: ButtonNew(
                width: isPC ? 96 : null,
                hasColor: true,
                disabled: !confirmActive,
                text: '确认提现',
                onClick: handleConfirm,
              ),
              padding: EdgeInsets.only(
                top: 12,
              ),
            ),
          ],
        ),
        padding: EdgeInsets.symmetric(
          horizontal: 20,
        ),
      ),
    );
  }
}
