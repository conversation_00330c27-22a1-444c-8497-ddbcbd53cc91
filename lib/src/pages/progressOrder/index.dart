import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:roo_flutter/tools/response_system.dart';
import 'package:waimai_e_fe_flutter_finance/src/common/flap.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/empty.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/month_daily_bill/widgets/daily_charge_table.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/billChargeListForOrder.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/requests.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/uiTools.dart';
import 'package:waimai_e_fe_flutter_finance/waimai_e_fe_flutter_finance.dart';

import 'model/card_model.dart';
import 'model/pc_tab_model.dart';
import 'widgets/header.dart';
import 'widgets/listCard.dart';
import 'widgets/pc_tab_cart.dart';

@Flap('finance')
class ProgessOrderPage extends StatelessWidget {
  const ProgessOrderPage({Key key, this.params}) : super(key: key);
  final Map params;

  @override
  Widget build(BuildContext context) {
    return LoadingPage(
      child: _ProgressOrderPage(
        params: params ?? {},
      ),
    );
  }
}

class _ProgressOrderPage extends StatefulWidget {
  const _ProgressOrderPage({Key key, this.params = const {}}) : super(key: key);
  final Map params;

  @override
  State<_ProgressOrderPage> createState() => _ProgressOrderPageState();
}

class _ProgressOrderPageState extends State<_ProgressOrderPage> {
  OrderCardModel orderCardModel;
  List<OrgizeListModel> orgizeListModelList = [];
  List<WmPoiBillChargeDynamicVoList> wmPoiBillChargeDynamicVoList = [];
  final ScrollController _controller = ScrollController();
  int pageNo = 1;
  bool isLoading = false;
  int get pageSize => ResponsiveSystem.bothAppPc(runApp: 20, runPc: 10);

  /// 是否展示到底文案
  bool showLoadMore = false;

  String _dailyBillDate;

  // 订单类明细头部提示内容
  List<WmPoiBillChargeTitleVoList> billChargeTitleVoList = [];
  PCTabListModel pcTabModelList;
  @override
  void initState() {
    super.initState();
    ResponsiveSystem.bothAppPc(
      runApp: () {
        getDatas(true);
      },
      runPc: () {
        getDateList();
      },
    );
  }

  getDateList() {
    comGetApi(
      path: '/finance/waimai/poiBillCharge/api/unFinishOrderSummary',
      params: {'businessLine': 1},
    ).then((value) {
      if (value?.data != null) {
        pcTabModelList = PCTabListModel.fromJson(value.data);
        pcTabModelList.list = pcTabModelList.list
            .where(
              (item) => item.count > 0,
            )
            .toList();
        setState(() {});
        if (pcTabModelList.list.length > 0) {
          _dailyBillDate = widget.params['dailyBillDate'] ??
              DateFormat.formatYYYYMMDD(
                pcTabModelList.list[0].dailyBillDateTimestamp,
              );
          getDatas(
            true,
            dailyBillDate: _dailyBillDate,
          );
        }
      }
    }).whenComplete(() {});
  }

  getDatas(bool init, {String dailyBillDate}) {
    if (isLoading == true) {
      return;
    }
    if (init == true) {
      wmPoiBillChargeDynamicVoList = [];
      showLoadMore == false;
      pageNo = 1;
    }
    isLoading = true;
    orgizeListModelList = [];
    Loading.showLoading();
    Map par = {'type': 2, 'pageNo': pageNo, 'pageSize': pageSize};
    if (dailyBillDate != null || _dailyBillDate != null) {
      par['dailyBillDate'] = dailyBillDate ?? _dailyBillDate;
    }
    comGetApi(
      path: '/finance/waimai/poiBillCharge/api/orderList',
      params: par,
    ).then((value) {
      if (value?.data != null) {
        orderCardModel = OrderCardModel.fromJson(value.data);
        ResponsiveSystem.bothAppPc(runApp: () {
          if (orderCardModel.wmPoiBillChargeDynamicVoList.isNotEmpty) {
            pageNo = pageNo + 1;
            calcData(orderCardModel);
          }
        }, runPc: () {
          wmPoiBillChargeDynamicVoList =
              orderCardModel.wmPoiBillChargeDynamicVoList;
          if (orderCardModel.wmPoiBillChargeTitleVoList != null) {
            billChargeTitleVoList = orderCardModel.wmPoiBillChargeTitleVoList;
          }
          setState(() {});
        });
      }
    }).whenComplete(() {
      isLoading = false;
      Loading.dismissLoading();
    });
  }

  calcData(OrderCardModel _orderCardModel) {
    if (_orderCardModel.wmPoiBillChargeDynamicVoList.length < pageSize) {
      showLoadMore = true;
    }
    wmPoiBillChargeDynamicVoList
        .addAll(_orderCardModel.wmPoiBillChargeDynamicVoList);
    Map<int, List<WmPoiBillChargeDynamicVoList>> timeMap = {};

    wmPoiBillChargeDynamicVoList.forEach((element) {
      DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(
          element.outCreateTimestamp * 1000);

      ///此处把时分秒时间戳换成年月日
      int _stamp = DateTime(dateTime.year, dateTime.month, dateTime.day)
          .millisecondsSinceEpoch;
      if (timeMap[_stamp] == null) {
        timeMap[_stamp] = [];
      }
      timeMap[_stamp].add(element);
    });
    timeMap.forEach((k, v) {
      orgizeListModelList.add(
        OrgizeListModel(
            orderList: v,
            timeStamp: k,
            timeString: DateFormat.forTimeString(k)),
      );
    });
    orgizeListModelList.sort((a, b) => b.timeStamp - a.timeStamp);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Color(0xFFF7F8FA),
        appBar: UITools.renderNavbar(
          context: context,
          title: '进行中订单',
        ),
        body: EasyRefresh(
          header: PlatformTool.isPC ? null : MaterialHeader(),
          footer: PlatformTool.isPC
              ? null
              : MaterialFooter(enableInfiniteLoad: false),
          onRefresh: () async {
            if (PlatformTool.isPC) {
              return null;
            }
            return getDatas(true);
          },
          onLoad: () async {
            if (PlatformTool.isPC) {
              return null;
            }
            return getDatas(false);
          },
          child: ResponsiveSystem(
            app: build4APP(),
            pc: build4PC(),
          ),
        ));
  }

  Widget build4PC() {
    return orderCardModel == null
        ? Empty()
        : SingleChildScrollView(
            child: Container(
              width: MediaQuery.of(context).size.width,
              padding: EdgeInsets.symmetric(vertical: 6),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                children: <Widget>[
                  ProgressOrderHeader(
                      title: orderCardModel.unFinishOrderTip,
                      content: orderCardModel.unFinishOrderTipDetail,
                      statusColors: [Color(0xFFFEEA88), Color(0xFFFDD222)]),
                  pcTabModelList != null && pcTabModelList.list.length > 0
                      ? ProcessOrderPageTabCard(
                          pcTabModelList: pcTabModelList,
                          dailyBillDate: widget.params['dailyBillDate'],
                          onTimeChange: (String dailyBillDate) {
                            pageNo = 1;
                            _dailyBillDate = dailyBillDate;
                            getDatas(true, dailyBillDate: dailyBillDate);
                          })
                      : SizedBox.shrink(),
                  DailyChargeTable(
                    categoryType: 1,
                    hideSummary: true,
                    listContent: wmPoiBillChargeDynamicVoList ?? [],
                    listTitle: billChargeTitleVoList,
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                      top: 20,
                    ),
                    child: RooPagination(
                      onPageChanged: (int currentPage) async {
                        pageNo = currentPage;
                        getDatas(false);
                      },
                      currentPage: pageNo,
                      totalPage:
                          ((orderCardModel?.count ?? 0) / pageSize).ceil(),
                    ),
                  ),
                ],
              ),
            ),
          );
  }

  Widget build4APP() {
    return (orderCardModel == null || wmPoiBillChargeDynamicVoList.isEmpty)
        ? Empty()
        : SingleChildScrollView(
            controller: _controller,
            child: Padding(
              child: Column(
                children: [
                  ProgressOrderHeader(
                      title: orderCardModel.unFinishOrderTip,
                      content: orderCardModel.unFinishOrderTipDetail,
                      statusColors: [Color(0xFFFEEA88), Color(0xFFFDD222)]),
                  ...orgizeListModelList
                      .map((e) => ProgressOrderCard(orgizeListModel: e))
                      .toList(),
                  SizedBox(height: 20),
                  showLoadMore == true
                      ? Text(
                          '已经到底了',
                          style: TextStyle(
                            fontSize: 12,
                            color: Color(0xFF999999),
                            fontWeight: FontWeight.w300,
                          ),
                        )
                      : SizedBox.shrink()
                ],
              ),
              padding: EdgeInsets.all(12),
            ));
  }
}
