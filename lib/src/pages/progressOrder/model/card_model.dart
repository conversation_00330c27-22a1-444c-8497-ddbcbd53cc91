import 'package:waimai_e_fe_flutter_finance/src/services/model/billChargeListForOrder.dart';

class OrgizeListModel {
  OrgizeListModel({
    this.orderList,
    this.timeStamp,
    this.timeString,
  });
  List<WmPoiBillChargeDynamicVoList> orderList = [];
  int timeStamp;
  String timeString;
}

class OrderCardModel {
  String unFinishOrderTip;
  List<WmPoiBillChargeDynamicVoList> wmPoiBillChargeDynamicVoList = [];
  List<WmPoiBillChargeTitleVoList> wmPoiBillChargeTitleVoList;
  String unFinishOrderTipDetail;
  num count = 0;
  num unFinishCount = 0;
  OrderCardModel.fromJson(Map<String, dynamic> json) {
    unFinishOrderTip = json['unFinishOrderTip'];
    count = json['count'];
    if (json['wmPoiBillChargeDynamicVoList'] != null) {
      wmPoiBillChargeDynamicVoList = <WmPoiBillChargeDynamicVoList>[];
      json['wmPoiBillChargeDynamicVoList'].forEach((v) {
        wmPoiBillChargeDynamicVoList
            .add(WmPoiBillChargeDynamicVoList.fromJson(v));
      });
    }
    if (json['wmPoiBillChargeTitleVoList'] != null) {
      wmPoiBillChargeTitleVoList = [];
      json['wmPoiBillChargeTitleVoList'].forEach((e) {
        wmPoiBillChargeTitleVoList.add(WmPoiBillChargeTitleVoList.fromJson(e));
      });
    }
    unFinishOrderTipDetail = json['unFinishOrderTipDetail'];
    unFinishCount = json['unFinishCount'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['unFinishOrderTip'] = this.unFinishOrderTip;
    if (this.wmPoiBillChargeDynamicVoList != null) {
      data['orderList'] =
          this.wmPoiBillChargeDynamicVoList.map((v) => v.toJson()).toList();
    }
    data['unFinishOrderTipDetail'] = this.unFinishOrderTipDetail;
    data['unFinishCount'] = this.unFinishCount;
    return data;
  }
}

class OrderListItemModel {
  int billChargeId;
  String dailyBillDate;
  int dailyBillDateTimestamp;
  int wmOrderViewId;
  String billChargeTypeName;
  int chargeTypeCode;
  int chargeAmount;
  int outCreateTimestamp;
  int poiOrderPushDayseq;

  OrderListItemModel(
      {this.billChargeId,
      this.dailyBillDate,
      this.dailyBillDateTimestamp,
      this.wmOrderViewId,
      this.billChargeTypeName,
      this.chargeTypeCode,
      this.chargeAmount,
      this.outCreateTimestamp,
      this.poiOrderPushDayseq});

  OrderListItemModel.fromJson(Map<String, dynamic> json) {
    billChargeId = json['billChargeId'];
    dailyBillDate = json['dailyBillDate'];
    dailyBillDateTimestamp = json['dailyBillDateTimestamp'];
    wmOrderViewId = json['wmOrderViewId'];
    billChargeTypeName = json['billChargeTypeName'];
    chargeTypeCode = json['chargeTypeCode'];
    chargeAmount = json['chargeAmount'];
    outCreateTimestamp = json['outCreateTimestamp'];
    poiOrderPushDayseq = json['poiOrderPushDayseq'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['billChargeId'] = this.billChargeId;
    data['dailyBillDate'] = this.dailyBillDate;
    data['dailyBillDateTimestamp'] = this.dailyBillDateTimestamp;
    data['wmOrderViewId'] = this.wmOrderViewId;
    data['billChargeTypeName'] = this.billChargeTypeName;
    data['chargeTypeCode'] = this.chargeTypeCode;
    data['chargeAmount'] = this.chargeAmount;
    data['outCreateTimestamp'] = this.outCreateTimestamp;
    data['poiOrderPushDayseq'] = this.poiOrderPushDayseq;
    return data;
  }
}
