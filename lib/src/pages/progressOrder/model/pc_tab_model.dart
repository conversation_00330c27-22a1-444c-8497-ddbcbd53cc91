class PCTabListModel {
  List<PCTabModel> list = [];
  PCTabListModel.fromJson(List json) {
    if (json.length > 0) {
      json.forEach((element) {
        list.add(PCTabModel.fromJson(element));
      });
    }
  }
}

class PCTabModel {
  int dailyBillDateTimestamp;
  int count;
  bool today;

  PCTabModel.fromJson(Map<String, dynamic> json) {
    dailyBillDateTimestamp = json['dailyBillDateTimestamp'];
    count = json['count'];
    today = json['today'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['dailyBillDateTimestamp'] = this.dailyBillDateTimestamp;
    data['count'] = this.count;
    data['today'] = this.today;
    return data;
  }
}
