import 'package:flutter/material.dart';
import 'package:roo_flutter/roo_flutter.dart';

class ProgressOrderHeader extends StatefulWidget {
  const ProgressOrderHeader({
    Key key,
    this.title,
    this.content,
    this.statusColors,
  }) : super(key: key);
  final String title;
  final String content;
  final List<Color> statusColors;

  @override
  State<ProgressOrderHeader> createState() => _ProgressOrderStateHeader();
}

class _ProgressOrderStateHeader extends State<ProgressOrderHeader> {
  Widget qishouIcon() {
    return Image.network(
      'https://p0.meituan.net/ingee/40a4e65dfd5e1a057ecf33557ba21767775.png',
      width: 60,
      height: 60,
    );
  }

  Widget titleIcon() {
    return Image.network(
      'https://p1.meituan.net/tuling/244546d373d053be6399cae49d8292522462.png',
      width: 90,
      height: 25,
    );
  }

  Widget renderToolTip() {
    List<Widget> arr = [];
    List<String> l = widget.content.split('\$\$');
    l.removeAt(0);
    l.asMap().forEach((index, ele) {
      arr.add(
        Container(
          margin: EdgeInsets.only(top: index == 0 ? 0 : 12),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 10,
                height: 10,
                margin: EdgeInsets.only(right: 6, top: 2),
                decoration: BoxDecoration(
                  color: Color(0xFFFFCC33),
                  borderRadius: BorderRadius.circular(5),
                ),
              ),
              Expanded(
                child: Text(
                  ele,
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF222222),
                  ),
                ),
              )
            ],
          ),
        ),
      );
    });
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: arr,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(10, 0, 15, 0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        gradient: LinearGradient(
          colors: widget.statusColors,
          // [
          //   Color(0xFFFEEA88),
          //   Color(0xFFFDD222),
          // ],
        ),
      ),
      child: Row(
        children: [
          qishouIcon(),
          SizedBox(width: 15),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(height: 15),
                GestureDetector(
                  onTap: () {
                    showDialog(
                        context: context,
                        builder: (context) {
                          return RooDialog(
                            context,
                            titleText: widget.content.split('\$\$').first,
                            confirmText: "知道了",
                            content: Container(
                              padding: EdgeInsets.all(12),
                              child: renderToolTip(),
                              decoration: BoxDecoration(
                                color: Color(0xFFF5F6FA),
                                borderRadius: BorderRadius.circular(6.5),
                              ),
                            ),
                          );
                        });
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      titleIcon(),
                      Row(
                        children: [
                          Text(
                            '详情',
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFF222222),
                            ),
                          ),
                          Image.network(
                            'http://p0.meituan.net/scarlett/5fff794eec6c89db69999b98c9121a932083.png',
                            width: 12,
                          )
                        ],
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 5),
                Text(
                  widget.title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    height: 1.3,
                    color: Color(0xFF222222),
                  ),
                ),
                SizedBox(height: 15),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
