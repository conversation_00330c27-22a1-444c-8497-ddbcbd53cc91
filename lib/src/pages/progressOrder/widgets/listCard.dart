import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/arrow.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/progressOrder/model/card_model.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart';

class ProgressOrderCard extends StatelessWidget {
  const ProgressOrderCard({Key key, this.orgizeListModel}) : super(key: key);
  final OrgizeListModel orgizeListModel;
  Widget renderList(BuildContext context) {
    List<Widget> arr = [];
    orgizeListModel.orderList.asMap().forEach((index, ele) {
      arr.add(
        GestureDetector(
          onTap: () {
            Map<String, dynamic> orderParams = {
              "chargeTypeCode": ele.chargeTypeCode,
              "billChargeId": ele.billChargeId,
              "wmOrderViewId": ele.wmOrderViewId,
              "dailyBillDate":
                  DateFormat.formatYYYYMMDD(ele.dailyBillDateTimestamp),
            };
            RouterTools.flutterPageUrl(
              context,
              '/orderDetail',
              params: orderParams,
            );
          },
          child: Container(
            margin: EdgeInsets.only(top: index == 0 ? 0 : 24.5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('#${ele.poiOrderPushDayseq} ${ele.billChargeTypeName}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xFF222222),
                          fontWeight: FontWeight.w500,
                        )),
                    SizedBox(height: 5),
                    Text(DateFormat.forTimeStringHMS(ele.outCreateTimestamp),
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF999999),
                        )),
                  ],
                ),
                Row(
                  children: [
                    Text(
                      '¥${MoneyTool.formatMoney(ele.chargeAmount)}',
                      style: TextStyle(
                        fontSize: 16,
                        color: Color(0xFF999999),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    ArrowIcon(),
                  ],
                ),
              ],
            ),
          ),
        ),
      );
    });
    return Column(children: arr);
  }

  @override
  Widget build(BuildContext context) {
    bool today = DateFormat.getIsToday(orgizeListModel.timeStamp);
    return Container(
      margin: EdgeInsets.only(top: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Row(
            children: [
              Text(
                '${orgizeListModel.timeString} 下单',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF222222),
                ),
              ),
              today
                  ? Container(
                      padding: EdgeInsets.symmetric(vertical: 4, horizontal: 6),
                      margin: EdgeInsets.only(left: 5),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4),
                        gradient: LinearGradient(colors: [
                          Color(0xFFFFE862),
                          Color(0xFFFFCC33),
                        ]),
                      ),
                      child: Text('今日',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF222222),
                          )),
                    )
                  : SizedBox.shrink()
            ],
          ),
          Container(
            margin: EdgeInsets.only(top: 16),
            padding: EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10.5),
            ),
            child: renderList(context),
          ),
        ],
      ),
    );
  }
}
