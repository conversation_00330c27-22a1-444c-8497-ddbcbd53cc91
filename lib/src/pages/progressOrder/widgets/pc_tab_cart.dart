import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';

import '../model/pc_tab_model.dart';

class ProcessOrderPageTabCard extends StatefulWidget {
  ProcessOrderPageTabCard({
    Key key,
    this.pcTabModelList,
    this.onTimeChange,
    this.dailyBillDate,
  }) : super(key: key);
  final PCTabListModel pcTabModelList;
  final ValueChanged<String> onTimeChange;
  final String dailyBillDate;
  @override
  State<ProcessOrderPageTabCard> createState() =>
      _ProcessOrderPageTabCardState();
}

class _ProcessOrderPageTabCardState extends State<ProcessOrderPageTabCard> {
  int nowSelectIndex = 0;

  @override
  void initState() {
    super.initState();
    if (widget.dailyBillDate != null) {
      nowSelectIndex = widget.pcTabModelList.list.indexWhere((element) =>
          DateFormat.formatYYYYMMDD(element.dailyBillDateTimestamp) ==
          widget.dailyBillDate);
    }
  }

  void changeElectIndex(index) {
    nowSelectIndex = index;
    widget.onTimeChange(
      DateFormat.formatYYYYMMDD(
        widget.pcTabModelList.list[index].dailyBillDateTimestamp,
      ),
    );
    setState(() {});
  }

  Widget renderCard(int index) {
    final element = widget.pcTabModelList.list[index];
    if (element.count == 0) {
      return SizedBox.shrink();
    }
    final Widget child = Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text('${DateFormat.formatYYYYMMDD(element.dailyBillDateTimestamp)}',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            )),
        if (element.today == true)
          Container(
            padding: EdgeInsets.symmetric(vertical: 3, horizontal: 6),
            margin: EdgeInsets.only(left: 6),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              color: Color.fromRGBO(255, 255, 255, .5),
            ),
            child: Text('今日',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF222222),
                )),
          )
      ],
    );
    if (nowSelectIndex == index) {
      return GestureDetector(
        child: getNowCard(child),
        onTap: () {
          if (index != nowSelectIndex) {
            changeElectIndex(index);
          }
        },
      );
    } else {
      return Column(children: [
        GestureDetector(
          child: Container(
            height: 70,
            width: 220,
            margin: EdgeInsets.only(right: 20),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Colors.white,
            ),
            child: child,
          ),
          onTap: () {
            changeElectIndex(index);
          },
        )
      ]);
    }
  }

  getNowCard(Widget child) {
    return Container(
      height: 89,
      width: 220,
      margin: EdgeInsets.only(right: 20),
      child: child,
      padding: EdgeInsets.only(bottom: 19),
      decoration: BoxDecoration(
        image: DecorationImage(
          image: NetworkImage(
            'http://p0.meituan.net/tuling/233ec642d3fef6af588200a01f3bb2fe11539.png',
          ),
          fit: BoxFit.fill,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 20, bottom: 20),
      height: 89,
      child: ListView.builder(
        itemBuilder: (context, index) {
          return renderCard(index);
        },
        scrollDirection: Axis.horizontal,
        itemCount: widget.pcTabModelList.list.length,
      ),
    );
  }
}
