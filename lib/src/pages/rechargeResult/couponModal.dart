import 'package:flutter/material.dart';
import 'package:waimai_e_fe_ffw_utils/platform_tool.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/rechangeResult.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/gradient.dart';

class CouponWidget extends StatefulWidget {
  const CouponWidget({
    this.list,
  });

  final List<CouponDetailsModal> list;

  @override
  State<CouponWidget> createState() => _CouponWidgetState();
}

class _CouponWidgetState extends State<CouponWidget> {
  /// 当前展示券内容
  int activeIndex = 0;
  final bool isPC = PlatformTools.isPC;

  /// 是否展示推广券弹窗
  bool isShowCoupon = false;
  final Color firstColor = Color(0xFF222222);
  // 券的背景图片
  final String appBgImg =
      'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/9b2a5e5023f8a95d/coupon_bg_app.png';
  final String bgImg =
      'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/b33101ef25db85fc/recharge_res_coupon.png';
  final TextStyle firstTitleStyle = TextStyle(
      color: title1,
      fontSize: PlatformTools.isPC ? 16 : 14,
      fontWeight: FontWeight.w500);
  final TextStyle secondTitleStyle = TextStyle(
      color: Color(0xFF999999), fontSize: PlatformTools.isPC ? 14 : 12);
  final TextStyle discountStyle = TextStyle(
      color: Color(0xFFFF192D), fontSize: PlatformTools.isPC ? 28 : 18);
  final double arrowSize = 16;
  @override
  Widget build(BuildContext context) {
    List<CouponDetailsModal> detailsList = widget?.list ?? [];

    if (detailsList.length == 0) {
      return SizedBox.shrink();
    }
    int couponLen = detailsList.length;
    CouponDetailsModal activeItem = detailsList[activeIndex];

    List<Widget> appchangeBar = [];
    for (int i = 0; i < couponLen; i++) {
      int curInd = i;
      appchangeBar.add(
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          child: Padding(
            padding: EdgeInsets.all(8),
            child: Icon(
              IconData(0xe163, fontFamily: 'MaterialIcons'),
              size: 4,
              color:
                  curInd == activeIndex ? Color(0xFFFFCC33) : Color(0xFFF5F6FA),
            ),
          ),
          onTap: () {
            this.setState(() {
              activeIndex = curInd;
            });
          },
        ),
      );
    }
    return Column(mainAxisAlignment: MainAxisAlignment.center, children: [
      Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          isPC
              ? Padding(
                  padding: EdgeInsets.only(right: 2),
                  child: activeIndex - 1 >= 0
                      ? GestureDetector(
                          child: Icon(
                            IconData(0xe093,
                                fontFamily: 'MaterialIcons',
                                matchTextDirection: true),
                            size: arrowSize,
                          ),
                          onTap: () {
                            activeIndex--;
                            this.setState(() {});
                          })
                      : SizedBox(
                          width: arrowSize,
                          height: arrowSize,
                        ),
                )
              : SizedBox.shrink(),
          Container(
            width: isPC ? 335 : null,
            height: isPC ? 148 : null,
            alignment: Alignment.center,
            padding: EdgeInsets.all(14),
            decoration: BoxDecoration(
              image: DecorationImage(
                  image: NetworkImage(isPC ? bgImg : appBgImg),
                  fit: BoxFit.fill),
            ),
            child: Row(children: [
              Padding(
                padding: EdgeInsets.only(
                  right: 40,
                ),
                child: Text(
                  '${activeItem.rate}折',
                  style: discountStyle,
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    activeItem.prodName,
                    style: firstTitleStyle,
                  ),
                  Text(
                    '仅限${activeItem.prodName}使用',
                    style: secondTitleStyle,
                  ),
                  Text(
                    '最高抵${activeItem.limitAmount}元',
                    style: firstTitleStyle,
                  ),
                  Text(
                    '有效期至${activeItem.timeLimit}',
                    style: secondTitleStyle,
                  ),
                  Text(
                    '限${activeItem.timeRangeList}使用',
                    style: secondTitleStyle,
                  ),
                ],
              )
            ]),
          ),
          isPC
              ? Padding(
                  padding: EdgeInsets.only(
                    left: 2,
                  ),
                  child: activeIndex + 1 < couponLen
                      ? GestureDetector(
                          child: Icon(
                            IconData(0xe09c,
                                fontFamily: 'MaterialIcons',
                                matchTextDirection: true),
                            size: arrowSize,
                          ),
                          onTap: () {
                            activeIndex++;
                            this.setState(() {});
                          },
                        )
                      : SizedBox(
                          width: arrowSize,
                          height: arrowSize,
                        ),
                )
              : SizedBox.shrink(),
        ],
      ),
      !isPC && couponLen > 1
          ? Padding(
              padding: EdgeInsets.only(top: 4),
              child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: appchangeBar),
            )
          : SizedBox.shrink(),
      Padding(
        padding: EdgeInsets.only(
          top: 10,
        ),
        child: Text(
          '该优惠券可抵扣门店推广产生的部分费用',
          style: secondTitleStyle,
        ),
      ),
    ]);
  }
}
