import 'dart:async';

import 'package:flutter/material.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:waimai_e_fe_ffw_utils/platform_tool.dart';
import 'package:waimai_e_fe_flutter_finance/src/common/flap.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/back.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/newButton.dart';
import 'package:waimai_e_fe_flutter_finance/src/pages/rechargeResult/couponModal.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/rechargeResult.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/rechangeResult.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/uiTools.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/util/index.dart';

/// 充值结果页面
@Flap('finance')
class RechargeResultPage extends StatelessWidget {
  const RechargeResultPage({this.params});
  final Map<dynamic, dynamic> params;

  @override
  Widget build(BuildContext context) {
    return LoadingPage(
      child: RechargeResult(params: params),
    );
  }
}

class RechargeResult extends StatefulWidget {
  const RechargeResult({this.params});
  final Map<dynamic, dynamic> params;

  @override
  State<RechargeResult> createState() => _RechargeResultState();
}

class _RechargeResultState extends State<RechargeResult> {
  String wmPoiId;
  final bool isPC = PlatformTools.isPC;
  RechargeResultModal rechargeRes;

  String isSuccess;
  String outNo;
  int acctType;

  /// 但前选中券序号
  int activeIndex = 0;

  final RechargeResultInfo infoSuccess = RechargeResultInfo(
    title: '充值成功',
    tip: '充值将在24小时之内到账，请在财务对账中查看',
    imageUrl:
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/52042b7f36e252e0/recharge_success.png',
  );
  final RechargeResultInfo infoFail = RechargeResultInfo(
    title: '充值失败',
    tip: '因系统原因充值失败，请联系客服',
    imageUrl:
        'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/cdade5185f9cfbac/recharge_fail.png',
  );

  @override
  void initState() {
    super.initState();
    outNo = getParamByKey('outNo');
    isSuccess = getParamByKey('isSuccess');
    acctType = int.tryParse('${getParamByKey('acctType')}');
    print('outNo=$outNo isSuccess=$isSuccess acctType=$acctType');
    if (outNo != null && isSuccess != null && acctType != null) {
      fetchData();
    }
    Util.getPoiId().then((value) {
      wmPoiId = value;
    });
  }

  String getParamByKey(String key) {
    dynamic pValue;
    if (widget.params != null) {
      pValue = widget.params[key];
    }
    print('pValue$pValue');
    print('urlValue${pValue}');
    return pValue.toString() ?? Util.getUrlParam(key) ?? Util.getCookie(key);
  }

  fetchData() {
    Map<String, dynamic> params = {
      "outNo": outNo ?? '',
      "isSuccess": isSuccess == '1' ? true : false,
      // 充值类型
      'type': acctType,
    };
    searchRechargeResult(params).then((RechargeResultModal data) {
      this.setState(() {
        rechargeRes = data;
      });
      // 是推广账户充值，且券的数量大于0
      bool isShowCoupon = (acctType ?? 0) == 1028 && (data?.couponNum ?? 0) > 0;

      // int couponNum = (data?.couponDetails ?? []).length;
      if (isShowCoupon) {
        Timer(Duration(microseconds: 200), () {
          showDialog(
              context: context,
              builder: (context) {
                return RooDialog(
                  context,
                  titleText: "获得${data.couponNum}张门店推广抵用券",
                  cancelText: "去看看",
                  cancelCallback: () {
                    String toSeeUrl = '';
                    if (isPC) {
                      toSeeUrl = data?.hrefToCouponPC ?? '';
                    } else {
                      toSeeUrl = data?.hrefToCouponApp ?? '';
                    }
                    RouterTools.openWebPageUrl(toSeeUrl);
                  },
                  confirmText: "知道了",
                  content: Padding(
                    child: CouponWidget(
                      list: rechargeRes?.couponDetails ?? [],
                    ),
                    padding: EdgeInsets.only(
                      top: 14,
                    ),
                  ),
                );
              });
        });
      }
    });
  }

  Widget _buildResultInfo() {
    RechargeResultInfo info = infoSuccess;
    return Container(
      height: 192,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(8)),
          color: Colors.white),
      alignment: Alignment.center,
      child: Column(mainAxisSize: MainAxisSize.min, children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.network(
              info.imageUrl,
              width: 34,
              height: 34,
            ),
            Padding(
              padding: EdgeInsets.only(left: 10),
              child: Text(
                info.title,
                style: TextStyle(
                  fontSize: 18,
                  color: Color(0xFF222222),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        Padding(
          padding: EdgeInsets.only(
            top: 20,
            bottom: 30,
          ),
          child: Text(
            info.tip,
            style: TextStyle(
                color: Color(0xFF666666), fontWeight: FontWeight.w500),
          ),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ButtonNew(
              text: '查看充值记录',
              hasColor: false,
              onClick: () {
                if (isPC) {
                  RouterTools.flutterPageUrl(context, '/accountInfo');
                } else {
                  // // 余额流水页面
                  if ((acctType ?? 0) == 1008) {
                    RouterTools.flutterPageUrl(context, '/balanceFlow',
                        callback: (val) {
                      print('resultPage_pop_route_value$val');
                      if (mounted) {
                        Navigator.pop(context);
                      }
                    });
                    // Navigator.popAndPushNamed(context, '/finance/balanceFlow');
                  } else {
                    // 其他账户余额流水
                    // /finance/static/gray_html/accountFlow.html?wmPoiId=${wmPoiId!0}&acctType=${type}&token=${token}&acctId=${acctId}&pageSize=1&pageNo=20
                    RouterTools.openWebPageUrl(
                        '/finance/static/html/accountFlow.html',
                        params: {
                          "wmPoiId": wmPoiId,
                          "acctType": acctType,
                          "acctId": Util.getUrlParam('acctId') ??
                              Util.getCookie('acctId'),
                          "token": Util.getUrlParam('token') ??
                              Util.getCookie('token'),
                          "pageSize": 1,
                          "pageNo": 20,
                        });
                  }
                }
              },
            ),
            SizedBox(
              width: 8,
            ),
            ButtonNew(
              text: '返回财务对账',
              hasColor: true,
              onClick: () {
                // 跳转首页
                if (isPC) {
                  RouterTools.flutterPageUrl(context, '/home');
                } else {
                  Navigator.pop(context);
                }
              },
            ),
          ],
        ),
      ]),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: UITools.renderNavbar(
        context: context,
        title: '充值结果',
      ),
      body: Column(children: [
        isPC
            ? backPCTop(context, callback: () {
                Util.back2Steps(context);
              })
            : SizedBox.shrink(),
        _buildResultInfo(),
      ]),
    );
  }
}
