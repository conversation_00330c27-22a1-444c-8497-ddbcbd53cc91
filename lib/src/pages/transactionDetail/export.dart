import 'package:flutter/material.dart';
import 'package:toast_widget_flutter/toast_widget_flutter.dart';
import 'package:waimai_e_fe_ffw_utils/platform_tool.dart';
import 'package:waimai_e_fe_flutter_finance/src/common/flap.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/arrow.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/back.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/card.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/customWillPopScope.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/steps.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/withdrawDetail.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/withdrawDetail.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/uiTools.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/util/index.dart';

/// 交易详情页——提现详情
@Flap('finance')
class ExportDetailPage extends StatelessWidget {
  ExportDetailPage({this.params, this.pageName});
  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  Widget build(BuildContext context) {
    return LoadingPage(
      child: ExportDetailWidget(
        pageName: pageName,
        params: params,
      ),
    );
  }
}

class ExportDetailWidget extends StatefulWidget {
  ExportDetailWidget({String pageName, this.params})
      : this.pageName =
            pageName ?? (params != null ? params['mtf_page'].toString() : '');
  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  _ExportDetailState createState() => _ExportDetailState();
}

class _ExportDetailState extends State<ExportDetailWidget>
    with ExportDetailMixin {
  WithdrawDetailModel withdrawDetail;

  final bool isPC = PlatformTools.isPC;

  String wmPoiId;

  @override
  void initState() {
    super.initState();
    _fetchData();
    Util.getPoiId().then((value) {
      wmPoiId = value;
    });
    ReportLX.pv(pageKeyInfo, cid);
  }

  // 获取数据
  _fetchData() {
    Loading.showLoading();
    bool isPC = PlatformTools.isPC;
    final params = isPC
        ? {
            "outId": Util.getUrlParam('outId') ??
                (widget?.params != null ? widget?.params['outId'] : ''),
            "wmPoiId": Util.getUrlParam('wmPoiId') ?? Util.getCookie('wmPoiId')
          }
        : {
            "outId": widget?.params['outId'],
            "wmPoiId": widget?.params['wmPoiId']
          };
    fetchWithdrawDetail(params).then((WithdrawDetailModel response) {
      setState(() {
        withdrawDetail = response;
      });
      Loading.dismissLoading();
    });
  }

  // 金额部分
  Widget _buildMoney() {
    return RadiusCard(
      padding: EdgeInsets.all(24),
      margin: EdgeInsets.only(top: isPC ? 0 : 8),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment:
            isPC ? CrossAxisAlignment.start : CrossAxisAlignment.center,
        children: <Widget>[
          Text(
            withdrawDetail?.payBindType == 1 ? '提现至银行卡' : '提现至钱包',
            style: TextStyle(color: Color(0xFF666666), fontSize: 14),
          ),
          FittedBox(
              child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: <Widget>[
              Text(
                '¥',
                style: TextStyle(
                  color: Color(0xFF222222),
                  fontWeight: FontWeight.w900,
                  fontSize: 18,
                ),
              ),
              Text(
                '${MoneyTool.formatMoney(withdrawDetail?.moneyCent)}',
                style: TextStyle(
                  color: Color(0xFF222222),
                  fontWeight: FontWeight.bold,
                  fontSize: 28,
                ),
              )
            ],
          )),
        ],
      ),
    );
  }

  // 账户信息
  Widget _buildAccount() {
    String accountText = '';
    if (withdrawDetail?.payBindType == 2) {
      accountText = withdrawDetail != null && withdrawDetail.walletId > 0
          ? '商家钱包 ${withdrawDetail?.walletId ?? ""}'
          : '异常';
    } else {
      accountText =
          '${withdrawDetail?.bankName ?? ""} ${withdrawDetail?.cardNumber ?? ""}';
    }
    MainAxisAlignment mainAxisAlignment =
        isPC ? MainAxisAlignment.start : MainAxisAlignment.spaceBetween;
    return RadiusCard(
      padding: EdgeInsets.all(16),
      margin: EdgeInsets.only(top: 8),
      child: Column(
        children: <Widget>[
          Row(
            mainAxisAlignment: mainAxisAlignment,
            children: <Widget>[
              Text('汇入账号'),
              isPC ? SizedBox(width: 10) : SizedBox.shrink(),
              // payBindType == 2 汇入钱包，否则是银行卡提现
              MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  onTap: () {
                    // 去账户结算信息页面
                    RouterTools.flutterPageUrl(context, '/accountDetails',
                        params: {
                          'wmPoiId': wmPoiId,
                          'from': 'flutter',
                        });
                  },
                  child: Row(
                    children: <Widget>[
                      Container(
                        width: isPC ? 300 : 200,
                        child: Text(
                          '$accountText',
                          style: TextStyle(
                              fontSize: 14,
                              color:
                                  isPC ? Color(0xFFFF6A00) : Color(0xFF666666)),
                          textAlign: isPC ? TextAlign.left : TextAlign.right,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      isPC
                          ? SizedBox.shrink()
                          : ArrowIcon(color: ArrowIconColorEnum.grey),
                    ],
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.0),
          Row(mainAxisAlignment: mainAxisAlignment, children: <Widget>[
            Text('流水单号'),
            isPC ? SizedBox(width: 10) : SizedBox.shrink(),
            Text('${withdrawDetail?.flowNo ?? ""}')
          ]),
        ],
      ),
    );
  }

  // 处理进度
  Widget _buildProcessing() {
    List<Widget> stepListInfo = [];
    List<WithdrawFlows> flows = withdrawDetail?.withdrawFlows ?? [];
    flows.asMap().forEach((index, flowItem) {
      stepListInfo.add(
        CustomStep(withdrawDetail: withdrawDetail, index: index),
      );
    });
    return RadiusCard(
        padding: EdgeInsets.all(16),
        margin: EdgeInsets.only(top: 8),
        child: isPC
            ? Container(
                width: 935,
                height: 120,
                child: ListView.builder(
                    itemCount: 1,
                    scrollDirection: Axis.horizontal,
                    itemBuilder: (BuildContext context, int idx) {
                      return Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: stepListInfo,
                      );
                    }))
            : Column(
                children: stepListInfo,
              ));
  }

  @override
  Widget build(BuildContext context) {
    String routeFrom = widget?.params != null ? widget?.params['from'] : null;
    return CustomWillPopScope(
      child: Scaffold(
        appBar: UITools.renderNavbar(
          context: context,
          title: '交易详情',
        ),
        body: Container(
          margin: EdgeInsets.symmetric(horizontal: 12),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              isPC ? backPCTop(context) : SizedBox.shrink(),
              _buildMoney(),
              _buildAccount(),
              _buildProcessing(),
            ],
          ),
        ),
      ),
      from: routeFrom,
    );
  }
}
