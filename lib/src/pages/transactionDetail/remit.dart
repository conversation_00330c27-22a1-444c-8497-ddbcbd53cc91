import 'package:flutter/material.dart';
import 'package:toast_widget_flutter/toast_widget_flutter.dart';
import 'package:waimai_e_fe_flutter_finance/src/common/flap.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/arrow.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/card.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/remitDetail.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/remitDetail.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/routerTools.dart'
    if (dart.library.html) 'package:waimai_e_fe_flutter_finance/src/tools/routerTools4Web.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/uiTools.dart';

/// 交易详情页——账单详情
@Flap('finance')
class RemitDetailPage extends StatelessWidget {
  RemitDetailPage({this.params, this.pageName});
  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  Widget build(BuildContext context) {
    return LoadingPage(
      child: RemitDetailWidget(
        pageName: pageName,
        params: params,
      ),
    );
  }
}

class RemitDetailWidget extends StatefulWidget {
  RemitDetailWidget({String pageName, this.params})
      : this.pageName =
            pageName ?? (params != null ? params['mtf_page'].toString() : '');
  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  _RemitDetailState createState() => _RemitDetailState();
}

class _RemitDetailState extends State<RemitDetailWidget> with RemitDetailMixin {
  DetailModel billDetail;

  @override
  void initState() {
    super.initState();
    _fetchData();
    ReportLX.pv(pageKeyInfo, cid);
  }

  // 获取数据
  _fetchData() {
    Loading.showLoading();
    fetchSettleBillDetail({
      'wmPoiId': widget.params['wmPoiId'],
      "settleBillId": widget.params['settleBillId']
    }).then((DetailModel response) {
      setState(() {
        billDetail = response;
      });
      Loading.dismissLoading();
    });
  }

  // 金额部分
  Widget _buildMoney() {
    return RadiusCard(
      padding: EdgeInsets.symmetric(vertical: 24),
      margin: EdgeInsets.only(top: 8),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Text(
            '账单结算',
            style: TextStyle(color: Color(0xFF666666), fontSize: 14),
          ),
          FittedBox(
              child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: <Widget>[
              Text(
                '¥',
                style: TextStyle(
                  color: Color(0xFF222222),
                  fontWeight: FontWeight.w900,
                  fontSize: 18,
                ),
              ),
              Text(
                '${MoneyTool.formatMoney(billDetail?.billAmount)}',
                style: TextStyle(
                  color: Color(0xFF222222),
                  fontWeight: FontWeight.bold,
                  fontSize: 28,
                ),
              )
            ],
          )),
        ],
      ),
    );
  }

  // 账户信息
  Widget _buildAccount() {
    return RadiusCard(
      padding: EdgeInsets.all(16),
      margin: EdgeInsets.only(top: 8),
      child: Column(
        children: <Widget>[
          Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Align(alignment: Alignment.centerLeft, child: Text('结算周期')),
                Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      '${DateFormat.formatYYYYMMDD(billDetail?.settleBillStartDateTimestamp) ?? "--"} 至 ${DateFormat.formatYYYYMMDD(billDetail?.settleBillEndDateTimestamp) ?? "--"}',
                      style: TextStyle(fontSize: 14),
                    ))
              ]),
          SizedBox(height: 16.0),
          Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Align(alignment: Alignment.centerLeft, child: Text('创建时间')),
                Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      '${DateFormat.formatFull(billDetail?.createTimestamp) ?? ""}',
                      style: TextStyle(fontSize: 14),
                    ))
              ]),
          SizedBox(height: 16.0),
          Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Align(alignment: Alignment.centerLeft, child: Text('流水单号')),
                Align(
                    alignment: Alignment.centerRight,
                    child: Text('${billDetail?.settleBillId ?? ""}'))
              ]),
        ],
      ),
    );
  }

  // 账单详情
  Widget _buildDetailList() {
    final dailyBillList = billDetail?.dailyBills ?? [];
    final List<Widget> dailyBillWidgets = [];
    dailyBillList.forEach((item) {
      dailyBillWidgets.add(Container(
          margin: EdgeInsets.only(bottom: 18),
          child: GestureDetector(
              // 扩大点击范围，让自身整个区域都响应点击事件
              behavior: HitTestBehavior.opaque,
              onTap: () {
                ReportLX.mc(pageKeyInfo, cid, 'b_waimai_e_ri7qk9zf_mc');
                RouterTools.flutterPageUrl(context, '/dailyBills', params: {
                  "dailyBillDate":
                      DateFormat.formatYYYYMMDD(item?.dailyBillDateTimestamp)
                });
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Align(
                    child: Text(
                      '${DateFormat.formatYYYYMMDD(item?.dailyBillDateTimestamp) ?? ""}',
                      style: TextStyle(
                          color: Color(0xFF222222),
                          fontSize: 14.0,
                          fontWeight: FontWeight.w400),
                    ),
                  ),
                  Align(
                      child: Row(children: <Widget>[
                    Text(
                      '￥',
                      style: TextStyle(
                          color: Color(0xFF222222),
                          fontSize: 12.0,
                          fontWeight: FontWeight.w500),
                    ),
                    Text(
                      MoneyTool.formatMoney(item?.dailyBillAmount ?? 0),
                      style: TextStyle(
                          color: Color(0xFF222222),
                          fontSize: 15.0,
                          fontWeight: FontWeight.w500),
                    ),
                    ArrowIcon(
                      color: ArrowIconColorEnum.grey,
                    ),
                  ]))
                ],
              ))));
    });
    return Column(children: dailyBillWidgets);
  }

  Widget _buildDetail() {
    return RadiusCard(
      padding: EdgeInsets.only(left: 16, top: 16, right: 16, bottom: 0),
      margin: EdgeInsets.only(top: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            '账单详情',
            style: TextStyle(
                color: Color(0xFF222222),
                fontSize: 16,
                fontWeight: FontWeight.w600),
          ),
          SizedBox(height: 20),
          _buildDetailList(),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: UITools.renderNavbar(context: context, title: '交易详情'),
      body: Container(
          margin: EdgeInsets.symmetric(horizontal: 12),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: <Widget>[
                _buildMoney(),
                _buildAccount(),
                _buildDetail(),
              ],
            ),
          )),
    );
  }
}
