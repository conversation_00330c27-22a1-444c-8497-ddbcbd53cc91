import 'package:flutter/material.dart';
import 'package:roo_flutter/roo_flutter.dart';
import 'package:waimai_e_fe_ffw_utils/platform_tool.dart';
import 'package:waimai_e_fe_flutter_finance/src/common/flap.dart';
import 'package:waimai_e_fe_flutter_finance/src/components/billChargetListJumper.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/api/tradingType.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/billChargeListForOrder.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/dailyBill/billChargeList.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/tradingType.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/ocean.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/reportLX.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/uiTools.dart';

@Flap('finance')
class TradingTypePage extends StatelessWidget {
  const TradingTypePage({this.params, this.pageName});
  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  Widget build(BuildContext context) {
    return LoadingPage(
      child: TradingType(
        pageName: pageName,
        params: params,
      ),
    );
  }
}

class TradingType extends StatefulWidget {
  TradingType({String pageName, this.params})
      : this.pageName =
            pageName ?? (params != null ? params['mtf_page'].toString() : '');
  final Map<dynamic, dynamic> params;
  final String pageName;

  @override
  TradingTypeState createState() => TradingTypeState();
}

class TradingTypeState extends State<TradingType> with TradingTypeMixin {
  int pageNo = 1;
  int pageSize = 20;
  SelectItem currentItem = SelectItem(label: '全部', value: '-1');
  List<SelectItem> selectList = [
    SelectItem(label: '全部', value: '-1'),
  ]; // 交易类List
  List<BillChargeModel> billChargeList = []; // 交易账单List
  int billChargeTypeCode = -1; // 当前交易类型Code
  bool done = false;
  final bool isPC = PlatformTools.isPC;

  /// 决定当前UI渲染
  bool isLoading = true;

  // // 选择交易类型弹窗
  void showSelectBottomDialog() {
    billChargeList = [];
    isLoading = true;
    pageNo = 1;
    done = false;
    _fetchBillChargeList();
  }

  // 查询交易类型列表
  void _fetchOverviewDetail() {
    fetchOverviewDetail(
      {
        'startDate': DateFormat.formatYYYYMMDD(
          DateFormat.formatStringToTm(widget.params['startDate']),
        ),
        'endDate': DateFormat.formatYYYYMMDD(
          DateFormat.formatStringToTm(widget.params['endDate']),
        ),
      },
    ).then(
      (OverviewDetailModal response) {
        /// 使用空数组兜底，避免返回内容为null,flap解析报错
        List<BillChargeType> billTypeList =
            response?.billChargeTypeDetails ?? [];
        billTypeList.forEach((BillChargeType item) {
          bool isSelectedItem = item.billChargeTypeCode == billChargeTypeCode;
          if (isSelectedItem) {
            currentItem = SelectItem(
                label: item.billChargeTypeName,
                value: '${item.billChargeTypeCode}');
            billChargeTypeCode = item.billChargeTypeCode;
          }
          selectList.add(
            SelectItem(
              value: '${item.billChargeTypeCode}',
              label: item.billChargeTypeName,
            ),
          );
          this.setState(() {});
        });
      },
    );
  }

  // 查询交易列表List
  void _fetchBillChargeList() {
    Loading.showLoading();
    fetchBillChargeList({
      'startDate': widget.params['startDate'] ?? null,
      'endDate': widget.params['endDate'] ?? null,
      'billChargeTypeCode': billChargeTypeCode,
      'pageSize': pageSize,
      'pageNo': pageNo
    }).then((BillChargeListModal response) {
      /// 接口没有返回total字段，只能通过下一次请求确认
      if (response == null) {
        done = true;
      } else {
        List<BillChargeModel> currentBillChargeList =
            response?.billChargeList ?? [];
        billChargeList = billChargeList..addAll(currentBillChargeList);

        /// 作为结束请求的一个条件
        if (currentBillChargeList.length < pageSize) {
          done = true;
        }
      }
      if (done) {
        // 页面触底，埋点
        ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_x8gwpe4s_mv');
      }
    }).whenComplete(() {
      isLoading = false;
      setState(() {});
      Loading.dismissLoading();
    });
  }

  _buildOneRow(BillChargeModel item) {
    String outCreateDate = DateFormat.formatYYYYMMDD(item.outCreateTimestamp);
    const textStyle = TextStyle(
      fontSize: 14,
      color: Color(0xff222222),
      fontWeight: FontWeight.w500,
    );
    return Container(
      color: Color(0xFFFFFFFF),
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 13),
      child: Row(
        children: <Widget>[
          Expanded(
            flex: 1,
            child: Text(
              item?.poiOrderPushDayseq != null && item?.poiOrderPushDayseq != -1
                  ? '$outCreateDate#${item?.poiOrderPushDayseq}'
                  : '$outCreateDate',
              style: textStyle,
            ),
          ),
          Expanded(
            flex: 1,
            child: Column(
              children: <Widget>[
                Text(
                  '${item?.billChargeTypeName}',
                  style: textStyle,
                ),
                item?.chargeTypeName != '' && item?.chargeTypeName != null
                    ? Text(
                        '${item?.chargeTypeName}',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0XFF999999),
                          fontWeight: FontWeight.w500,
                        ),
                      )
                    : SizedBox.shrink(),
              ],
            ),
          ),
          Expanded(
            flex: 1,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: <Widget>[
                Container(
                  constraints: BoxConstraints(
                    maxWidth: (MediaQuery.of(context).size.width - 24) / 3 - 16,
                  ),
                  child: Text.rich(
                    TextSpan(
                      children: [
                        TextSpan(
                          text: '¥',
                          style: TextStyle(
                            color: Color(0xFF222222),
                            fontWeight: FontWeight.w500,
                            fontSize: 12,
                          ),
                        ),
                        TextSpan(
                          text: '${MoneyTool.formatMoney(item?.chargeAmount)}',
                          style: TextStyle(
                            color: Color(0xFF222222),
                            fontWeight: FontWeight.w500,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Container(
                  width: 12,
                  height: 12,
                  margin: EdgeInsets.only(left: 4),
                  child: Image.network(
                    'https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/pic-manager/27aa028dad636936701ddc3f46bf9c6f/arrow_right_grey.png',
                    height: 12,
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  //  订单Item
  Widget _buildOrderItem(int index) {
    BillChargeModel item = billChargeList[index];
    String dailyBillDate =
        DateFormat.formatYYYYMMDD(item.dailyBillDateTimestamp);

    WmPoiBillChargeDynamicVoList list = WmPoiBillChargeDynamicVoList(
      billChargeId: item.billChargeId,
      webDetailType: item.webDetailType,
      appDetailType: item.appDetailType,
      chargeTypeCode: item.chargeTypeCode,
      webTemplate: item.webTemplate,
      appTemplate: item.appTemplate,
      isKa: item.isKA == true ? 1 : 0,
      appUrl: item.appUrl,
      webUrl: item.webUrl,
      specialType: item.specialType,
      wmOrderViewId: int.tryParse(item.wmOrderViewId),
    );

    return BillChargeListJumper(
        bill: list, dailyBillDate: dailyBillDate, child: _buildOneRow(item));
  }

  @override
  void initState() {
    super.initState();
    if (widget.params['billChargeTypeCode'] != null) {
      setState(() {
        /// 支持aot和flap的写法
        billChargeTypeCode =
            int.tryParse('${widget.params['billChargeTypeCode']}');
      });
    }
    _fetchOverviewDetail();
    _fetchBillChargeList();
    ReportLX.pv(pageKeyInfo, cid);
  }

  @override
  Widget build(BuildContext context) {
    Widget selectorW = RooSelector(
      value: currentItem?.value ?? "",
      options: selectList,
      widthPC: 150,
      onChange: (item, index) {
        this.setState(() {
          currentItem = item;
          billChargeTypeCode = int.tryParse(item.value);
        });
        showSelectBottomDialog();
      },
    );
    ReportLX.mv(pageKeyInfo, cid, 'b_waimai_e_fdna3aie_mv');

    return Scaffold(
      appBar: UITools.renderNavbar(
        context: context,
        title: '交易类型',
      ),
      body: Column(
        mainAxisSize: isPC ? MainAxisSize.min : MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: <Widget>[
          Container(
            color: Colors.white,
            padding: EdgeInsets.symmetric(vertical: 14, horizontal: 12),
            child: selectorW,
          ),
          isLoading
              ? SizedBox.shrink()
              : billChargeList.length > 0
                  ? Expanded(
                      child: Container(
                        padding: EdgeInsets.only(bottom: 12),
                        child: ListView.builder(
                          shrinkWrap: true,
                          itemCount: billChargeList.length,
                          itemBuilder: (BuildContext context, int index) {
                            bool hasLabel = false;
                            if (index == billChargeList.length - 1) {
                              if (!done) {
                                pageNo = pageNo + 1;
                                _fetchBillChargeList();
                              }
                              hasLabel = true;
                            }
                            return Column(children: <Widget>[
                              _buildOrderItem(index),
                              hasLabel
                                  ? Container(
                                      padding:
                                          EdgeInsets.symmetric(vertical: 12),
                                      alignment: Alignment.center,
                                      child: Text(
                                        done ? '没有更多数据了' : '加载中…',
                                        style:
                                            TextStyle(color: Color(0xFF999999)),
                                      ),
                                    )
                                  : SizedBox()
                            ]);
                          },
                        ),
                      ),
                    )
                  : Container(
                      color: Color(0xFFFFFFFF),
                      padding: EdgeInsets.symmetric(vertical: 24),
                      alignment: Alignment.center,
                      child: Text('暂无交易'),
                    ),
        ],
      ),
    );
  }
}
