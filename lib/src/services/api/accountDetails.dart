import 'package:waimai_e_fe_ffw_utils/platform_tool.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/accountDetails.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/requests.dart';

/// AccountDetails
Future<AccountInfoModal> fetchAccountDetails() {
  return comGetApi(
      path: '/finance/v4/h5/api/account/accountInfo',
      params: {'source': PlatformTools.isPC ? 1 : 2}).then((response) {
    if (response?.data == null) {
      return null;
    }
    return AccountInfoModal.fromJson(response?.data);
  });
}

Future<AccountInfoModal> fetchSchoolAccountDetails(Map params) {
  return comGetApi(path: '/finance/waimai/money/channel/accountInfo', params: {
    'source': PlatformTools.isPC ? 1 : 2,
    'schoolId': params['schoolId'] ?? ''
  }).then((response) {
    if (response?.data == null) {
      return null;
    }
    return AccountInfoModal.fromJson(response?.data);
  });
}
