import 'package:waimai_e_fe_flutter_finance/src/services/model/ad_flow_details.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/requests.dart';

/// 获取智能满减活动流水记录列表
Future<AdFlowDetailModal> fetchAdFlowDetailList(Map<String, dynamic> params) {
  return comGetApi(
    path: '/finance/waimai/poiBillCharge/api/adZNMJSettleBill',
    params: params,
  ).then((response) {
    if (response?.data == null) {
      return null;
    }
    return AdFlowDetailModal.fromJson(response.data);
  });
}
