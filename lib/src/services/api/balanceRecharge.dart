import 'package:waimai_e_fe_flutter_finance/src/services/model/balanceRecharge.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/requests.dart';
import 'package:wef_network/wef_request.dart';

/// BalanceRecharge
Future<RechargeInfoModal> fetchRechargeInfo(Map params) {
  return comGetApi(
    path: '/finance/waimai/recharge/api/rechargeInfo',
    params: params,
  ).then((ResponseData response) {
    if (response?.data == null) {
      return null;
    }
    return RechargeInfoModal.fromJson(response?.data);
  });
}

Future<RechargePreOrderModal> fetchRechargePreOrder(Map params) {
  return comPostApi(
    path: '/finance/waimai/recharge/api/genRechargePreOrder',
    params: params,
  ).then((ResponseData response) {
    if (response?.data == null) {
      return null;
    }
    return RechargePreOrderModal.fromJson(response.data);
  }).catchError((onError) {
    print('onerror $onError');
  });
}

Future<RechargePreAdOrderModal> fetchAdRechargePreOrder(Map params) {
  return comGetApi(
    path: '/finance/pc/api/adMain/genPreRechargeOrder',
    params: params,
  ).then((ResponseData response) {
    if (response?.data == null) {
      return null;
    }
    return RechargePreAdOrderModal.fromJson(response.data);
  }).catchError((onError) {
    print('onerror $onError');
  });
}
