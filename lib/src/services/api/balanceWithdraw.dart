import 'package:waimai_e_fe_flutter_finance/src/services/model/balanceWithdraw.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/requests.dart';
import 'package:wef_network/wef_request.dart';

Future<WithdrawInfoModal> fetchWithdrawInfo(Map params) {
  return comGetApi(
    path: '/finance/v4/h5/api/withdraw/withdrawInfo',
    params: params,
  ).then((ResponseData response) {
    if (response?.data == null) {
      return null;
    }
    return WithdrawInfoModal.fromJson(response?.data);
  });
}

/// 发送验证码接口
Future<ResponseData> sendMsg(Map params) {
  return comPostApi(
          path: '/finance/waimai/recharge/api/sendmsg',
          params: params,
          isControlShowToast: true)
      .then((ResponseData response) {
    return response;
    // print('response${response.code}');
    // return response?.code == 0;
  });
}

/// 确认提现
Future<ResponseData> confirmWidthdraw(Map params) {
  return comPostApi(
          path: '/finance/waimai/recharge/api/withdrawConfirm',
          params: params,
          isControlShowToast: true)
      .then((ResponseData response) {
    return response;
    // if (response?.data == null) {
    //   return null;
    // }
    // return WithdrawConfirmModal.fromJson(response?.data);
  });
}
