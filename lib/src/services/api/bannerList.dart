import 'package:waimai_e_fe_flutter_finance/src/services/model/bannerItemModel.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/requests.dart';

/// banner
Future<List<BannerItemModel>> fetchBannerList() {
  return comGetApi(path: '/frontPage/bannerList').then((response) {
    List<BannerItemModel> list = [];
    response?.data?.forEach((dataItem) {
      BannerItemModel item = BannerItemModel.fromJson(dataItem);
      list.add(item);
    });
    return list;
  });
}
