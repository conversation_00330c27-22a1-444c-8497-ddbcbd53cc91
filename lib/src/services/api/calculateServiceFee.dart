import 'package:waimai_e_fe_flutter_finance/src/services/model/calculateServiceFee.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/requests.dart';

/// AccountInfo
Future<CalculateServiceFeeModel> fetchServiceFee(Map params) {
  return comGetApi(
    path: '/estimate/feeCalTool/calculateServiceFee',
    params: params,
  ).then((response) {
    if (response?.data == null) {
      return null;
    }
    return CalculateServiceFeeModel.fromJson(response?.data);
  });
}
