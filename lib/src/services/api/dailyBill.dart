import 'package:waimai_e_fe_flutter_finance/src/services/model/billChargeListForOrder.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/monthBill/monthOverview.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/requests.dart';

Future<MonthOverviewModel> fetchDailyOverview(Map params) {
  return comGetApi(
    path: '/finance/waimai/dailyBill/api/overviewDetail',
    params: params,
  ).then((response) {
    if (response?.data == null) {
      return null;
    }
    return MonthOverviewModel.fromJson(response?.data);
  });
}

Future<MonthOverviewModel> fetchOrderSum(Map params) {
  return comGetApi(
    path: '/finance/waimai/poiBillCharge/api/billChargeSummaryDetailsForWeb',
    params: params,
  ).then((response) {
    if (response?.data == null) {
      return null;
    }
    return MonthOverviewModel.fromJson(response?.data);
  });
}

/// 日账单列表接口
Future<BillChargeListForOrderModel> fetchBillChargeListByDate(Map params) {
  return comGetApi(
    path: '/finance/waimai/poiBillCharge/api/v2/billChargeListForWeb',
    params: params,
  ).then((response) {
    if (response?.data == null) {
      return null;
    }
    return BillChargeListForOrderModel.fromJson(response?.data);
  });
}
