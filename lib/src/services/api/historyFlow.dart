import 'package:waimai_e_fe_flutter_finance/src/services/model/historyFlows.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/requests.dart';

/// BalanceRecharge
Future<List<HistoryFlowsModel>> fetchHistoryFlows(Map params) {
  return comGetApi(
          path: '/finance/v4/h5/api/balance/historyFlows', params: params)
      .then((response) {
    List<HistoryFlowsModel> list = [];
    response?.data?.forEach((dataItem) {
      HistoryFlowsModel item = HistoryFlowsModel.fromJson(dataItem);
      list.add(item);
    });
    return list;
  });
}

/// 查询所有账户类型的流水信息
Future<AccountFlowsList> fetchAccountFlows(Map params) {
  return comGetApi(
          path: '/finance/waimai/accountFlow/api/poiAccountFlows',
          params: params)
      .then((response) {
    if (response != null && response.data != null) {
      return AccountFlowsList.fromJson(response.data);
    } else {
      return null;
    }
  });
}

/// 查询所有账户类型的流水信息
Future<AccountFlowsList> fetchSchoolAccountFlows(Map params) {
  return comGetApi(
          path: '/finance/waimai/money/channel/poiAccountFlowsByType',
          params: params)
      .then((response) {
    if (response != null && response.data != null) {
      return AccountFlowsList.fromJson(response.data);
    } else {
      return null;
    }
  });
}
