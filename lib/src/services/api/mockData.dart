import 'package:waimai_e_fe_flutter_finance/src/services/model/mockModel.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/requests.dart';

Future<List<MockModel>> fetchItemList() {
  return comGetApi(path: '/select/dialog/mock').then((response) {
    List<MockModel> list = [];
    response?.data?.forEach((dataItem) {
      MockModel item = MockModel.fromJson(dataItem);
      list.add(item);
    });
    return list;
  });
}
