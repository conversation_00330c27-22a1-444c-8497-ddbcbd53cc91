import 'package:waimai_e_fe_flutter_finance/src/services/model/dailyBill/billChargeList.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/monthBill/monthOverview.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/monthBill/monthsIncomeOverview.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/requests.dart';

Future<MonthOverviewModel> fetchMonthOverview(Map params) {
  return comGetApi(
          path: '/finance/v4/h5/api/statistics/monthsOverview', params: params)
      .then((response) {
    return MonthOverviewModel.fromJson(response?.data ?? {});
  });
}

// 查询交易明细
Future<List<BillChargeModel>> fetchBillChargeListForDaily(Map params) {
  return comGetApi(
          path: '/finance/v4/h5/api/billCharge/billChargeList', params: params)
      .then((response) {
    if (response?.data == null) {
      return [];
    }
    List<BillChargeModel> billChMolist = [];
    if (response?.data != null) {
      List dataList = response?.data ?? [];
      for (int i = 0; i < dataList.length; i++) {
        BillChargeModel hb = BillChargeModel.fromJson(dataList[i]);
        billChMolist.add(hb);
      }
    }
    return billChMolist;
  });
}

// 查询一个月每天的收入
Future<List<int>> fetchIncomeList(Map params) {
  return comGetApi(
    path: '/finance/v4/h5/api/statistics/incomeList',
    // path: '/finance/v3/h5/api/dailyBill/incomeList',
    params: params,
  ).then((response) {
    List<int> list = [];
    if (response?.data != null) {
      List res = response.data;
      res.forEach((e) {
        list.add(e);
      });
    }
    return list;
  });
}

// 查询近四个月收入数据
Future fetch4MonthsIncome(Map params) {
  return comGetApi(
    path: '/finance/v4/h5/api/statistics/monthsIncomeOverview',
    params: params,
  ).then((response) {
    List<MonthsIncomeOverviewModel> list = [];
    if (response?.data != null) {
      List<dynamic> dataList = response?.data ?? [];
      for (int i = 0; i < dataList.length; i++) {
        dynamic data = dataList[i];
        MonthsIncomeOverviewModel hb = MonthsIncomeOverviewModel.fromJson(data);
        list.add(hb);
      }
    }
    return list;
  });
}

Future<bool> fetchMultiPoiGray() {
  return comGetApi(
          path: '/finance/v4/h5/api/statistics/multiPoiGray',
          isControlShowToast: true)
      .then((response) {
    return response?.data ?? false;
  });
}
