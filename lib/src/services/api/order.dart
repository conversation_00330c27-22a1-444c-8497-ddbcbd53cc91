import 'package:waimai_e_fe_flutter_finance/src/services/model/order/billChargeDetailDynamic.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/requests.dart';

/// 新费率
Future<BillChargeDetailDynamicModel> fetchNewBillCharge(Map params) {
  return comGetApi(
    path: '/finance/v4/h5/api/billCharge/billChargeDetailDynamic',
    params: params,
  ).then((response) {
    if (response?.data == null) {
      return null;
    }
    return BillChargeDetailDynamicModel.fromJson(response?.data);
  });
}

/// 先富的订单详情
Future<BillChargeDetailDynamicModel> fetchXainfuBillCharge(Map params) {
  return comGetApi(
    path: '/xianfuBill/poiBillCharge/billChargeDetailDynamic',
    params: params,
    useCommonParam: false,
  ).then((response) {
    if (response?.data == null) {
      return null;
    }
    return BillChargeDetailDynamicModel.from<PERSON>son(response?.data);
  });
}
