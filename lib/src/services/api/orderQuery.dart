import 'package:waimai_e_fe_flutter_finance/src/services/model/billChargeListForOrder.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/requests.dart';

/// 新费率
Future<BillChargeListForOrderModel> fetchBillChargeListByOrderId(Map params) {
  return comGetApi(
    path: '/finance/v4/h5/api/billCharge/billChargeListForOrder',
    params: params,
  ).then((response) {
    if (response?.data == null) {
      return null;
    }
    return BillChargeListForOrderModel.fromJson(response?.data);
  });
}
