import 'package:waimai_e_fe_flutter_finance/src/services/model/rechangeResult.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/requests.dart';

/// 查询充值结果页面接口
Future<RechargeResultModal> searchRechargeResult(Map params) {
  return comGetApi(
          path: '/finance/MH5/recharge/v1/payRechargeResult', params: params)
      .then((response) {
    if (response?.data == null) {
      return null;
    }
    return RechargeResultModal.fromJson(response?.data);
  });
}
