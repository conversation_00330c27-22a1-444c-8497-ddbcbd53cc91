import 'package:waimai_e_fe_flutter_finance/src/services/model/remitDetail.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/requests.dart';

/// 结算账单
Future<DetailModel> fetchSettleBillDetail(Map params) {
  return comGetApi(
    path: '/finance/v4/h5/api/settleBill/detail',
    params: params,
  ).then((response) {
    if (response?.data == null) {
      return null;
    }
    return DetailModel.fromJson(response?.data);
  });
}
