import 'package:waimai_e_fe_flutter_finance/src/services/model/tradingType.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/requests.dart';

/// OverviewDetail
Future<OverviewDetailModal> fetchOverviewDetail(Map params) {
  return comGetApi(
          path: '/finance/v4/h5/api/dailyBill/monthOverview', params: params)
      .then((response) {
    if (response?.data == null) {
      return null;
    }
    return OverviewDetailModal.fromJson(response?.data);
  });
}

/// BillChargeList
Future<BillChargeListModal> fetchBillChargeList(Map params) {
  return comGetApi(
          path: '/finance/v4/h5/api/billCharge/billChargeList', params: params)
      .then((response) {
    if (response?.data == null) {
      return null;
    }
    return BillChargeListModal.fromJson(response?.data);
  });
}
