import 'package:waimai_e_fe_flutter_finance/src/services/model/withdrawDetail.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/requests.dart';

/// 提现详情
Future<WithdrawDetailModel> fetchWithdrawDetail(Map params) {
  return comGetApi(
    path: '/finance/v4/h5/api/balance/withdrawDetail',
    params: params,
  ).then((response) {
    if (response?.data == null) {
      return null;
    }
    return WithdrawDetailModel.fromJson(response?.data);
  });
}
