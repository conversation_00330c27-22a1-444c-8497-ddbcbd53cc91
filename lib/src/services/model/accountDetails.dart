class AccountInfoModal {
  /// 结算周期
  String settlePeriodName;

  /// 结算方式
  String settleTypeName;

  /// 最低转出余额
  num minWithdrawAmount;

  /// 1:银行卡提现 2:钱包提现
  num payBindType;

  /// 账户
  String cardNumber;

  /// 开户行
  String bankName;

  /// 户名
  String bankAccountName;

  /// 钱包ID
  int walletId;

  /// 结算设置id
  num settleSettingId;

  // 钱包流水URL
  String walletFowUrl;

  // 业务经理电话
  String serviceManagerMobile;

  // 业务经理电话
  String serviceManagerName;

  // 是否展示结算设置修改入口
  bool showSettleSettingButton;

  // 是否展示结算设置新增入口
  bool showSettleSettingAddButton;

  // 账号是否为200账号
  num accType;

  int isDelay;

  // fromJson方法
  AccountInfoModal.fromJson(Map<String, dynamic> json) {
    settlePeriodName = json['settlePeriodName'];
    settleTypeName = json['settleTypeName'];
    minWithdrawAmount = json['minWithdrawAmount'];
    payBindType = json['payBindType'];
    cardNumber = json['cardNumber'];
    bankName = json['bankName'];
    bankAccountName = json['bankAccountName'];
    walletId = json['walletId'];
    settleSettingId = json['settleSettingId'];
    walletFowUrl = json['wallet_flow_url'];
    serviceManagerMobile = json['serviceManagerMobile'];
    serviceManagerName = json['serviceManagerName'];
    showSettleSettingButton = json['showSettleSettingButton'];
    showSettleSettingAddButton = json['showSettleSettingAddButton'];
    accType = json['accType'];
    isDelay = json['isDelay'];
  }
}
