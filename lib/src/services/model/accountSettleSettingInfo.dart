class AccountSettleSettingInfoModel {
  // 属性
  String settlePeriod;
  String settleTypeName;
  String minWithdrawAmount;
  String serviceManagerName;
  String serviceManagerMobile;
  String bankAccountName;
  String bank;
  String cardNo;
  int payBindType;
  int walletId;
  String walletAccountName;
  String walletFlowUrl;
  int settleSettingId;

  // 构造方法
  AccountSettleSettingInfoModel(
      {this.settlePeriod,
      this.settleTypeName,
      this.minWithdrawAmount,
      this.serviceManagerName,
      this.serviceManagerMobile,
      this.bankAccountName,
      this.bank,
      this.cardNo,
      this.payBindType,
      this.walletId,
      this.walletAccountName,
      this.walletFlowUrl,
      this.settleSettingId});

  // fromJson方法
  AccountSettleSettingInfoModel.fromJson(Map<String, dynamic> json) {
    settlePeriod = json['settlePeriod'];
    settleTypeName = json['settleTypeName'];
    minWithdrawAmount = json['minWithdrawAmount'];
    serviceManagerName = json['serviceManagerName'];
    serviceManagerMobile = json['serviceManagerMobile'];
    bankAccountName = json['bankAccountName'];
    bank = json['bank'];
    cardNo = json['cardNo'];
    payBindType = json['payBindType'];
    walletId = json['walletId'];
    walletAccountName = json['walletAccountName'];
    walletFlowUrl = json['walletFlowUrl'];
    settleSettingId = json['settleSettingId'];
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['settlePeriod'] = this.settlePeriod;
    data['settleTypeName'] = this.settleTypeName;
    data['minWithdrawAmount'] = this.minWithdrawAmount;
    data['serviceManagerName'] = this.serviceManagerName;
    data['serviceManagerMobile'] = this.serviceManagerMobile;
    data['bankAccountName'] = this.bankAccountName;
    data['bank'] = this.bank;
    data['cardNo'] = this.cardNo;
    data['payBindType'] = this.payBindType;
    data['walletId'] = this.walletId;
    data['walletAccountName'] = this.walletAccountName;
    data['walletFlowUrl'] = this.walletFlowUrl;
    data['settleSettingId'] = this.settleSettingId;
    return data;
  }
}
