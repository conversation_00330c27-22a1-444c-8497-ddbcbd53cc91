class Orders {
  // 属性
  int date;
  int bizId;
  int type;
  int poiIncomeAmount;
  int configActivityAmount;
  int realActivityAmount;

  // 构造方法
  Orders(
      {this.date,
      this.bizId,
      this.type,
      this.poiIncomeAmount,
      this.configActivityAmount,
      this.realActivityAmount});

  // fromJson方法
  Orders.fromJson(Map<String, dynamic> json) {
    date = json['date'];
    bizId = json['bizId'];
    type = json['type'];
    poiIncomeAmount = json['poiIncomeAmount'];
    configActivityAmount = json['configActivityAmount'];
    realActivityAmount = json['realActivityAmount'];
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['date'] = this.date;
    data['bizId'] = this.bizId;
    data['type'] = this.type;
    data['poiIncomeAmount'] = this.poiIncomeAmount;
    data['configActivityAmount'] = this.configActivityAmount;
    data['realActivityAmount'] = this.realActivityAmount;
    return data;
  }
}

class AdZNMJSettleBillModel {
  // 属性
  int pageCount;
  int pageNo;
  int pageSize;
  String comment;
  int poiRealIncomeTotalAmount;
  String settleBillComment;
  int flowNo;
  List<Orders> orders;

  // 构造方法
  AdZNMJSettleBillModel(
      {this.pageCount,
      this.pageNo,
      this.pageSize,
      this.comment,
      this.poiRealIncomeTotalAmount,
      this.settleBillComment,
      this.flowNo,
      this.orders});

  // fromJson方法
  AdZNMJSettleBillModel.fromJson(Map<String, dynamic> json) {
    pageCount = json['pageCount'];
    pageNo = json['pageNo'];
    pageSize = json['pageSize'];
    comment = json['comment'];
    poiRealIncomeTotalAmount = json['poiRealIncomeTotalAmount'];
    settleBillComment = json['settleBillComment'];
    flowNo = json['flowNo'];

    if (json['orders'] != null) {
      orders = [];
      json['orders'].forEach((v) {
        orders.add(Orders.fromJson(v));
      });
    }
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['pageCount'] = this.pageCount;
    data['pageNo'] = this.pageNo;
    data['pageSize'] = this.pageSize;
    data['comment'] = this.comment;
    data['poiRealIncomeTotalAmount'] = this.poiRealIncomeTotalAmount;
    data['settleBillComment'] = this.settleBillComment;
    data['flowNo'] = this.flowNo;

    if (this.orders != null) {
      data['orders'] = this.orders.map((v) => v.toJson()).toList();
    }

    return data;
  }
}
