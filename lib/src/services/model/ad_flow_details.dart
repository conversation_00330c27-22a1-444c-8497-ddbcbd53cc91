class AdFlowDetailModal {
  //页数
  int pageCount;
  //页码
  int pageNo;
  //每页大小
  int pageSize;
  //商家节省金额总计
  int poiIncomeTotalAmount;
  //平台代付金额
  int platformPayTotalAmount;
  //商家实际入账金额
  int poiRealIncomeTotalAmount;
  //结算账期
  String settleBillComment;
  //流水单号
  int flowNo;
  // 交易列表
  List<AdFlowList> orders;

  AdFlowDetailModal.fromJson(Map<String, dynamic> json) {
    if (json != null) {
      pageCount = json['pageCount'];
      pageNo = json['pageNo'];
      pageSize = json['pageSize'];
      poiIncomeTotalAmount = json['poiIncomeTotalAmount'];
      platformPayTotalAmount = json['platformPayTotalAmount'];
      settleBillComment = json['settleBillComment'];
      flowNo = json['flowNo'];
      orders = [];
      if (json['orders'] != null) {
        json['orders'].forEach((item) {
          orders.add(AdFlowList.fromJson(item));
        });
      }
    }
  }
}

class AdFlowList {
  // 时间戳
  int date;
  // 订单id
  int bizId;
  //1:正向订单 对应交易类型：外卖订单, 2:逆向订单 对应交易类型：退款订单
  int type;
  //活动配置金额
  int configActivityAmount;
  //订单实际使用金额
  int realActivityAmount;
  //商家节省金额
  int poiIncomeAmount;
  //平台代付金额
  int platformPayAmount;

  AdFlowList.fromJson(Map<String, dynamic> json) {
    if (json != null) {
      date = json['date'];
      bizId = json['bizId'];
      type = json['type'];
      configActivityAmount = json['configActivityAmount'];
      realActivityAmount = json['realActivityAmount'];
      poiIncomeAmount = json['poiIncomeAmount'];
      platformPayAmount = json['platformPayAmount'];
    }
  }
}
