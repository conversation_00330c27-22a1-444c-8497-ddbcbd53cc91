class AigcAnalysis {
  AigcAnalysis.fromJson(Map<String, dynamic> json) {
    if (json == null) {
      return;
    }

    event = json['event'];
    sceneType = json['sceneType'];
    cuiResponse = json['cuiResponse'] != null
        ? CUIResponse.fromJson(json['cuiResponse'])
        : null;
    relatedSceneType = json['relatedSceneType'] != null
        ? (json['relatedSceneType'] as List)
            .map((e) => RelatedSceneType.fromJson(e))
            .toList()
        : [];
  }

  /// cmpl 用于标识本包是文本生成操作的一个片段；
  /// lineChartData 用于标识本包是携带折线图数据；
  /// all_done 用于确认流传输结束的标志
  String event;

  /// 场景类型
  String sceneType;
  CUIResponse cuiResponse;
  List<RelatedSceneType> relatedSceneType;
}

class CUIResponse {
  CUIResponse.fromJson(Map<String, dynamic> json) {
    if (json == null) {
      return;
    }

    sessionID = json['sessionID'];
    replyMessage = json['replyMessage'] != null
        ? (json['replyMessage'] as List)
            .map((e) => ReplyMessage.fromJson(e))
            .toList()
        : [];
    content = json['content'];
  }
  String sessionID;
  List<ReplyMessage> replyMessage;
  // 当 contentType 为 cmpl 时，完整的 message
  String content;
}

/// 流式生成内容
class ReplyMessage {
  ReplyMessage.fromJson(Map<String, dynamic> json) {
    if (json == null) {
      return;
    }

    role = json['role'];
    message = json['message'];
    contentType = json['contentType'];
  }

  /// 角色
  String role;

  /// 信息
  String message;

  /// 信息类别
  int contentType;
}

/// 推荐关联场景
class RelatedSceneType {
  RelatedSceneType(this.sceneTypeName, this.sceneType);
  RelatedSceneType.fromJson(Map<String, dynamic> json) {
    if (json == null) {
      return;
    }

    sceneTypeName = json['sceneTypeName'];
    sceneType = json['sceneType'];
  }

  /// 推荐关联场景名称
  String sceneTypeName;

  /// 推荐关联场景类型
  String sceneType;
}

class LineChartItem {
  LineChartItem.fromJson(Map<String, dynamic> json) {
    if (json == null) {
      return;
    }

    dt = json['dt'];
    amount = json['amount'];
    order = json['order'];
  }
  String dt;
  num amount;
  num order;
}

class AigcAnalysisEvent {
  static String cmpl = 'cmpl';
  static String lineChartData = 'lineChartData';
  // ignore: non_constant_identifier_names
  static String all_done = 'all_done';
}

class AigcSceneType {
  static const String home = '1';
  static const String dailyBill = '2';
  static const String balanceFlow = '3';
}

class StreamError {
  StreamError(this.type, this.messgae);

  // 暂定只有type 为 1 情况，后续可细化
  int type;
  String messgae;
}
