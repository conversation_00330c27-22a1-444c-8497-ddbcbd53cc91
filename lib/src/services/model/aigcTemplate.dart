import 'package:waimai_e_fe_flutter_finance/src/services/model/aigcAnalysis.dart';

class AigcTemplate {
  AigcTemplate.fromJson(Map<String, dynamic> json) {
    if (json == null) {
      return;
    }
    sceneType = json['sceneType'];
    content = json['content'];
    relatedSceneType = json['relatedSceneType'] != null
        ? (json['relatedSceneType'] as List)
            .map((e) => RelatedSceneType.fromJson(e))
            .toList()
        : [];
    sessionID = json['sessionID'];
  }

  /// 场景类型
  String sceneType;
  String content;
  List<RelatedSceneType> relatedSceneType;
  String sessionID;
}
