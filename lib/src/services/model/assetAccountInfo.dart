class AssetAccountInfoModel {
  // 属性
  int depositRefundProcessStatus;
  String picUrl;
  String depositBalance;
  int depositBalanceCent;
  // 推广费红包账户是否展示
  int showAdRedPacketRechargeFlag;
  String depositRefundProcessId;
  int showDepositRefundFlag;
  String adRedPacketBalance;
  // 推广红包账户总金额
  int adRedPacketBalanceCent;
  // 推广红包下的赠送红包
  int giftRedPacketBalanceCent;
  String logisticsBalance;
  int logisticsBalanceCent;

  /// 是否展示推广费账户自动充值入口
  int showAutoTransferFlag;
  int showCrowdSourcingAccountRechargeFlag;
  String mainBalance;
  int mainBalanceCent;
  int showDepositAccountRechargeFlag;
  // 是否展示履约保障金账户区域
  int showDepositAccount;
  int supportDepositAccountSelfWithdraw;

  /// 推广费账户是否展示
  int showAdAccount;

  /// 推广费账户余额
  String adBalance;
  int adBalanceCent;
  int showMainAccountRechargeFlag;
  String bankAccountName;

  /// 推广费账户是否有充值入口
  int showAdAccountRechargeFlag;
  int bussinessCount;
  String bussinessName;
  int bussinessAmount;

  /// 高佣返红包（元）
  String highCommissionReturnBalance;
  int highCommissionReturnBalanceCent;

  /// 交易额转入推广费（元）
  String tradeToPromotionBalance;
  int tradeToPromotionBalanceCent;

  /// 智能账户（元）
  String intelligentActivityBalance;
  int intelligentActivityBalanceCent;
  int showFoodSafetyRedPacketAccount;
  String foodSafetyRedPacketBalance;
  int foodSafetyRedPacketBalanceCent;

  /// 推广红包账户
  int discountRedPacketBalance;
  int discountRedPacketBalanceCent;

  // 构造方法
  AssetAccountInfoModel(
      {this.depositRefundProcessStatus,
      this.picUrl,
      this.depositBalance,
      this.depositBalanceCent,
      this.showAdRedPacketRechargeFlag,
      this.depositRefundProcessId,
      this.showDepositRefundFlag,
      this.adRedPacketBalance,
      this.adRedPacketBalanceCent,
      this.giftRedPacketBalanceCent,
      this.logisticsBalance,
      this.logisticsBalanceCent,
      this.showAutoTransferFlag,
      this.showCrowdSourcingAccountRechargeFlag,
      this.mainBalance,
      this.mainBalanceCent,
      this.showDepositAccount,
      this.showDepositAccountRechargeFlag,
      this.supportDepositAccountSelfWithdraw,
      this.showAdAccount,
      this.adBalance,
      this.adBalanceCent,
      this.showMainAccountRechargeFlag,
      this.bankAccountName,
      this.showAdAccountRechargeFlag,
      this.bussinessCount,
      this.bussinessName,
      this.bussinessAmount,
      this.highCommissionReturnBalance,
      this.highCommissionReturnBalanceCent,
      this.tradeToPromotionBalance,
      this.tradeToPromotionBalanceCent,
      this.intelligentActivityBalance,
      this.intelligentActivityBalanceCent,
      this.discountRedPacketBalance,
      this.discountRedPacketBalanceCent,
      this.showFoodSafetyRedPacketAccount,
      this.foodSafetyRedPacketBalance,
      this.foodSafetyRedPacketBalanceCent});

  // fromJson方法
  AssetAccountInfoModel.fromJson(Map<String, dynamic> json) {
    depositRefundProcessStatus = json['depositRefundProcessStatus'];
    picUrl = json['picUrl'];
    depositBalance = json['depositBalance'];
    depositBalanceCent = json['depositBalanceCent'];
    showAdRedPacketRechargeFlag = json['showAdRedPacketRechargeFlag'];
    depositRefundProcessId = json['depositRefundProcessId'];
    showDepositRefundFlag = json['showDepositRefundFlag'];
    adRedPacketBalance = json['adRedPacketBalance'];
    adRedPacketBalanceCent = json['adRedPacketBalanceCent'];
    giftRedPacketBalanceCent = json['giftRedPacketBalanceCent'];
    logisticsBalance = json['logisticsBalance'];
    logisticsBalanceCent = json['logisticsBalanceCent'];
    showAutoTransferFlag = json['showAutoTransferFlag'];
    showCrowdSourcingAccountRechargeFlag =
        json['showCrowdSourcingAccountRechargeFlag'];
    mainBalance = json['mainBalance'];
    mainBalanceCent = json['mainBalanceCent'];
    showDepositAccount = json['showDepositAccount'];
    showDepositAccountRechargeFlag = json['showDepositAccountRechargeFlag'];
    supportDepositAccountSelfWithdraw =
        json['supportDepositAccountSelfWithdraw'];
    showAdAccount = json['showAdAccount'];
    adBalance = json['adBalance'];
    adBalanceCent = json['adBalanceCent'];
    showMainAccountRechargeFlag = json['showMainAccountRechargeFlag'];
    bankAccountName = json['bankAccountName'];
    showAdAccountRechargeFlag = json['showAdAccountRechargeFlag'];
    bussinessCount = json['bussinessCount'];
    bussinessName = json['bussinessName'];
    bussinessAmount = json['bussinessAmount'];
    highCommissionReturnBalance = json['highCommissionReturnBalance'];
    highCommissionReturnBalanceCent = json['highCommissionReturnBalanceCent'];
    tradeToPromotionBalance = json['tradeToPromotionBalance'];
    tradeToPromotionBalanceCent = json['tradeToPromotionBalanceCent'];
    intelligentActivityBalance = json['intelligentActivityBalance'];
    intelligentActivityBalanceCent = json['intelligentActivityBalanceCent'];
    discountRedPacketBalance = json['discountRedPacketBalance'];
    discountRedPacketBalanceCent = json['discountRedPacketBalanceCent'];

    foodSafetyRedPacketBalance = json['foodSafetyRedPacketBalance'];
    showFoodSafetyRedPacketAccount = json['showFoodSafetyRedPacketAccount'];
    foodSafetyRedPacketBalanceCent = json['foodSafetyRedPacketBalanceCent'];
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['depositRefundProcessStatus'] = this.depositRefundProcessStatus;
    data['picUrl'] = this.picUrl;
    data['depositBalance'] = this.depositBalance;
    data['depositBalanceCent'] = this.depositBalanceCent;
    data['showAdRedPacketRechargeFlag'] = this.showAdRedPacketRechargeFlag;
    data['depositRefundProcessId'] = this.depositRefundProcessId;
    data['showDepositRefundFlag'] = this.showDepositRefundFlag;
    data['adRedPacketBalance'] = this.adRedPacketBalance;
    data['adRedPacketBalanceCent'] = this.adRedPacketBalanceCent;
    data['giftRedPacketBalanceCent'] = this.giftRedPacketBalanceCent;
    data['logisticsBalance'] = this.logisticsBalance;
    data['logisticsBalanceCent'] = this.logisticsBalanceCent;
    data['showAutoTransferFlag'] = this.showAutoTransferFlag;
    data['showCrowdSourcingAccountRechargeFlag'] =
        this.showCrowdSourcingAccountRechargeFlag;
    data['mainBalance'] = this.mainBalance;
    data['mainBalanceCent'] = this.mainBalanceCent;
    data['showDepositAccount'] = this.showDepositAccount;
    data['showDepositAccountRechargeFlag'] =
        this.showDepositAccountRechargeFlag;
    data['supportDepositAccountSelfWithdraw'] =
        this.supportDepositAccountSelfWithdraw;
    data['showAdAccount'] = this.showAdAccount;
    data['adBalance'] = this.adBalance;
    data['adBalanceCent'] = this.adBalanceCent;
    data['showMainAccountRechargeFlag'] = this.showMainAccountRechargeFlag;
    data['bankAccountName'] = this.bankAccountName;
    data['showAdAccountRechargeFlag'] = this.showAdAccountRechargeFlag;
    data['bussinessCount'] = this.bussinessCount;
    data['bussinessName'] = this.bussinessName;
    data['bussinessAmount'] = this.bussinessAmount;
    data['highCommissionReturnBalance'] = this.highCommissionReturnBalance;
    data['highCommissionReturnBalanceCent'] =
        this.highCommissionReturnBalanceCent;
    data['tradeToPromotionBalance'] = this.tradeToPromotionBalance;
    data['tradeToPromotionBalanceCent'] = this.tradeToPromotionBalanceCent;
    data['intelligentActivityBalance'] = this.intelligentActivityBalance;
    data['intelligentActivityBalanceCent'] =
        this.intelligentActivityBalanceCent;
    data['discountRedPacketBalance'] = this.discountRedPacketBalance;
    data['discountRedPacketBalanceCent'] = this.discountRedPacketBalanceCent;
    data['showFoodSafetyRedPacketAccount'] =
        this.showFoodSafetyRedPacketAccount;
    data['foodSafetyRedPacketBalance'] = this.foodSafetyRedPacketBalance;
    data['foodSafetyRedPacketBalanceCent'] =
        this.foodSafetyRedPacketBalanceCent;
    return data;
  }
}
