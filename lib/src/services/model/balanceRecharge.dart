// ignore_for_file: non_constant_identifier_names
class RechargeInfoModal {
  /// 账户余额
  num accountBalance;

  /// 账户类型code
  num acctType;

  /// 账户类型名称
  String acctTypeName;

  /// 充值活动文案
  String activityComment;

  /// 商家名称
  String wmPoiName;

  /// 广告账户 - 最小充值金额
  num minAmount;

  // 保证金
  num depositBalance;

  // 保证金
  num depositRechargeAmount;

  // fromJson方法
  RechargeInfoModal.fromJson(Map<String, dynamic> json) {
    accountBalance = json['accountBalance'];
    acctType = json['acctType'];
    acctTypeName = json['acctTypeName'];
    activityComment = json['activityComment'];
    wmPoiName = json['wmPoiName'];
    minAmount = json['minAmount'];
    depositBalance = json['depositBalance'];
    depositRechargeAmount = json['depositRechargeAmount'];
  }
}

class RechargePreOrderModal {
  int jumpNew;
  // String url;
  String tradeno;
  String pay_token;
  String pay_success_url;
  // String redr_url;
  // String redrUrl;
  // String returnUrl;
  // String nb_platform;
  // String nb_version;
  // String nb_show_nav;
  // String nb_source;
  String epassportToken;
  int loginType;
  // 旧版收银台要用
  String redrUrl;
  // int type;
  // int outNo;

  RechargePreOrderModal.fromJson(Map<String, dynamic> json) {
    jumpNew = json['jumpNew'];
    // url = json['url'];
    tradeno = json['tradeNo'];
    pay_token = json['payToken'];
    pay_success_url = json['returnUrl'] ?? json['paySuccessUrl'];
    // redr_url = json['redr_url'];
    redrUrl = json['redrUrl'];
    // nb_platform = json['nb_platform'];
    // nb_version = json['nb_version'];
    // nb_show_nav = json['nb_show_nav'];
    // nb_source = json['nb_source'];
    epassportToken = json['epassportToken'];
    loginType = json['loginType'];
    // type = json['type'];
    // outNo = json['outNo'];
    // returnUrl = json['returnUrl'];
  }
}

class RechargePreAdOrderModal {
  /// 旧版收银台的跳转前缀
  String url;

  /// 公共参数
  RechargePreOrderModal rechargePreOrderModal;

  RechargePreAdOrderModal.fromJson(Map<String, dynamic> json) {
    url = json['url'];
    rechargePreOrderModal = RechargePreOrderModal.fromJson(json['data']);
  }
}
