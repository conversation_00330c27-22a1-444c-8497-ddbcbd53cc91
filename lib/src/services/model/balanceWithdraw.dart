class WithdrawInfoModal {
  int payBindType;
  int walletId;
  num balance;
  int balanceOutStatus;
  String balanceOutMsg;
  String walletAccountName;
  String bankName;
  String accountMobile;
  String bankAccountName;
  String cardNumber;

  WithdrawInfoModal({
    this.payBindType,
    this.walletId,
    this.balance = 0,
    this.balanceOutStatus = 0,
    this.balanceOutMsg,
    this.walletAccountName,
    this.bankName,
    this.accountMobile,
    this.bankAccountName,
    this.cardNumber,
  });

  WithdrawInfoModal.fromJson(Map<String, dynamic> json) {
    if (json != null) {
      payBindType = json['payBindType'];
      walletId = json['walletId'];
      balance = json['balance'];
      balanceOutStatus = json['balanceOutStatus'];
      balanceOutMsg = json['balanceOutMsg'];
      walletAccountName = json['walletAccountName'];
      bankName = json['bankName'];
      accountMobile = json['accountMobile'];
      bankAccountName = json['bankAccountName'];
      cardNumber = json['cardNumber'];
    }
  }
}

class WithdrawConfirmModal {
  int outId = 0;
  WithdrawConfirmModal({this.outId});
  WithdrawConfirmModal.fromJson(Map<String, dynamic> json) {
    if (json != null) {
      outId = json['outId'];
    }
  }
}
