class BannerItemModel {
  BannerItemModel({this.imgUrl, this.linkUrl, this.id});

  BannerItemModel.fromJson(Map<String, dynamic> json) {
    imgUrl = json['imgUrl'];
    linkUrl = json['linkUrl'];
    id = json['id'];
  }

  String imgUrl;
  String linkUrl;
  Null id;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['imgUrl'] = this.imgUrl;
    data['linkUrl'] = this.linkUrl;
    data['id'] = this.id;
    return data;
  }
}
