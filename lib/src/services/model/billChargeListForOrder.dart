class OrderTag {
  int type;
  String name;
  int weight;
  int code;
  OrderTag({this.type, this.name, this.weight, this.code});
  OrderTag.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    name = json['name'];
    weight = json['weight'];
    code = json['code'];
  }
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['type'] = this.type;
    data['name'] = this.name;
    data['weight'] = this.weight;
    data['code'] = this.code;

    return data;
  }
}

class WmPoiBillChargeFeeDynamicVoList {
  // 属性
  int billFeeTypeCode;
  String billFeeTypeName;
  int feeAmount;
  dynamic tip;
  String direction;

  // 构造方法
  WmPoiBillChargeFeeDynamicVoList(
      {this.billFeeTypeCode,
      this.billFeeTypeName,
      this.feeAmount,
      this.tip,
      this.direction});

  // fromJson方法
  WmPoiBillChargeFeeDynamicVoList.fromJson(Map<String, dynamic> json) {
    billFeeTypeCode = json['billFeeTypeCode'];
    billFeeTypeName = json['billFeeTypeName'];
    feeAmount = json['feeAmount'];
    tip = json['tip'];
    direction = json['direction'];
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['billFeeTypeCode'] = this.billFeeTypeCode;
    data['billFeeTypeName'] = this.billFeeTypeName;
    data['feeAmount'] = this.feeAmount;
    data['tip'] = this.tip;
    data['direction'] = this.direction;
    return data;
  }
}

class WmPoiBillChargeDynamicVoList {
  // 属性
  int billChargeId;
  String dailyBillDate;
  int dailyBillDateTimestamp;
  int chargeTypeCode;
  String chargeTypeName;
  int billChargeType;
  String billChargeTypeName;
  int wmOrderViewId;
  int outCreateTimestamp;
  String outCreateTime;
  String outCreateDate;
  int poiOrderPushDayseq;
  int estimateAmount;
  int chargeAmount;
  int isKa;
  String appUrl;
  String webUrl;
  int webDetailType;
  int appDetailType;
  String webTemplate;
  String appTemplate;
  dynamic tips;
  int specialType;
  String title;
  String settleStatus;
  dynamic orderSeqComment;
  int orderType;
  List<OrderTag> tags;

  /// 0未入账 1已入账 2未完成 3已提现    当值为2是跳转到进行中订单列表
  int settleStatusCode;
  List<WmPoiBillChargeFeeDynamicVoList> wmPoiBillChargeFeeDynamicVoList;

  // 构造方法
  WmPoiBillChargeDynamicVoList(
      {this.billChargeId,
      this.dailyBillDate,
      this.dailyBillDateTimestamp,
      this.chargeTypeCode,
      this.chargeTypeName,
      this.billChargeType,
      this.billChargeTypeName,
      this.wmOrderViewId,
      this.outCreateTimestamp,
      this.outCreateTime,
      this.outCreateDate,
      this.poiOrderPushDayseq,
      this.estimateAmount,
      this.chargeAmount,
      this.isKa,
      this.appUrl,
      this.webUrl,
      this.webDetailType,
      this.appDetailType,
      this.webTemplate,
      this.appTemplate,
      this.tips,
      this.specialType,
      this.title,
      this.settleStatus,
      this.orderSeqComment,
      this.orderType,
      this.settleStatusCode,
      this.wmPoiBillChargeFeeDynamicVoList,
      this.tags});

  // fromJson方法
  WmPoiBillChargeDynamicVoList.fromJson(Map<String, dynamic> json) {
    billChargeId = json['billChargeId'];
    dailyBillDate = json['dailyBillDate'];
    dailyBillDateTimestamp = json['dailyBillDateTimestamp'];
    chargeTypeCode = json['chargeTypeCode'];
    chargeTypeName = json['chargeTypeName'];
    billChargeType = json['billChargeType'];
    billChargeTypeName = json['billChargeTypeName'];
    wmOrderViewId = json['wmOrderViewId'];
    outCreateTimestamp = json['outCreateTimestamp'];
    outCreateTime = json['outCreateTime'];
    outCreateDate = json['outCreateDate'];
    poiOrderPushDayseq = json['poiOrderPushDayseq'];
    estimateAmount = json['estimateAmount'];
    chargeAmount = json['chargeAmount'];
    isKa = json['isKa'];
    appUrl = json['appUrl'];
    webUrl = json['webUrl'];
    webDetailType = json['webDetailType'];
    appDetailType = json['appDetailType'];
    webTemplate = json['webTemplate'];
    appTemplate = json['appTemplate'];
    tips = json['tips'];
    specialType = json['specialType'];
    title = json['title'];
    settleStatus = json['settleStatus'];
    orderSeqComment = json['orderSeqComment'];
    orderType = json['orderType'];
    settleStatusCode = json['settleStatusCode'];

    if (json['wmPoiBillChargeFeeDynamicVoList'] != null) {
      wmPoiBillChargeFeeDynamicVoList = [];
      json['wmPoiBillChargeFeeDynamicVoList'].forEach((v) {
        wmPoiBillChargeFeeDynamicVoList
            .add(WmPoiBillChargeFeeDynamicVoList.fromJson(v));
      });
    }
    if (json['tags'] != null) {
      tags = [];
      json['tags'].forEach((v) {
        tags.add(OrderTag.fromJson(v));
      });
    }
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['billChargeId'] = this.billChargeId;
    data['dailyBillDate'] = this.dailyBillDate;
    data['dailyBillDateTimestamp'] = this.dailyBillDateTimestamp;
    data['chargeTypeCode'] = this.chargeTypeCode;
    data['chargeTypeName'] = this.chargeTypeName;
    data['billChargeType'] = this.billChargeType;
    data['billChargeTypeName'] = this.billChargeTypeName;
    data['wmOrderViewId'] = this.wmOrderViewId;
    data['outCreateTimestamp'] = this.outCreateTimestamp;
    data['outCreateTime'] = this.outCreateTime;
    data['outCreateDate'] = this.outCreateDate;
    data['poiOrderPushDayseq'] = this.poiOrderPushDayseq;
    data['estimateAmount'] = this.estimateAmount;
    data['chargeAmount'] = this.chargeAmount;
    data['isKa'] = this.isKa;
    data['appUrl'] = this.appUrl;
    data['webUrl'] = this.webUrl;
    data['webDetailType'] = this.webDetailType;
    data['appDetailType'] = this.appDetailType;
    data['webTemplate'] = this.webTemplate;
    data['appTemplate'] = this.appTemplate;
    data['tips'] = this.tips;
    data['specialType'] = this.specialType;
    data['title'] = this.title;
    data['settleStatus'] = this.settleStatus;
    data['orderSeqComment'] = this.orderSeqComment;
    data['orderType'] = this.orderType;
    data['settleStatusCode'] = this.settleStatusCode;

    if (this.tags != null) {
      data['tags'] = this.tags.map((v) => v.toJson()).toList();
    }
    if (this.wmPoiBillChargeFeeDynamicVoList != null) {
      data['wmPoiBillChargeFeeDynamicVoList'] =
          this.wmPoiBillChargeFeeDynamicVoList.map((v) => v.toJson()).toList();
    }

    return data;
  }
}

class WmPoiBillChargeTitleVoList {
  String title;
  String typeCode;
  String tips;
  WmPoiBillChargeTitleVoList.fromJson(Map<String, dynamic> json) {
    tips = json['tips'];
    typeCode = json['typeCode'];
    title = json['title'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['tips'] = this.tips;
    data['typeCode'] = this.typeCode;
    data['title'] = this.title;
    return data;
  }
}

class BillChargeListForOrderModel {
  // 属性
  List<WmPoiBillChargeTitleVoList> wmPoiBillChargeTitleVoList;
  int count;
  List<WmPoiBillChargeDynamicVoList> wmPoiBillChargeDynamicVoList;
  String poiInfo;
  int wmPoiId;
  bool isCompleteBeforeCancelOrder;

  // 构造方法
  BillChargeListForOrderModel(
      {this.wmPoiBillChargeTitleVoList,
      this.count,
      this.wmPoiBillChargeDynamicVoList,
      this.poiInfo,
      this.wmPoiId,
      this.isCompleteBeforeCancelOrder});

  // fromJson方法
  BillChargeListForOrderModel.fromJson(Map<String, dynamic> json) {
    if (json['wmPoiBillChargeTitleVoList'] != null) {
      wmPoiBillChargeTitleVoList = [];
      json['wmPoiBillChargeTitleVoList'].forEach((e) {
        wmPoiBillChargeTitleVoList.add(WmPoiBillChargeTitleVoList.fromJson(e));
      });
    }

    count = json['count'];

    if (json['wmPoiBillChargeDynamicVoList'] != null) {
      wmPoiBillChargeDynamicVoList = [];
      json['wmPoiBillChargeDynamicVoList'].forEach((v) {
        wmPoiBillChargeDynamicVoList
            .add(WmPoiBillChargeDynamicVoList.fromJson(v));
      });
    }

    poiInfo = json['poiInfo'];
    wmPoiId = json['wmPoiId'];
    isCompleteBeforeCancelOrder = json['isCompleteBeforeCancelOrder'];
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    if (this.wmPoiBillChargeTitleVoList != null) {
      data['wmPoiBillChargeTitleVoList'] =
          this.wmPoiBillChargeTitleVoList.map((e) => e.toJson()).toList();
    }
    data['count'] = this.count;

    if (this.wmPoiBillChargeDynamicVoList != null) {
      data['wmPoiBillChargeDynamicVoList'] =
          this.wmPoiBillChargeDynamicVoList.map((v) => v.toJson()).toList();
    }

    data['poiInfo'] = this.poiInfo;
    data['wmPoiId'] = this.wmPoiId;
    data['isCompleteBeforeCancelOrder'] = this.isCompleteBeforeCancelOrder;
    return data;
  }
}
