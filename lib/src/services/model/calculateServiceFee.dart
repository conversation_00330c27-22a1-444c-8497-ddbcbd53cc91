import 'package:waimai_e_fe_flutter_finance/src/services/model/order/billChargeDetailDynamic.dart';

class WmPoiBillChargeCardDynamicVo {
  // 属性
  int chargeTypeCode;
  int settleState;
  String wmOrderViewId;
  int wmOrderDaySeq;
  int amount;
  int estimateAmount;
  List<DataList> dataList;
  int chargeModel;
  int settleNegativeType;

  // 构造方法
  WmPoiBillChargeCardDynamicVo(
      {this.chargeTypeCode,
      this.settleState,
      this.wmOrderViewId,
      this.wmOrderDaySeq,
      this.amount,
      this.estimateAmount,
      this.dataList,
      this.chargeModel,
      this.settleNegativeType});

  // fromJson方法
  WmPoiBillChargeCardDynamicVo.fromJson(Map<String, dynamic> json) {
    chargeTypeCode = json['chargeTypeCode'];
    settleState = json['settleState'];
    wmOrderViewId = json['wmOrderViewId'];
    wmOrderDaySeq = json['wmOrderDaySeq'];
    amount = json['amount'];
    estimateAmount = json['estimateAmount'];

    if (json['dataList'] != null) {
      dataList = [];
      json['dataList'].forEach((v) {
        dataList.add(DataList.fromJson(v));
      });
    }

    chargeModel = json['chargeModel'];
    settleNegativeType = json['settleNegativeType'];
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['chargeTypeCode'] = this.chargeTypeCode;
    data['settleState'] = this.settleState;
    data['wmOrderViewId'] = this.wmOrderViewId;
    data['wmOrderDaySeq'] = this.wmOrderDaySeq;
    data['amount'] = this.amount;
    data['estimateAmount'] = this.estimateAmount;

    if (this.dataList != null) {
      data['dataList'] = this.dataList.map((v) => v.toJson()).toList();
    }

    data['chargeModel'] = this.chargeModel;
    data['settleNegativeType'] = this.settleNegativeType;
    return data;
  }
}

class FeeCalRequestWrapper {
  // 属性
  int partnerId;
  int originalFoodTotal;
  int foodTotalPriceCent;
  int originalPoiChargeFee;
  int poiChargeFeeCent;
  String logisticsCode;
  int chargeMode;
  int originalCommissionRate;
  double commissionRate;
  int originalCommissionGuarantee;
  int commissionGuaranteeCent;
  int originalOrderDistance;
  int orderDistanceMetre;

  // 构造方法
  FeeCalRequestWrapper(
      {this.partnerId,
      this.originalFoodTotal,
      this.foodTotalPriceCent,
      this.originalPoiChargeFee,
      this.poiChargeFeeCent,
      this.logisticsCode,
      this.chargeMode,
      this.originalCommissionRate,
      this.commissionRate,
      this.originalCommissionGuarantee,
      this.commissionGuaranteeCent,
      this.originalOrderDistance,
      this.orderDistanceMetre});

  // fromJson方法
  FeeCalRequestWrapper.fromJson(Map<String, dynamic> json) {
    partnerId = json['partnerId'];
    originalFoodTotal = json['originalFoodTotal'];
    foodTotalPriceCent = json['foodTotalPriceCent'];
    originalPoiChargeFee = json['originalPoiChargeFee'];
    poiChargeFeeCent = json['poiChargeFeeCent'];
    logisticsCode = json['logisticsCode'];
    chargeMode = json['chargeMode'];
    originalCommissionRate = json['originalCommissionRate'];
    commissionRate = json['commissionRate'];
    originalCommissionGuarantee = json['originalCommissionGuarantee'];
    commissionGuaranteeCent = json['commissionGuaranteeCent'];
    originalOrderDistance = json['originalOrderDistance'];
    orderDistanceMetre = json['orderDistanceMetre'];
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['partnerId'] = this.partnerId;
    data['originalFoodTotal'] = this.originalFoodTotal;
    data['foodTotalPriceCent'] = this.foodTotalPriceCent;
    data['originalPoiChargeFee'] = this.originalPoiChargeFee;
    data['poiChargeFeeCent'] = this.poiChargeFeeCent;
    data['logisticsCode'] = this.logisticsCode;
    data['chargeMode'] = this.chargeMode;
    data['originalCommissionRate'] = this.originalCommissionRate;
    data['commissionRate'] = this.commissionRate;
    data['originalCommissionGuarantee'] = this.originalCommissionGuarantee;
    data['commissionGuaranteeCent'] = this.commissionGuaranteeCent;
    data['originalOrderDistance'] = this.originalOrderDistance;
    data['orderDistanceMetre'] = this.orderDistanceMetre;
    return data;
  }
}

class ExtInfo {
  // 属性
  int partnerId;
  String foodTotalPrice;
  String poiChargeFee;
  String logisticsCode;
  String chargeMode;

  // 构造方法
  ExtInfo(
      {this.partnerId,
      this.foodTotalPrice,
      this.poiChargeFee,
      this.logisticsCode,
      this.chargeMode});

  // fromJson方法
  ExtInfo.fromJson(Map<String, dynamic> json) {
    partnerId = json['partnerId'];
    foodTotalPrice = json['foodTotalPrice'];
    poiChargeFee = json['poiChargeFee'];
    logisticsCode = json['logisticsCode'];
    chargeMode = json['chargeMode'];
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['partnerId'] = this.partnerId;
    data['foodTotalPrice'] = this.foodTotalPrice;
    data['poiChargeFee'] = this.poiChargeFee;
    data['logisticsCode'] = this.logisticsCode;
    data['chargeMode'] = this.chargeMode;
    return data;
  }
}

class CalculateServiceFeeModel {
  // 属性
  WmPoiBillChargeCardDynamicVo wmPoiBillChargeCardDynamicVo;
  FeeCalRequestWrapper feeCalRequestWrapper;
  ExtInfo extInfo;

  // 构造方法
  CalculateServiceFeeModel(
      {this.wmPoiBillChargeCardDynamicVo,
      this.feeCalRequestWrapper,
      this.extInfo});

  // fromJson方法
  CalculateServiceFeeModel.fromJson(Map<String, dynamic> json) {
    wmPoiBillChargeCardDynamicVo = json['wmPoiBillChargeCardDynamicVo'] != null
        ? WmPoiBillChargeCardDynamicVo.fromJson(
            json['wmPoiBillChargeCardDynamicVo'])
        : null;
    feeCalRequestWrapper = json['feeCalRequestWrapper'] != null
        ? FeeCalRequestWrapper.fromJson(json['feeCalRequestWrapper'])
        : null;
    extInfo =
        json['extInfo'] != null ? ExtInfo.fromJson(json['extInfo']) : null;
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();

    if (this.wmPoiBillChargeCardDynamicVo != null) {
      data['wmPoiBillChargeCardDynamicVo'] =
          this.wmPoiBillChargeCardDynamicVo.toJson();
    }

    if (this.feeCalRequestWrapper != null) {
      data['feeCalRequestWrapper'] = this.feeCalRequestWrapper.toJson();
    }

    if (this.extInfo != null) {
      data['extInfo'] = this.extInfo.toJson();
    }

    return data;
  }
}
