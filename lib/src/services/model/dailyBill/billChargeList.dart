class BillChargeModel {
  // 属性
  int billChargeId;
  String dailyBillDate;
  int dailyBillDateTimestamp;
  int outCreateTimestamp;
  int chargeTypeCode;
  String chargeTypeName;
  int billChargeType;
  String billChargeTypeName;
  int chargeAmount;
  String wmOrderViewId;
  int poiOrderPushDayseq;
  String outCreateDate;
  String outCreateTime;
  String currency;
  bool isKA;
  String appUrl;
  String webUrl;
  int webDetailType;
  int appDetailType;
  String webTemplate;
  String appTemplate;
  dynamic tips;
  int specialType;

  // 构造方法
  BillChargeModel(
      {this.billChargeId,
      this.dailyBillDate,
      this.dailyBillDateTimestamp,
      this.outCreateTimestamp,
      this.chargeTypeCode,
      this.chargeTypeName,
      this.billChargeType,
      this.billChargeTypeName,
      this.chargeAmount,
      this.wmOrderViewId,
      this.poiOrderPushDayseq,
      this.outCreateDate,
      this.outCreateTime,
      this.currency,
      this.isKA,
      this.appUrl,
      this.webUrl,
      this.webDetailType,
      this.appDetailType,
      this.webTemplate,
      this.appTemplate,
      this.tips,
      this.specialType});

  // fromJson方法
  BillChargeModel.fromJson(Map<String, dynamic> json) {
    billChargeId = json['billChargeId'];
    dailyBillDate = json['dailyBillDate'];
    dailyBillDateTimestamp = json['dailyBillDateTimestamp'];
    outCreateTimestamp = json['outCreateTimestamp'];
    chargeTypeCode = json['chargeTypeCode'];
    chargeTypeName = json['chargeTypeName'];
    billChargeType = json['billChargeType'];
    billChargeTypeName = json['billChargeTypeName'];
    chargeAmount = json['chargeAmount'];
    wmOrderViewId = json['wmOrderViewId'];
    poiOrderPushDayseq = json['poiOrderPushDayseq'];
    outCreateDate = json['outCreateDate'];
    outCreateTime = json['outCreateTime'];
    currency = json['currency'];
    isKA = json['isKA'];
    appUrl = json['appUrl'];
    webUrl = json['webUrl'];
    webDetailType = json['webDetailType'];
    appDetailType = json['appDetailType'];
    webTemplate = json['webTemplate'];
    appTemplate = json['appTemplate'];
    tips = json['tips'];
    specialType = json['specialType'];
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['billChargeId'] = this.billChargeId;
    data['dailyBillDate'] = this.dailyBillDate;
    data['dailyBillDateTimestamp'] = this.dailyBillDateTimestamp;
    data['outCreateTimestamp'] = this.outCreateTimestamp;
    data['chargeTypeCode'] = this.chargeTypeCode;
    data['chargeTypeName'] = this.chargeTypeName;
    data['billChargeType'] = this.billChargeType;
    data['billChargeTypeName'] = this.billChargeTypeName;
    data['chargeAmount'] = this.chargeAmount;
    data['wmOrderViewId'] = this.wmOrderViewId;
    data['poiOrderPushDayseq'] = this.poiOrderPushDayseq;
    data['outCreateDate'] = this.outCreateDate;
    data['outCreateTime'] = this.outCreateTime;
    data['currency'] = this.currency;
    data['isKA'] = this.isKA;
    data['appUrl'] = this.appUrl;
    data['webUrl'] = this.webUrl;
    data['webDetailType'] = this.webDetailType;
    data['appDetailType'] = this.appDetailType;
    data['webTemplate'] = this.webTemplate;
    data['appTemplate'] = this.appTemplate;
    data['tips'] = this.tips;
    data['specialType'] = this.specialType;
    return data;
  }
}
