class UnfinishOrderNotice {
  bool showTip;
  String webUrl;
  String appUrl;
  UnfinishOrderNotice({
    this.appUrl,
    this.showTip,
    this.webUrl,
  });

  UnfinishOrderNotice.fromJson(Map<String, dynamic> json) {
    appUrl = json["appUrl"];
    webUrl = json["webUrl"];
    showTip = json["showTip"];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data["appUrl"] = this.appUrl;
    data["webUrl"] = this.webUrl;
    data["showTip"] = this.showTip;
    return data;
  }
}
