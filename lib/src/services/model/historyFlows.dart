class HistoryFlowsModel {
  // 属性
  int wmPoiId;
  int flowType;
  String flowTypeName;
  int settleBillId;
  String moneyStr;
  String balanceStr;
  String flowTime;
  String outId;
  String flowStateName;
  int subFlowType;
  int withdrawStatus;
  String flowTypeComment;
  int moneyCent;
  int balanceCent;
  dynamic comment;
  int flowTimestamp;

  // 构造方法
  HistoryFlowsModel(
      {this.wmPoiId,
      this.flowType,
      this.flowTypeName,
      this.settleBillId,
      this.moneyStr,
      this.balanceStr,
      this.flowTime,
      this.outId,
      this.flowStateName,
      this.subFlowType,
      this.withdrawStatus,
      this.flowTypeComment,
      this.moneyCent,
      this.balanceCent,
      this.comment,
      this.flowTimestamp});

  // fromJson方法
  HistoryFlowsModel.fromJson(Map<String, dynamic> json) {
    wmPoiId = json['wmPoiId'];
    flowType = json['flowType'];
    flowTypeName = json['flowTypeName'];
    settleBillId = json['settleBillId'];
    moneyStr = json['moneyStr'];
    balanceStr = json['balanceStr'];
    flowTime = json['flowTime'];
    outId = json['outId'];
    flowStateName = json['flowStateName'];
    subFlowType = json['subFlowType'];
    withdrawStatus = json['withdrawStatus'];
    flowTypeComment = json['flowTypeComment'];
    moneyCent = json['moneyCent'];
    balanceCent = json['balanceCent'];
    comment = json['comment'];
    flowTimestamp = json['flowTimestamp'];
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['wmPoiId'] = this.wmPoiId;
    data['flowType'] = this.flowType;
    data['flowTypeName'] = this.flowTypeName;
    data['settleBillId'] = this.settleBillId;
    data['moneyStr'] = this.moneyStr;
    data['balanceStr'] = this.balanceStr;
    data['flowTime'] = this.flowTime;
    data['outId'] = this.outId;
    data['flowStateName'] = this.flowStateName;
    data['subFlowType'] = this.subFlowType;
    data['withdrawStatus'] = this.withdrawStatus;
    data['flowTypeComment'] = this.flowTypeComment;
    data['moneyCent'] = this.moneyCent;
    data['balanceCent'] = this.balanceCent;
    data['comment'] = this.comment;
    data['flowTimestamp'] = this.flowTimestamp;
    return data;
  }
}

class AccountFlowsList {
  // 总数量
  int totalCount;
  List<HistoryFlowsModel> flowList;

  AccountFlowsList.fromJson(Map<String, dynamic> json) {
    if (json != null) {
      totalCount = json["totalCount"];
      if (json["wmAccountFlowListResponseList"] != null) {
        flowList = [];
        json["wmAccountFlowListResponseList"].forEach((v) {
          flowList.add(HistoryFlowsModel.fromJson(v));
        });
      }
    }
  }
}
