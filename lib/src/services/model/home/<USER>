class DailyBill {
  // 属性
  String dailyBillDate;
  int dailyBillDateTimestamp;
  int dailyBillAmount;
  String currency;
  String settleDate;

  /// 是否是今日，展示【今日已完单金额】
  bool today;

  // 构造方法
  DailyBill(
      {this.dailyBillDate,
      this.dailyBillAmount,
      this.currency,
      this.settleDate,
      this.dailyBillDateTimestamp});

  // fromJson方法
  DailyBill.fromJson(Map<String, dynamic> json) {
    dailyBillDate = json['dailyBillDate'];
    dailyBillAmount = json['dailyBillAmount'];
    currency = json['currency'];
    dailyBillDateTimestamp = json['dailyBillDateTimestamp'];
    today = json['today'];
    settleDate = json['settleDate'];
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['dailyBillDate'] = this.dailyBillDate;
    data['dailyBillAmount'] = this.dailyBillAmount;
    data['currency'] = this.currency;
    data['settleDate'] = this.settleDate;
    data['today'] = this.today;
    data['dailyBillDateTimestamp'] = this.dailyBillDateTimestamp;
    return data;
  }
}
