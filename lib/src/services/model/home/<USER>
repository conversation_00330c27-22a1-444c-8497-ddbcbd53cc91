// 最近结算账单
import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';

class RecentOneModel {
  // 属性
  int settleBillId;
  String settleBillStartDate;
  String settleBillEndDate;
  int settleBillAmount;
  String currency;
  List<DailyBill> dailyBills;

  // 构造方法
  RecentOneModel(
      {this.settleBillId,
      this.settleBillStartDate,
      this.settleBillEndDate,
      this.settleBillAmount,
      this.currency,
      this.dailyBills});

  // fromJson方法
  RecentOneModel.fromJson(Map<String, dynamic> json) {
    settleBillId = json['settleBillId'];
    settleBillStartDate = json['settleBillStartDate'];
    settleBillEndDate = json['settleBillEndDate'];
    settleBillAmount = json['settleBillAmount'];
    currency = json['currency'];

    if (json['dailyBills'] != null) {
      dailyBills = [];
      json['dailyBills'].forEach((v) {
        dailyBills.add(DailyBill.fromJson(v));
      });
    }
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['settleBillId'] = this.settleBillId;
    data['settleBillStartDate'] = this.settleBillStartDate;
    data['settleBillEndDate'] = this.settleBillEndDate;
    data['settleBillAmount'] = this.settleBillAmount;
    data['currency'] = this.currency;

    if (this.dailyBills != null) {
      data['dailyBills'] = this.dailyBills.map((v) => v.toJson()).toList();
    }

    return data;
  }
}
