class TipList {
  String tipContent;
  int tipType;
  int dailyBillDate;
  int withDrawId;

  TipList({this.tipContent, this.tipType, this.dailyBillDate, this.withDrawId});

  TipList.fromJson(Map<String, dynamic> json) {
    tipContent = json['tipContent'];
    tipType = json['tipType'];
    dailyBillDate = json['dailyBillDate'];
    withDrawId = json['withDrawId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['tipContent'] = this.tipContent;
    data['tipType'] = this.tipType;
    data['dailyBillDate'] = this.dailyBillDate;
    data['withDrawId'] = this.withDrawId;
    return data;
  }
}

class SimpleInfoModel {
  List<TipList> tipList;
  // 属性
  String withDrawDate;
  int withDrawDateTimestamp;
  String payBindTypeName;
  int withDrawLimit;
  String settlePeriodName;
  int withDrawAmount;
  int balance;
  // 账户冻结状态
  int isFrozenFlag;
  String balanceOutTypeStr;
  String withDrawMsg;
  int settleType;
  String serviceManagerMobile;
  int withDrawStatus;
  String serviceManagerName;
  int withDrawId;
  // 头图链接
  String picUrl;
  // 结算账户名称
  String bankAccountName;
  // 灰度
  int showOrderTab;
  // 是否有返佣入账数据
  int commissionRebateDate;
  String frozenReason;
  int freezeCode;
  int frozenReasonType;

  // 构造方法
  SimpleInfoModel(
      {this.withDrawDate,
      this.withDrawDateTimestamp,
      this.payBindTypeName,
      this.withDrawLimit,
      this.settlePeriodName,
      this.withDrawAmount,
      this.balance,
      this.isFrozenFlag,
      this.balanceOutTypeStr,
      this.withDrawMsg,
      this.settleType,
      this.serviceManagerMobile,
      this.withDrawStatus,
      this.serviceManagerName,
      this.withDrawId,
      this.picUrl,
      this.bankAccountName,
      this.tipList,
      this.showOrderTab,
      this.commissionRebateDate,
      this.frozenReason,
      this.freezeCode,
      this.frozenReasonType});

  // fromJson方法
  SimpleInfoModel.fromJson(Map<String, dynamic> json) {
    if (json == null) {
      return;
    }
    withDrawDate = json['withDrawDate'];
    withDrawDateTimestamp = json['withDrawDateTimestamp'];
    payBindTypeName = json['payBindTypeName'];
    withDrawLimit = json['withDrawLimit'];
    settlePeriodName = json['settlePeriodName'];
    withDrawAmount = json['withDrawAmount'];
    balance = json['balance'];
    isFrozenFlag = json['isFrozenFlag'];
    balanceOutTypeStr = json['balanceOutTypeStr'];
    withDrawMsg = json['withDrawMsg'];
    settleType = json['settleType'];
    serviceManagerMobile = json['serviceManagerMobile'];
    withDrawStatus = json['withDrawStatus'];
    serviceManagerName = json['serviceManagerName'];
    withDrawId = json['withDrawId'];
    picUrl = json["picUrl"];
    bankAccountName = json["bankAccountName"];
    showOrderTab = json["showOrderTab"];
    commissionRebateDate = json["commissionRebateDate"];
    frozenReason = json['frozenReason'];
    freezeCode = json['freezeCode'];
    frozenReasonType = json['frozenReasonType'];

    if (json['tipList'] != null) {
      tipList = [];
      json['tipList'].forEach((v) {
        tipList.add(TipList.fromJson(v));
      });
    }
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['withDrawDate'] = this.withDrawDate;
    data['withDrawDateTimestamp'] = this.withDrawDateTimestamp;
    data['payBindTypeName'] = this.payBindTypeName;
    data['withDrawLimit'] = this.withDrawLimit;
    data['settlePeriodName'] = this.settlePeriodName;
    data['withDrawAmount'] = this.withDrawAmount;
    data['balance'] = this.balance;
    data['isFrozenFlag'] = this.isFrozenFlag;
    data['balanceOutTypeStr'] = this.balanceOutTypeStr;
    data['withDrawMsg'] = this.withDrawMsg;
    data['settleType'] = this.settleType;
    data['serviceManagerMobile'] = this.serviceManagerMobile;
    data['withDrawStatus'] = this.withDrawStatus;
    data['serviceManagerName'] = this.serviceManagerName;
    data['withDrawId'] = this.withDrawId;
    data['bankAccountName'] = this.bankAccountName;
    data['picUrl'] = this.picUrl;
    data['showOrderTab'] = this.showOrderTab;
    data['commissionRebateDate'] = this.commissionRebateDate;
    data['frozenReason'] = this.frozenReason;
    data['freezeCode'] = this.freezeCode;
    data['frozenReasonType'] = this.frozenReasonType;

    if (this.tipList != null) {
      data['tipList'] = this.tipList.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
