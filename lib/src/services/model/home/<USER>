import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';
import 'package:waimai_e_fe_flutter_finance/src/tools/dateTime.dart';

// class DailyBillModel {
//   // 属性
//   int settleState;
//   int totalAmount;
//   int settleDate;
//   dynamic currency;

//   // 冗余字段，主要为了兼容前端字段
//   String dailyBillDate;
//   int dailyBillAmount;

//   // 构造方法
//   DailyBillModel({
//     this.settleState,
//     this.totalAmount,
//     this.settleDate,
//     this.currency,
//     this.dailyBillDate,
//   });

//   // fromJson方法
//   DailyBillModel.fromJson(Map<String, dynamic> json) {
//     settleState = json['settleState'];
//     totalAmount = json['totalAmount'];
//     settleDate = json['settleDate'];
//     currency = json['currency'];
//     // 处理日期
//     dailyBillDate = DateFormat.formatMMDD(settleDate);
//     dailyBillAmount = totalAmount;
//   }

//   // // toJson方法
//   // Map<String, dynamic> toJson() {
//   //   final Map<String, dynamic> data = Map<String, dynamic>();
//   //   data['settleState'] = this.settleState;
//   //   data['totalAmount'] = this.totalAmount;
//   //   data['settleDate'] = this.settleDate;
//   //   data['currency'] = this.currency;
//   //   data['settleBillDate'] = this.dailyBillDate;
//   //   return data;
//   // }
// }

// 今日实时账单
class DailyBillListModel {
  List<DailyBill> dailyBillList;
  DailyBillListModel({this.dailyBillList});
  DailyBillListModel.fromJson(List<dynamic> list) {
    dailyBillList = [];
    if (list != null) {
      list.forEach((v) {
        int settleDate = v['settleDate'];
        int totalAmount = v['totalAmount'];
        String dailyBillDate = DateFormat.formatYYYYMMDD(settleDate);
        int dailyBillAmount = totalAmount;
        // 类型转换
        v['dailyBillDate'] = dailyBillDate;
        v['dailyBillAmount'] = dailyBillAmount;
        dailyBillList.add(DailyBill.fromJson(v));
      });
    }
  }
}
