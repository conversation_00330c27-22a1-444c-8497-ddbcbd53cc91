class WithdrawDisplayMsgModel {
  // 属性
  String message;
  bool display;
  String url;

  // 构造方法
  WithdrawDisplayMsgModel({this.message, this.display, this.url});

  // fromJson方法
  WithdrawDisplayMsgModel.fromJson(Map<String, dynamic> json) {
    message = json['message'];
    display = json['display'];
    url = json['url'];
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['message'] = this.message;
    data['display'] = this.display;
    data['url'] = this.url;
    return data;
  }
}
