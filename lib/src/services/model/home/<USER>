import 'package:waimai_e_fe_flutter_finance/src/services/model/home/<USER>';

class SettleBill {
  // 结算周期开始时间
  String settleBillStartDate;
  int settleBillStartDateTimestamp;

  /// 结算周期结束时间
  String settleBillEndDate;
  int settleBillEndDateTimestamp;

  /// 结算状态，0-未结算，1-已汇入余额
  int settleState;

  /// 预计入账时间
  String settleDate;
  int settleDateTimestamp;
  String convertedSettleDate;
  String accountSuccessTime;
  String tag;
  int totalAmount;
  String currency;
  List<DailyBill> dailyBills;

  // 构造方法
  SettleBill(
      {this.settleBillStartDate,
      this.settleBillEndDate,
      this.settleState,
      this.settleDate,
      this.convertedSettleDate,
      this.accountSuccessTime,
      this.tag,
      this.totalAmount,
      this.currency,
      this.dailyBills,
      this.settleBillEndDateTimestamp,
      this.settleBillStartDateTimestamp,
      this.settleDateTimestamp});

  // fromJson方法
  SettleBill.fromJson(Map<String, dynamic> json) {
    settleBillStartDate = json['settleBillStartDate'];
    settleBillEndDate = json['settleBillEndDate'];
    settleState = json['settleState'];
    settleDate = json['settleDate'];
    settleBillEndDateTimestamp = json['settleBillEndDateTimestamp'];
    settleBillStartDateTimestamp = json['settleBillStartDateTimestamp'];
    convertedSettleDate = json['convertedSettleDate'];
    accountSuccessTime = json['accountSuccessTime'];
    tag = json['tag'];
    totalAmount = json['totalAmount'];
    currency = json['currency'];
    settleDateTimestamp = json['settleDateTimestamp'];

    if (json['dailyBills'] != null) {
      dailyBills = [];
      json['dailyBills'].forEach((v) {
        dailyBills.add(DailyBill.fromJson(v));
      });
    }
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['settleBillStartDate'] = this.settleBillStartDate;
    data['settleBillEndDate'] = this.settleBillEndDate;
    data['settleState'] = this.settleState;
    data['settleDate'] = this.settleDate;
    data['convertedSettleDate'] = this.convertedSettleDate;
    data['accountSuccessTime'] = this.accountSuccessTime;
    data['tag'] = this.tag;
    data['totalAmount'] = this.totalAmount;
    data['currency'] = this.currency;

    data['settleBillEndDateTimestamp'] = this.settleBillEndDateTimestamp;
    data['settleBillStartDateTimestamp'] = this.settleBillStartDateTimestamp;
    data['settleDateTimestamp'] = this.settleDateTimestamp;

    if (this.dailyBills != null) {
      data['dailyBills'] = this.dailyBills.map((v) => v.toJson()).toList();
    }

    return data;
  }
}

class HistorySettleBillModel {
  // 属性
  int payPeriodNum;
  int payPeriodUnit;
  int settledBillCount;
  List<SettleBill> unSettledBills;
  List<SettleBill> settledBills;
  int isDelay;
  int delayNum;
  int isIncrementDelay;

  // 构造方法
  HistorySettleBillModel(
      {this.payPeriodNum,
      this.payPeriodUnit,
      this.settledBillCount,
      this.unSettledBills,
      this.settledBills,
      this.isDelay,
      this.delayNum,
      this.isIncrementDelay});

  // fromJson方法
  HistorySettleBillModel.fromJson(Map<String, dynamic> json) {
    if (json == null) {
      return;
    }
    payPeriodNum = json['payPeriodNum'];
    payPeriodUnit = json['payPeriodUnit'];
    settledBillCount = json['settledBillCount'];
    isDelay = json['isDelay'];
    delayNum = json['delayNum'];
    isIncrementDelay = json['isIncrementDelay'];

    if (json['unSettledBills'] != null) {
      unSettledBills = [];
      json['unSettledBills'].forEach((v) {
        unSettledBills.add(SettleBill.fromJson(v));
      });
    }

    if (json['settledBills'] != null) {
      settledBills = [];
      json['settledBills'].forEach((v) {
        settledBills.add(SettleBill.fromJson(v));
      });
    }
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['payPeriodNum'] = this.payPeriodNum;
    data['payPeriodUnit'] = this.payPeriodUnit;
    data['settledBillCount'] = this.settledBillCount;
    data['isDelay'] = this.isDelay;
    data['delayNum'] = this.delayNum;
    data['isIncrementDelay'] = this.isIncrementDelay;

    if (this.unSettledBills != null) {
      data['unSettledBills'] =
          this.unSettledBills.map((v) => v.toJson()).toList();
    }

    if (this.settledBills != null) {
      data['settledBills'] = this.settledBills.map((v) => v.toJson()).toList();
    }

    return data;
  }
}
