class MockModel {
  MockModel({this.title, this.isSelected});

  MockModel.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    isSelected = json['isSelected'];
  }

  String title;
  int isSelected;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['title'] = this.title;
    data['isSelected'] = this.isSelected;
    return data;
  }
}
