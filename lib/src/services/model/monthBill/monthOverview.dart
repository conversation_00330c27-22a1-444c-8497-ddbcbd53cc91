import 'package:waimai_e_fe_ffw_utils/waimai_e_fe_ffw_utils.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/money.dart';

class DailyBillFeeDetails {
  // 属性
  int billFeeTypeCode;
  String billFeeTypeName;
  int feeAmountSum;
  int feeCount;
  int incomeExpenditureSign;

  // 构造方法
  DailyBillFeeDetails({
    this.billFeeTypeCode,
    this.billFeeTypeName,
    this.feeAmountSum,
    this.feeCount,
    this.incomeExpenditureSign,
  });

  // fromJson方法
  DailyBillFeeDetails.fromJson(Map<String, dynamic> json) {
    billFeeTypeCode = json['billFeeTypeCode'];
    billFeeTypeName = json['billFeeTypeName'];
    feeAmountSum = json['feeAmountSum'];
    feeCount = json['feeCount'];
    incomeExpenditureSign = json['incomeExpenditureSign'];
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['billFeeTypeCode'] = this.billFeeTypeCode;
    data['billFeeTypeName'] = this.billFeeTypeName;
    data['feeAmountSum'] = this.feeAmountSum;
    data['feeCount'] = this.feeCount;
    data['incomeExpenditureSign'] = this.incomeExpenditureSign;

    return data;
  }
}

class BillCategoryItem {
  String categoryName;
  int categoryCode;
  BillCategoryItem(
    String categoryName,
    int categoryCode,
  ) {
    this.categoryName = categoryName;
    this.categoryCode = categoryCode;
  }
}

class BillChargeItem {
  String name;
  String code;
  BillChargeItem(
    String name,
    String code,
  ) {
    this.name = name;
    this.code = code;
  }
}

class BillChargeTypeDetails {
  /// 属性 交易类型 1 -- 外卖订单 2 -- 订单退款 33 -- 服务费返还激励  54 -- 佣金返还类 55 -- 激励返还类
  int billChargeTypeCode;
  String billChargeTypeName;
  int billChargeCount;
  int billChargeAmountSum;
  int orderCategory;
  List<DailyBillFeeDetails> dailyBillFeeDetails;
  /**
   * 是否有问号提示，弹窗说明
   */
  int isShowDetail;
  /**
   * 弹窗具体文案
   */
  String remark;

  // 构造方法
  BillChargeTypeDetails(
      {this.billChargeTypeCode,
      this.billChargeTypeName,
      this.billChargeCount,
      this.billChargeAmountSum,
      this.orderCategory,
      this.dailyBillFeeDetails,
      this.isShowDetail,
      this.remark});

  // fromJson方法
  BillChargeTypeDetails.fromJson(Map<String, dynamic> json) {
    billChargeTypeCode = json['billChargeTypeCode'];
    billChargeTypeName = json['billChargeTypeName'];
    billChargeCount = json['billChargeCount'];
    billChargeAmountSum = json['billChargeAmountSum'];
    orderCategory = json['orderCategory'];
    isShowDetail = json['isShowDetail'];
    remark = json['remark'];

    if (json['dailyBillFeeDetails'] != null) {
      dailyBillFeeDetails = [];
      json['dailyBillFeeDetails'].forEach((v) {
        dailyBillFeeDetails.add(DailyBillFeeDetails.fromJson(v));
      });
    }
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['billChargeTypeCode'] = this.billChargeTypeCode;
    data['billChargeTypeName'] = this.billChargeTypeName;
    data['billChargeCount'] = this.billChargeCount;
    data['billChargeAmountSum'] = this.billChargeAmountSum;
    data['orderCategory'] = this.orderCategory;
    data['isShowDetail'] = this.isShowDetail;
    data['remark'] = this.remark;

    if (this.dailyBillFeeDetails != null) {
      data['dailyBillFeeDetails'] =
          this.dailyBillFeeDetails.map((v) => v.toJson()).toList();
    }

    return data;
  }
}

class MonthOverviewModel {
  // 属性
  int settleState;
  int totalAmount;
  dynamic settleDate;
  dynamic currency;
  int orderNum;
  int refundNum;
  String commissionDesc;
  List<BillChargeTypeDetails> billChargeTypeDetails;

  // 构造方法
  MonthOverviewModel(
      {this.settleState,
      this.totalAmount,
      this.settleDate,
      this.currency,
      this.orderNum,
      this.refundNum,
      this.commissionDesc,
      this.billChargeTypeDetails});

  // fromJson方法
  MonthOverviewModel.fromJson(Map<String, dynamic> json) {
    if (json == null) {
      return;
    }
    settleState = json['settleState'];
    totalAmount = json['totalAmount'];
    settleDate = json['settleDate'];
    currency = json['currency'];
    orderNum = json['orderNum'];
    refundNum = json['refundNum'];
    commissionDesc = json['commissionDesc'];

    if (json['billChargeTypeDetails'] != null) {
      billChargeTypeDetails = [];
      json['billChargeTypeDetails'].forEach((v) {
        billChargeTypeDetails.add(BillChargeTypeDetails.fromJson(v));
      });
    }
  }

  /// 1-对应订单类，3-服务费返还，其他code（0，2等）-其他类
  static Map<int, String> allCodeMap = {
    1: '1,2,16,17',
    2: '3',
    3: '33',
  };

  static getCategoryCode(int category) {
    if (allCodeMap[category] != null) {
      return allCodeMap[category];
    } else {
      // 其他类做兜底
      return allCodeMap[2];
    }
  }

  // 一级Tab筛选 orderCategory 1-订单类 3-服务费返还 其他值都是-其他类
  // 二级Tab筛选 billChargeTypeCode 1-外卖订单 2-订单退款 部分退款 = 16 部分退款冲抵 = 17
  static List<BillChargeTypeDetails> getOrderBillTabList(
      List<BillChargeTypeDetails> billChargeTypeDetails,
      {int category = 1}) {
    List<BillChargeTypeDetails> res = [];
    if (billChargeTypeDetails != null) {
      billChargeTypeDetails.forEach((element) {
        // 其他类的特殊逻辑
        if (category == 2 &&
            (element.billChargeTypeCode == 16 ||
                element.billChargeTypeCode == 17 ||
                element.orderCategory != 1 && element.orderCategory != 3)) {
          res.add(element);
        }
        if (PlatformTools.isPC) {
          if (element.orderCategory == category) {
            res.add(element);
          }
        } else {
          res.add(element);
        }
      });
    }
    return res;
  }

  /// 获取各种类型交易总金额
  static String getSummaryAmount(
      List<BillChargeTypeDetails> billChargeTypeDetails,
      {int category = 1}) {
    List<BillChargeTypeDetails> list =
        getOrderBillTabList(billChargeTypeDetails, category: category);
    int amount = 0;

    if (list.isNotEmpty == true) {
      list.forEach((ele) {
        if (category == 1 && (ele.orderCategory == 1)) {
          amount += ele.billChargeAmountSum;
        }
        if (category == 2 &&
            (ele.billChargeTypeCode == 16 ||
                ele.billChargeTypeCode == 17 ||
                ele.orderCategory != 1 && ele.orderCategory != 3)) {
          amount += ele.billChargeAmountSum;
        }
        if (category == 3 && (ele.orderCategory == 3)) {
          amount += ele.billChargeAmountSum;
        }
      });
      return MoneyTool.formatMoney(amount);
    } else {
      return '0.00';
    }
  }

  // 获取二级Tab内容
  static List<BillChargeItem> getTabCodeName(
      List<BillChargeTypeDetails> billChargeTypeDetails,
      {int category = 1}) {
    List<BillChargeTypeDetails> list =
        getOrderBillTabList(billChargeTypeDetails, category: category);
    List<BillChargeItem> res = [];
    List<int> codeList = [];
    List<BillChargeItem> tabItem = [];
    if (list != null) {
      list.forEach((value) {
        codeList.add(value.billChargeTypeCode);
        tabItem.add(BillChargeItem(
            value.billChargeTypeName, '${value.billChargeTypeCode}'));
      });
    }
    String allCode =
        codeList.isNotEmpty ? codeList.join(',') : getCategoryCode(category);
    res.add(BillChargeItem('全部', allCode));
    res.addAll(tabItem);
    return res;
  }

  /// 只有订单需要 返回对应二级类型下的总和,存在返回null的情况
  static BillChargeTypeDetails getSummaryByBillChargeTypeCode(
      List<BillChargeTypeDetails> billChargeTypeDetails, int billcode) {
    print('object$billChargeTypeDetails');
    List<BillChargeTypeDetails> list =
        getOrderBillTabList(billChargeTypeDetails, category: 1);
    BillChargeTypeDetails newDet;
    // 全部类型
    if (billcode == null) {
      if (list.length == 0) {
        return null;
      } else if (list.length > 0) {
        newDet = list.reduce(
            (value, element) => mergeBillChargeTypeDetails(value, element));
      }
    } else {
      // billcode对应的类型
      int index =
          list.indexWhere((element) => element.billChargeTypeCode == billcode);
      if (index >= 0) {
        return list[index];
      }
    }
    return newDet;
  }

  /// 合并两个BillChargeTypeDetails结构
  static BillChargeTypeDetails mergeBillChargeTypeDetails(
      BillChargeTypeDetails preObj, BillChargeTypeDetails nextObj) {
    List<DailyBillFeeDetails> newFeeObj = [];
    List<DailyBillFeeDetails> preObjFee = preObj?.dailyBillFeeDetails;
    List<DailyBillFeeDetails> nextObjFee = nextObj?.dailyBillFeeDetails;
    if (preObjFee != null && nextObjFee != null) {
      preObjFee.forEach((element) {
        int index = nextObjFee.indexWhere(
            (ele) => ele.billFeeTypeCode == element.billFeeTypeCode);
        if (index >= 0) {
          DailyBillFeeDetails nextObjFeeItem = nextObjFee[index];
          newFeeObj.add(DailyBillFeeDetails(
            billFeeTypeCode: element.billFeeTypeCode,
            billFeeTypeName: element.billFeeTypeName,
            feeAmountSum: element.feeAmountSum + nextObjFeeItem.feeAmountSum,
            feeCount: element.feeCount + nextObjFeeItem.feeCount,
          ));
        } else {
          newFeeObj.add(element);
        }
      });
    } else if (preObjFee != null && nextObjFee == null) {
      newFeeObj = preObjFee;
    } else if (preObjFee == null && nextObjFee != null) {
      newFeeObj = nextObjFee;
    } else {
      newFeeObj = null;
    }

    BillChargeTypeDetails newObj = BillChargeTypeDetails(
      billChargeAmountSum:
          preObj.billChargeAmountSum + nextObj.billChargeAmountSum,
      billChargeCount: preObj.billChargeCount + nextObj.billChargeCount,
      dailyBillFeeDetails: newFeeObj,
    );

    return newObj;
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['settleState'] = this.settleState;
    data['totalAmount'] = this.totalAmount;
    data['settleDate'] = this.settleDate;
    data['currency'] = this.currency;
    data['orderNum'] = this.orderNum;
    data['refundNum'] = this.refundNum;
    data['commissionDesc'] = this.commissionDesc;

    if (this.billChargeTypeDetails != null) {
      data['billChargeTypeDetails'] =
          this.billChargeTypeDetails.map((v) => v.toJson()).toList();
    }

    return data;
  }
}
