class MonthsIncomeOverviewModel {
  // 属性
  String date;
  int amount;

  // 构造方法
  MonthsIncomeOverviewModel({this.date, this.amount});

  // fromJson方法
  MonthsIncomeOverviewModel.fromJson(Map<String, dynamic> json) {
    date = json['date'];
    amount = json['amount'];
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['date'] = this.date;
    data['amount'] = this.amount;
    return data;
  }
}
