class Commodities {
  // 属性
  String commodityName;
  int commodityCount;
  int commodityAmount;
  int commodityType;

  // 构造方法
  Commodities(
      {this.commodityName,
      this.commodityCount,
      this.commodityAmount,
      this.commodityType});

  // fromJson方法
  Commodities.fromJson(Map<String, dynamic> json) {
    commodityName = json['commodityName'];
    commodityCount = json['commodityCount'];
    commodityAmount = json['commodityAmount'];
    commodityType = json['commodityType'];
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['commodityName'] = this.commodityName;
    data['commodityCount'] = this.commodityCount;
    data['commodityAmount'] = this.commodityAmount;
    data['commodityType'] = this.commodityType;
    return data;
  }
}

class ActivityList {
  // 属性
  String activityName;
  int activityAmount;

  // 构造方法
  ActivityList({this.activityName, this.activityAmount});

  // fromJson方法
  ActivityList.fromJson(Map<String, dynamic> json) {
    activityName = json['activityName'];
    activityAmount = json['activityAmount'];
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['activityName'] = this.activityName;
    data['activityAmount'] = this.activityAmount;
    return data;
  }
}

class ActivityInfo {
  // 属性
  int activityTotalAmount;
  List<ActivityList> activityList;

  // 构造方法
  ActivityInfo({this.activityTotalAmount, this.activityList});

  // fromJson方法
  ActivityInfo.fromJson(Map<String, dynamic> json) {
    activityTotalAmount = json['activityTotalAmount'];

    if (json['activityList'] != null) {
      activityList = [];
      json['activityList'].forEach((v) {
        activityList.add(ActivityList.fromJson(v));
      });
    }
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['activityTotalAmount'] = this.activityTotalAmount;

    if (this.activityList != null) {
      data['activityList'] = this.activityList.map((v) => v.toJson()).toList();
    }

    return data;
  }
}

class CommissionList {
  // 属性
  String commissionName;
  int commissionAmount;

  // 构造方法
  CommissionList({this.commissionName, this.commissionAmount});

  // fromJson方法
  CommissionList.fromJson(Map<String, dynamic> json) {
    commissionName = json['commissionName'];
    commissionAmount = json['commissionAmount'];
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['commissionName'] = this.commissionName;
    data['commissionAmount'] = this.commissionAmount;
    return data;
  }
}

class CommissionInfo {
  // 属性
  int commissionTotalAmount;
  List<CommissionList> commissionList;

  // 构造方法
  CommissionInfo({this.commissionTotalAmount, this.commissionList});

  // fromJson方法
  CommissionInfo.fromJson(Map<String, dynamic> json) {
    commissionTotalAmount = json['commissionTotalAmount'];

    if (json['commissionList'] != null) {
      commissionList = [];
      json['commissionList'].forEach((v) {
        commissionList.add(CommissionList.fromJson(v));
      });
    }
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['commissionTotalAmount'] = this.commissionTotalAmount;

    if (this.commissionList != null) {
      data['commissionList'] =
          this.commissionList.map((v) => v.toJson()).toList();
    }

    return data;
  }
}

class BillChargeDetailModel {
  // 属性
  int chargeTypeCode;
  int settleState;
  List<Commodities> commodities;
  ActivityInfo activityInfo;
  CommissionInfo commissionInfo;
  int chargeAmount;
  int tipsAmount;
  int userPayShippingAmount;
  int userOfflinePayAmount;
  String wmOrderViewId;
  int wmOrderDaySeq;
  String wmOrderSubmitedDate;
  String wmOrderCompletedDate;
  dynamic wmOrderRefundedDate;
  dynamic wmOrderCanceledDate;
  dynamic wmOrderCompensatedDate;
  String wmOrderShippingType;
  String wmOrderPayType;
  double wmOrderTotal;
  int wmDonationAmount;
  dynamic insuranceInfo;
  dynamic orderSeqComment;

  // 构造方法
  BillChargeDetailModel(
      {this.chargeTypeCode,
      this.settleState,
      this.commodities,
      this.activityInfo,
      this.commissionInfo,
      this.chargeAmount,
      this.tipsAmount,
      this.userPayShippingAmount,
      this.userOfflinePayAmount,
      this.wmOrderViewId,
      this.wmOrderDaySeq,
      this.wmOrderSubmitedDate,
      this.wmOrderCompletedDate,
      this.wmOrderRefundedDate,
      this.wmOrderCanceledDate,
      this.wmOrderCompensatedDate,
      this.wmOrderShippingType,
      this.wmOrderPayType,
      this.wmOrderTotal,
      this.wmDonationAmount,
      this.insuranceInfo,
      this.orderSeqComment});

  // fromJson方法
  BillChargeDetailModel.fromJson(Map<String, dynamic> json) {
    chargeTypeCode = json['chargeTypeCode'];
    settleState = json['settleState'];

    if (json['commodities'] != null) {
      commodities = [];
      json['commodities'].forEach((v) {
        commodities.add(Commodities.fromJson(v));
      });
    }

    if (json['activityInfo'] != null) {
      ActivityInfo.fromJson(json['activityInfo']);
    }
    if (json['commissionInfo'] != null) {
      CommissionInfo.fromJson(json['commissionInfo']);
    }
    chargeAmount = json['chargeAmount'];
    tipsAmount = json['tipsAmount'];
    userPayShippingAmount = json['userPayShippingAmount'];
    userOfflinePayAmount = json['userOfflinePayAmount'];
    wmOrderViewId = json['wmOrderViewId'];
    wmOrderDaySeq = json['wmOrderDaySeq'];
    wmOrderSubmitedDate = json['wmOrderSubmitedDate'];
    wmOrderCompletedDate = json['wmOrderCompletedDate'];
    wmOrderRefundedDate = json['wmOrderRefundedDate'];
    wmOrderCanceledDate = json['wmOrderCanceledDate'];
    wmOrderCompensatedDate = json['wmOrderCompensatedDate'];
    wmOrderShippingType = json['wmOrderShippingType'];
    wmOrderPayType = json['wmOrderPayType'];
    wmOrderTotal = json['wmOrderTotal'];
    wmDonationAmount = json['wmDonationAmount'];
    insuranceInfo = json['insuranceInfo'];
    orderSeqComment = json['orderSeqComment'];
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['chargeTypeCode'] = this.chargeTypeCode;
    data['settleState'] = this.settleState;

    if (this.commodities != null) {
      data['commodities'] = this.commodities.map((v) => v.toJson()).toList();
    }

    if (this.activityInfo != null) {
      data['activityInfo'] = this.activityInfo.toJson();
    }

    if (this.commissionInfo != null) {
      data['commissionInfo'] = this.commissionInfo.toJson();
    }

    data['chargeAmount'] = this.chargeAmount;
    data['tipsAmount'] = this.tipsAmount;
    data['userPayShippingAmount'] = this.userPayShippingAmount;
    data['userOfflinePayAmount'] = this.userOfflinePayAmount;
    data['wmOrderViewId'] = this.wmOrderViewId;
    data['wmOrderDaySeq'] = this.wmOrderDaySeq;
    data['wmOrderSubmitedDate'] = this.wmOrderSubmitedDate;
    data['wmOrderCompletedDate'] = this.wmOrderCompletedDate;
    data['wmOrderRefundedDate'] = this.wmOrderRefundedDate;
    data['wmOrderCanceledDate'] = this.wmOrderCanceledDate;
    data['wmOrderCompensatedDate'] = this.wmOrderCompensatedDate;
    data['wmOrderShippingType'] = this.wmOrderShippingType;
    data['wmOrderPayType'] = this.wmOrderPayType;
    data['wmOrderTotal'] = this.wmOrderTotal;
    data['wmDonationAmount'] = this.wmDonationAmount;
    data['insuranceInfo'] = this.insuranceInfo;
    data['orderSeqComment'] = this.orderSeqComment;
    return data;
  }
}
