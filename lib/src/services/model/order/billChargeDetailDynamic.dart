import 'dart:convert';

class StepPriceDetailList {
  // 属性
  String title;
  dynamic amount;
  int amountCent;
  String comment;

  // 构造方法
  StepPriceDetailList({this.title, this.amount, this.amountCent, this.comment});

  // fromJson方法
  StepPriceDetailList.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    amount = json['amount'];
    amountCent = json['amountCent'];
    comment = json['comment'];
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['title'] = this.title;
    data['amount'] = this.amount;
    data['amountCent'] = this.amountCent;
    data['comment'] = this.comment;
    return data;
  }
}

class SpDateCommentList {
  // 属性
  String title;
  String amount;
  String comment;

  // 构造方法
  SpDateCommentList({this.title, this.amount, this.comment});

  // fromJson方法
  SpDateCommentList.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    amount = json['amount'];
    comment = json['comment'];
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['title'] = this.title;
    data['amount'] = this.amount;
    data['comment'] = this.comment;
    return data;
  }
}

class SpDateDetailList {
  // 属性
  String title;
  String amount;
  String comment;

  // 构造方法
  SpDateDetailList({this.title, this.amount, this.comment});

  // fromJson方法
  SpDateDetailList.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    amount = json['amount'];
    comment = json['comment'];
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['title'] = this.title;
    data['amount'] = this.amount;
    data['comment'] = this.comment;
    return data;
  }
}

class TechAllowDetail {
  String amount;
  int amountCent;
  String comment;
  String title;
  TechAllowDetail.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    amount = json['amount'];
    amountCent = json['amountCent'];
    comment = json['comment'];
  }
}

class AgreeAllowanceDetail {
  String amount;
  String title;
  String comment;
  AgreeAllowanceDetail.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    amount = json['amount'];
    comment = json['comment'];
  }
}

class CommissionDataList {
  // 属性
  String title;
  String info;
  String amount;
  int amountCent;
  String startPrice;
  dynamic startPriceComment;
  String stepPrice;
  String stepPriceComment;
  List<StepPriceDetailList> stepPriceDetailList;
  List<SpDateCommentList> spDateCommentList;
  List<SpDateDetailList> spDateDetailList;
  int count;
  int remarkLevel;
  int isShowDetail;
  List<TechAllowDetail> techAllowanceDetailList;
  List<AgreeAllowanceDetail> agreeAllowanceDetailList;
  dynamic originalAmount;
  CouponDetail couponDetail;
  List<Tags> tags;

  // 构造方法
  CommissionDataList(
      {this.title,
      this.info,
      this.amount,
      this.amountCent,
      this.startPrice,
      this.startPriceComment,
      this.stepPrice,
      this.stepPriceComment,
      this.stepPriceDetailList,
      this.spDateCommentList,
      this.spDateDetailList,
      this.count,
      this.remarkLevel,
      this.isShowDetail,
      this.techAllowanceDetailList,
      this.agreeAllowanceDetailList,
      this.originalAmount,
      this.couponDetail,
      this.tags});

  // fromJson方法
  CommissionDataList.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    info = json['info'];
    amount = json['amount'];
    amountCent = json['amountCent'];
    startPrice = json['startPrice'];
    startPriceComment = json['startPriceComment'];
    stepPrice = json['stepPrice'];
    stepPriceComment = json['stepPriceComment'];

    if (json['stepPriceDetailList'] != null) {
      stepPriceDetailList = [];
      json['stepPriceDetailList'].forEach((v) {
        stepPriceDetailList.add(StepPriceDetailList.fromJson(v));
      });
    }

    if (json['spDateCommentList'] != null) {
      spDateCommentList = [];
      json['spDateCommentList'].forEach((v) {
        spDateCommentList.add(SpDateCommentList.fromJson(v));
      });
    }
    if (json['spDateDetailList'] != null) {
      spDateDetailList = [];
      json['spDateDetailList'].forEach((v) {
        spDateDetailList.add(SpDateDetailList.fromJson(v));
      });
    }

    if (json['agreeAllowanceDetailList'] != null) {
      agreeAllowanceDetailList = [];
      json['agreeAllowanceDetailList'].forEach((v) {
        agreeAllowanceDetailList.add(AgreeAllowanceDetail.fromJson(v));
      });
    }

    count = json['count'];
    remarkLevel = json['remarkLevel'];
    isShowDetail = json['isShowDetail'];
    // techAllowanceDetailList = json['techAllowanceDetailList'];
    if (json['techAllowanceDetailList'] != null) {
      techAllowanceDetailList = [];
      json['techAllowanceDetailList'].forEach((v) {
        techAllowanceDetailList.add(TechAllowDetail.fromJson(v));
      });
    }
    originalAmount = json['originalAmount'];
    if (json['couponDetail'] != null) {
      couponDetail = CouponDetail.fromJson(json['couponDetail']);
    }

    if (json['tags'] != null) {
      tags = [];
      json['tags'].forEach((v) {
        if (v.startsWith('{')) {
          tags.add(Tags.fromJson(jsonDecode(v)));
        } else {
          tags.add(Tags.fromJson(jsonDecode(
              "{\"bgColor\":\"0x33FFCC33\",\"color\":\"0xFF222222\",\"name\":\"${v}\",\"position\":\"back\",\"weight\":0}")));
        }
      });
    }
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['title'] = this.title;
    data['info'] = this.info;
    data['amount'] = this.amount;
    data['amountCent'] = this.amountCent;
    data['startPrice'] = this.startPrice;
    data['startPriceComment'] = this.startPriceComment;
    data['stepPrice'] = this.stepPrice;
    data['stepPriceComment'] = this.stepPriceComment;

    if (this.stepPriceDetailList != null) {
      data['stepPriceDetailList'] =
          this.stepPriceDetailList.map((v) => v.toJson()).toList();
    }

    if (this.spDateCommentList != null) {
      data['spDateCommentList'] =
          this.spDateCommentList.map((v) => v.toJson()).toList();
    }
    if (this.spDateDetailList != null) {
      data['spDateDetailList'] =
          this.spDateDetailList.map((v) => v.toJson()).toList();
    }
    data['count'] = this.count;
    data['remarkLevel'] = this.remarkLevel;
    data['isShowDetail'] = this.isShowDetail;
    data['techAllowanceDetailList'] = this.techAllowanceDetailList;
    data['agreeAllowanceDetailList'] = this.agreeAllowanceDetailList;
    data['originalAmount'] = this.originalAmount;
    data['couponDetail'] = this.couponDetail;
    if (this.tags != null) {
      data['tags'] = this.tags.map((v) => v.toJson()).toList();
    }
    ;
    return data;
  }
}

class CouponDetail {
  // 属性
  String couponName;
  String couponDesc;
  String appUrl;

  // fromJson方法
  CouponDetail.fromJson(Map<String, dynamic> json) {
    couponName = json['couponName'];
    couponDesc = json['couponDesc'];
    appUrl = json['appUrl'];
  }
  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['couponName'] = this.couponName;
    data['couponDesc'] = this.couponDesc;
    data['appUrl'] = this.appUrl;
    return data;
  }
}

class Tags {
  String position;
  String name;
  num weight;
  // 优惠减免标识的字体颜色
  String color;
  // 优惠减免标识的背景颜色
  String bgColor;
  String icon;
  int code;
  String rowBgColor;
  double width;
  double height;
  Tags(
      {this.position,
      this.name,
      this.weight,
      this.color,
      this.bgColor,
      this.icon,
      this.code,
      this.rowBgColor,
      this.width,
      this.height});

  Tags.fromJson(Map<String, dynamic> json) {
    position = json['position'];
    name = json['name'];
    weight = json['weight'];
    color = json['color'];
    bgColor = json['bgColor'];
    icon = json['icon'];
    code = json['code'];
    rowBgColor = json['rowBgColor'];
    width = json['width'] ?? 0;
    height = json['height'] ?? 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['position'] = this.position;
    data['name'] = this.name;
    data['weight'] = this.weight;
    data['color'] = this.color;
    data['bgColor'] = this.bgColor;
    data['icon'] = this.icon;
    data['code'] = this.code;
    data['rowBgColor'] = this.rowBgColor;
    return data;
  }
}

class SubRelatedInfo {
  int billChargeId;
  int dailyBillDate;
  int chargeTypeCode;
  int wmOrderViewId;
  int appDetailType;
  String appTemplate;
  String appUrl;
  int specialType;
  int isKa;
  int webDetailType;
  String webUrl;
  String webTemplate;

  List<Tags> tags;
  String desc;

  SubRelatedInfo(
      {this.billChargeId,
      this.dailyBillDate,
      this.chargeTypeCode,
      this.wmOrderViewId,
      this.tags,
      this.desc,
      this.appDetailType,
      this.appTemplate,
      this.appUrl,
      this.specialType,
      this.isKa,
      this.webUrl,
      this.webDetailType,
      this.webTemplate});

  // fromJson方法
  SubRelatedInfo.fromJson(Map<String, dynamic> json) {
    billChargeId = json['billChargeId'];
    dailyBillDate = json['dailyBillDate'];
    chargeTypeCode = json['chargeTypeCode'];
    wmOrderViewId = json['wmOrderViewId'];
    if (json['tags'] != null) {
      tags = [];
      json['tags'].forEach((v) {
        tags.add(Tags.fromJson(v));
      });
    }
    desc = json['desc'];
    appDetailType = json['appDetailType'];
    appTemplate = json['appTemplate'];
    appUrl = json['appUrl'];
    specialType = json['specialType'];
    isKa = json['isKa'];
    webUrl = json['webUrl'];
    webDetailType = json['webDetailType'];
    webTemplate = json['webTemplate'];
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['billChargeId'] = this.billChargeId;
    data['dailyBillDate'] = this.dailyBillDate;
    data['chargeTypeCode'] = this.chargeTypeCode;
    data['desc'] = this.desc;
    if (this.tags != null) {
      data['tags'] = this.tags.map((v) => v.toJson()).toList();
    }
    ;
    data['appDetailType'] = this.appDetailType;
    data['appTemplate'] = this.appTemplate;
    data['appUrl'] = this.appUrl;
    data['specialType'] = this.specialType;
    data['isKa'] = this.isKa;
    data['webUrl'] = this.webUrl;
    data['webDetailType'] = this.webDetailType;
    data['webTemplate'] = this.webTemplate;
    return data;
  }
}

class SubDataList {
  // 属性
  String name;
  int count;
  String info;
  List<Tags> tags;
  dynamic originalInfo;
  int amount;
  String remark;
  String originalRemark;
  int remarkLevel;
  dynamic skuDetail;
  List<CommissionDataList> commissionDataList;
  // 是否展示小问号
  int isShowDetail;

  /// 优惠券详情
  CouponDetail couponDetail;

  // 构造方法
  SubDataList({
    this.name,
    this.count,
    this.info,
    this.tags,
    this.originalInfo,
    this.amount,
    this.remark,
    this.originalRemark,
    this.remarkLevel,
    this.skuDetail,
    this.commissionDataList,
    this.isShowDetail,
    this.couponDetail,
  });

  // fromJson方法
  SubDataList.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    count = json['count'];
    info = json['info'];
    originalInfo = json['originalInfo'];
    amount = json['amount'];
    remark = json['remark'];
    originalRemark = json['originalRemark'];
    remarkLevel = json['remarkLevel'];
    skuDetail = json['skuDetail'];
    isShowDetail = json['isShowDetail'];
    if (json['couponDetail'] != null) {
      couponDetail = CouponDetail.fromJson(json['couponDetail']);
    }
    if (json['commissionDataList'] != null) {
      commissionDataList = [];
      json['commissionDataList'].forEach((v) {
        commissionDataList.add(CommissionDataList.fromJson(v));
      });
    }
    if (json['tags'] != null) {
      tags = [];
      json['tags'].forEach((v) {
        if (v.startsWith('{')) {
          tags.add(Tags.fromJson(jsonDecode(v)));
        } else {
          tags.add(Tags.fromJson(jsonDecode(
              "{\"bgColor\":\"0x33FFCC33\",\"color\":\"0xFF222222\",\"name\":\"${v}\",\"position\":\"back\",\"weight\":0}")));
        }
      });
    }
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['name'] = this.name;
    data['count'] = this.count;
    data['info'] = this.info;
    data['originalInfo'] = this.originalInfo;
    data['amount'] = this.amount;
    data['remark'] = this.remark;
    data['originalRemark'] = this.originalRemark;
    data['remarkLevel'] = this.remarkLevel;
    data['skuDetail'] = this.skuDetail;
    data['isShowDetail'] = this.isShowDetail;
    data['couponDetail'] = this.couponDetail;

    if (this.commissionDataList != null) {
      data['commissionDataList'] =
          this.commissionDataList.map((v) => v.toJson()).toList();
    }
    if (this.tags != null) {
      data['tags'] = this.tags.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class DataList {
  // 属性
  String title;
  int version;
  String icon;
  String totalAmount;
  int totalAmountCent;
  String originTotalAmount;
  int originTotalAmountCent;
  List<SubDataList> subDataList;
  int count;
  int remarkLevel;
  String remark;
  List<Tags> tags;
  int isShowDetail;
  CouponDetail couponDetail;
  SubRelatedInfo subRelatedInfo;

  // 构造方法
  DataList(
      {this.title,
      this.version,
      this.icon,
      this.totalAmount,
      this.totalAmountCent,
      this.originTotalAmount,
      this.originTotalAmountCent,
      this.subDataList,
      this.count,
      this.remarkLevel,
      this.remark,
      this.tags,
      this.isShowDetail,
      this.couponDetail,
      this.subRelatedInfo});

  // fromJson方法
  DataList.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    version = json['version'];
    icon = json['icon'];
    totalAmount = json['totalAmount'];
    totalAmountCent = json['totalAmountCent'];
    originTotalAmount = json['originTotalAmount'];
    originTotalAmountCent = json['originTotalAmountCent'];
    if (json['couponDetail'] != null) {
      couponDetail = CouponDetail.fromJson(json['couponDetail']);
    }
    if (json['subDataList'] != null) {
      subDataList = [];
      json['subDataList'].forEach((v) {
        subDataList.add(SubDataList.fromJson(v));
      });
    }

    if (json['subRelatedInfo'] != null) {
      subRelatedInfo = SubRelatedInfo.fromJson(json['subRelatedInfo']);
    }
    if (json['tags'] != null) {
      tags = [];
      json['tags'].forEach((v) {
        if (v.startsWith('{')) {
          tags.add(Tags.fromJson(jsonDecode(v)));
        } else {
          tags.add(Tags.fromJson(jsonDecode(
              "{\"bgColor\":\"0x33FFCC33\",\"color\":\"0xFF222222\",\"name\":\"${v}\",\"position\":\"back\",\"weight\":0}")));
        }
      });
    }

    count = json['count'];
    remarkLevel = json['remarkLevel'];
    remark = json['remark'];
    isShowDetail = json['isShowDetail'];
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['title'] = this.title;
    data['version'] = this.version;
    data['icon'] = this.icon;
    data['totalAmount'] = this.totalAmount;
    data['totalAmountCent'] = this.totalAmountCent;
    data['originTotalAmount'] = this.originTotalAmount;
    data['originTotalAmountCent'] = this.originTotalAmountCent;
    data['isShowDetail'] = this.isShowDetail;
    data['couponDetail'] = this.couponDetail;
    data['subRelatedInfo'] = this.subRelatedInfo;

    if (this.subDataList != null) {
      data['subDataList'] = this.subDataList.map((v) => v.toJson()).toList();
    }
    if (this.tags != null) {
      data['tags'] = this.tags.map((v) => v.toJson()).toList();
    }

    data['count'] = this.count;
    data['remarkLevel'] = this.remarkLevel;
    data['remark'] = this.remark;
    data['isShowDetail'] = this.isShowDetail;
    data['couponDetail'] = this.couponDetail;
    return data;
  }
}

class BillChargeDetailDynamicModel {
  // 属性
  int chargeTypeCode;
  int settleState;
  String wmOrderViewId;
  int wmOrderDaySeq;
  String wmOrderSubmittedDate;
  int wmOrderSubmittedDateTimestamp;
  String wmOrderCompletedDate;
  int wmOrderCompletedDateTimestamp;
  dynamic wmOrderRefundedDate;
  int wmOrderRefundedDateTimestamp;
  dynamic wmOrderCanceledDate;
  int wmOrderCanceledDateTimestamp;
  dynamic wmOrderCompensatedDate;
  int wmOrderCompensatedDateTimestamp;
  String wmOrderShippingType;
  String wmOrderPayType;
  int amount;
  int estimateAmount;
  String dailyBillDate;
  List<DataList> dataList;
  String other;
  String ctime;
  dynamic chargeName;
  dynamic chargeAlias;
  int chargeModel;
  dynamic orderSeqComment;
  dynamic nestDataList;
  String settleNegativeTypeName;
  String associateOrderSeq;
  num dailyBillId;
  int orderType;
  int ctimestamp;

  /// 拼好饭补贴数据
  DataList phfData;

  /// 是否是拼好饭
  bool isPhf;
  String comment;

  // 构造方法
  BillChargeDetailDynamicModel(
      {this.chargeTypeCode,
      this.settleState,
      this.wmOrderViewId,
      this.wmOrderDaySeq,
      this.wmOrderSubmittedDate,
      this.wmOrderSubmittedDateTimestamp,
      this.wmOrderCompletedDate,
      this.wmOrderCompletedDateTimestamp,
      this.wmOrderRefundedDate,
      this.wmOrderRefundedDateTimestamp,
      this.wmOrderCanceledDate,
      this.wmOrderCanceledDateTimestamp,
      this.wmOrderCompensatedDate,
      this.wmOrderCompensatedDateTimestamp,
      this.wmOrderShippingType,
      this.wmOrderPayType,
      this.amount,
      this.estimateAmount,
      this.dailyBillDate,
      this.dataList,
      this.other,
      this.ctime,
      this.chargeName,
      this.chargeAlias,
      this.chargeModel,
      this.orderSeqComment,
      this.nestDataList,
      this.settleNegativeTypeName,
      this.associateOrderSeq,
      this.dailyBillId,
      this.comment,
      this.phfData,
      this.isPhf,
      this.orderType,
      this.ctimestamp});

  // fromJson方法
  BillChargeDetailDynamicModel.fromJson(Map<String, dynamic> json) {
    if (json == null) {
      return;
    }
    chargeTypeCode = json['chargeTypeCode'];
    settleState = json['settleState'];
    wmOrderViewId = json['wmOrderViewId'];
    wmOrderDaySeq = json['wmOrderDaySeq'];
    wmOrderSubmittedDate = json['wmOrderSubmittedDate'];
    wmOrderSubmittedDateTimestamp = json['wmOrderSubmittedDateTimestamp'];
    wmOrderCompletedDate = json['wmOrderCompletedDate'];
    wmOrderCompletedDateTimestamp = json['wmOrderCompletedDateTimestamp'];
    wmOrderRefundedDate = json['wmOrderRefundedDate'];
    wmOrderRefundedDateTimestamp = json['wmOrderRefundedDateTimestamp'];
    wmOrderCanceledDate = json['wmOrderCanceledDate'];
    wmOrderCanceledDateTimestamp = json['wmOrderCanceledDateTimestamp'];
    wmOrderCompensatedDate = json['wmOrderCompensatedDate'];
    wmOrderCompensatedDateTimestamp = json['wmOrderCompensatedDateTimestamp'];
    wmOrderShippingType = json['wmOrderShippingType'];
    wmOrderPayType = json['wmOrderPayType'];
    amount = json['amount'];
    estimateAmount = json['estimateAmount'];
    dailyBillDate = json['dailyBillDate'];
    associateOrderSeq = json['associateOrderSeq'];
    dailyBillId = json['dailyBillId'];
    isPhf = json['isPhf'];
    comment = json['comment'];

    if (json['phfData'] != null) {
      phfData = DataList.fromJson(json['phfData']);
    }

    if (json['dataList'] != null) {
      dataList = [];
      json['dataList'].forEach((v) {
        dataList.add(DataList.fromJson(v));
      });
    }

    other = json['other'];
    ctime = json['ctime'];
    chargeName = json['chargeName'];
    chargeAlias = json['chargeAlias'];
    chargeModel = json['chargeModel'];
    orderSeqComment = json['orderSeqComment'];
    nestDataList = json['nestDataList'];
    settleNegativeTypeName = json['settleNegativeTypeName'];
    orderType = json['orderType'];
    ctimestamp = json['ctimestamp'];
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['chargeTypeCode'] = this.chargeTypeCode;
    data['settleState'] = this.settleState;
    data['wmOrderViewId'] = this.wmOrderViewId;
    data['wmOrderDaySeq'] = this.wmOrderDaySeq;
    data['wmOrderSubmittedDate'] = this.wmOrderSubmittedDate;
    data['wmOrderSubmittedDateTimestamp'] = this.wmOrderSubmittedDateTimestamp;
    data['wmOrderCompletedDate'] = this.wmOrderCompletedDate;
    data['wmOrderCompletedDateTimestamp'] = this.wmOrderCompletedDateTimestamp;
    data['wmOrderRefundedDate'] = this.wmOrderRefundedDate;
    data['wmOrderRefundedDateTimestamp'] = this.wmOrderRefundedDateTimestamp;
    data['wmOrderCanceledDate'] = this.wmOrderCanceledDate;
    data['wmOrderCanceledDateTimestamp'] = this.wmOrderCanceledDateTimestamp;
    data['wmOrderCompensatedDate'] = this.wmOrderCompensatedDate;
    data['wmOrderCompensatedDateTimestamp'] =
        this.wmOrderCompensatedDateTimestamp;
    data['wmOrderShippingType'] = this.wmOrderShippingType;
    data['wmOrderPayType'] = this.wmOrderPayType;
    data['amount'] = this.amount;
    data['estimateAmount'] = this.estimateAmount;
    data['dailyBillDate'] = this.dailyBillDate;
    data['isPhf'] = this.isPhf;
    data['comment'] = this.comment;

    if (this.phfData != null) {
      data['phfData'] = this.phfData.toJson();
    }
    if (this.dataList != null) {
      data['dataList'] = this.dataList.map((v) => v.toJson()).toList();
    }

    data['other'] = this.other;
    data['ctime'] = this.ctime;
    data['chargeName'] = this.chargeName;
    data['chargeAlias'] = this.chargeAlias;
    data['chargeModel'] = this.chargeModel;
    data['orderSeqComment'] = this.orderSeqComment;
    data['nestDataList'] = this.nestDataList;
    data['settleNegativeTypeName'] = this.settleNegativeTypeName;
    data['associateOrderSeq'] = this.associateOrderSeq;
    data['dailyBillId'] = this.dailyBillId;
    data['orderType'] = this.orderType;
    data['ctimestamp'] = this.ctimestamp;
    return data;
  }
}
