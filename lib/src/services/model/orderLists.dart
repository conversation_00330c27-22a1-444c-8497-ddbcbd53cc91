import 'dart:core';

class OrderLists {
  String title;
  String amount;

  OrderLists({this.title, this.amount});

  OrderLists.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    amount = json['amount'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['title'] = this.title;
    data['amount'] = this.amount;
    return data;
  }
}
