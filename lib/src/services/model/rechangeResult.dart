class RechargeResultInfo {
  RechargeResultInfo({
    this.title,
    this.tip,
    this.imageUrl,
  });
  String title;
  String tip;
  String imageUrl;
}

class CouponDetailsModal {
  /// 折扣
  double rate = 0;

  /// 券名称
  String prodName = '';

  /// 最高抵扣金额
  int limitAmount = 0;

  /// 有效期至（例：2022年02月02日）
  String timeLimit = '';

  /// 时间段限制范围 （例：00:00 ～ 24:00）
  String timeRangeList = '';
  CouponDetailsModal.fromJson(Map<String, dynamic> json) {
    if (json == null) {
      return;
    }
    rate = json["rate"];
    prodName = json["prodName"];
    limitAmount = json["limitAmount"];
    timeLimit = json["timeLimit"];
    timeRangeList = json["timeRangeList"];
  }
}

class RechargeResultModal {
  /// 充值是否成功
  bool isSuccess = false;

  /// 充值类型
  int rechargeType = 0;

  /// 推广费账户充值，显示抵用券的查看链接App
  String hrefToCouponApp = '';

  /// 推广费账户充值，显示抵用券的查看链接PC
  String hrefToCouponPC = '';

  /// 抵用券数量
  int couponNum = 0;

  /// 抵用券列表
  List<CouponDetailsModal> couponDetails = [];

  /// 客服电话
  String customerServicePhone;
  RechargeResultModal.fromJson(Map<String, dynamic> json) {
    if (json == null) {
      return;
    }
    isSuccess = json["isSuccess"];
    rechargeType = json["rechargeType"];
    hrefToCouponApp = json["hrefToCouponApp"];
    hrefToCouponPC = json["hrefToCouponPC"];
    couponNum = json["coupon_num"];
    if (json['coupon_details'] != null) {
      couponDetails = [];
      json['coupon_details'].forEach((v) {
        couponDetails.add(CouponDetailsModal.fromJson(v));
      });
    }
  }
}
