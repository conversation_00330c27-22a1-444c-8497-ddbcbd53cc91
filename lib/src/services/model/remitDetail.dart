class DailyBills {
  // 属性
  String dailyBillDate;
  int dailyBillDateTimestamp;
  int dailyBillAmount;
  String currency;

  // 构造方法
  DailyBills(
      {this.dailyBillDate,
      this.dailyBillDateTimestamp,
      this.dailyBillAmount,
      this.currency});

  // fromJson方法
  DailyBills.fromJson(Map<String, dynamic> json) {
    dailyBillDate = json['dailyBillDate'];
    dailyBillDateTimestamp = json['dailyBillDateTimestamp'];
    dailyBillAmount = json['dailyBillAmount'];
    currency = json['currency'];
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['dailyBillDate'] = this.dailyBillDate;
    data['dailyBillDateTimestamp'] = this.dailyBillDateTimestamp;
    data['dailyBillAmount'] = this.dailyBillAmount;
    data['currency'] = this.currency;
    return data;
  }
}

class DetailModel {
  // 属性
  String settleBillStartDate;
  int settleBillStartDateTimestamp;
  String settleBillEndDate;
  int settleBillEndDateTimestamp;
  int settleBillId;
  int billAmount;
  String createTime;
  int createTimestamp;
  String currency;
  List<DailyBills> dailyBills;

  // 构造方法
  DetailModel(
      {this.settleBillStartDate,
      this.settleBillStartDateTimestamp,
      this.settleBillEndDate,
      this.settleBillEndDateTimestamp,
      this.settleBillId,
      this.billAmount,
      this.createTime,
      this.createTimestamp,
      this.currency,
      this.dailyBills});

  // fromJson方法
  DetailModel.fromJson(Map<String, dynamic> json) {
    if (json == null) {
      return;
    }
    settleBillStartDate = json['settleBillStartDate'];
    settleBillStartDateTimestamp = json['settleBillStartDateTimestamp'];
    settleBillEndDate = json['settleBillEndDate'];
    settleBillEndDateTimestamp = json['settleBillEndDateTimestamp'];
    settleBillId = json['settleBillId'];
    billAmount = json['billAmount'];
    createTime = json['createTime'];
    createTimestamp = json['createTimestamp'];
    currency = json['currency'];

    if (json['dailyBills'] != null) {
      dailyBills = [];
      json['dailyBills'].forEach((v) {
        dailyBills.add(DailyBills.fromJson(v));
      });
    }
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['settleBillStartDate'] = this.settleBillStartDate;
    data['settleBillStartDateTimestamp'] = this.settleBillStartDateTimestamp;
    data['settleBillEndDate'] = this.settleBillEndDate;
    data['settleBillEndDateTimestamp'] = this.settleBillEndDateTimestamp;
    data['settleBillId'] = this.settleBillId;
    data['billAmount'] = this.billAmount;
    data['createTime'] = this.createTime;
    data['createTimestamp'] = this.createTimestamp;
    data['currency'] = this.currency;

    if (this.dailyBills != null) {
      data['dailyBills'] = this.dailyBills.map((v) => v.toJson()).toList();
    }

    return data;
  }
}
