import 'package:waimai_e_fe_flutter_finance/src/services/model/dailyBill/billChargeList.dart';

class OverviewDetailModal {
  int settleState;
  int totalAmount;
  int orderNum;
  String settleDate;
  String currency;
  int refundNum;
  List<BillChargeType> billChargeTypeDetails = [];
  // fromJson方法
  OverviewDetailModal.fromJson(Map<String, dynamic> json) {
    if (json == null) {
      return;
    }
    settleState = json['settleState'];
    totalAmount = json['totalAmount'];
    orderNum = json['orderNum'];
    settleDate = json['settleDate'];
    currency = json['currency'];
    refundNum = json['refundNum'];

    if (json['billChargeTypeDetails'] != null) {
      billChargeTypeDetails = [];
      json['billChargeTypeDetails'].forEach((v) {
        billChargeTypeDetails.add(BillChargeType.fromJson(v));
      });
    }
  }
}

class BillChargeType {
  int billChargeTypeCode;
  int billChargeCount;
  int billChargeAmountSum;
  int orderCategory;
  String billChargeTypeName;
  // dailyBillFeeDetails

  BillChargeType.fromJson(Map<String, dynamic> json) {
    billChargeTypeCode = json['billChargeTypeCode'];
    billChargeCount = json['billChargeCount'];
    billChargeAmountSum = json['billChargeAmountSum'];
    orderCategory = json['orderCategory'];
    billChargeTypeName = json['billChargeTypeName'];
  }
}

class BillChargeListModal {
  List<BillChargeModel> billChargeList;

  BillChargeListModal.fromJson(List json) {
    if (json != null) {
      billChargeList = [];
      json.forEach((v) {
        billChargeList.add(BillChargeModel.fromJson(v));
      });
    }
  }
}
