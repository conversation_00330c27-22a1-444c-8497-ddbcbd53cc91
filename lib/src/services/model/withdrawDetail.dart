class WithdrawFlows {
  // 属性
  int withdrawStatus;
  String displayMsg;
  String withdrawTime;
  int withdrawTimestamp;
  String displayComment;

  // 构造方法
  WithdrawFlows(
      {this.withdrawStatus,
      this.displayMsg,
      this.withdrawTime,
      this.withdrawTimestamp,
      this.displayComment});

  // fromJson方法
  WithdrawFlows.fromJson(Map<String, dynamic> json) {
    withdrawStatus = json['withdrawStatus'];
    displayMsg = json['displayMsg'];
    withdrawTime = json['withdrawTime'];
    withdrawTimestamp = json['withdrawTimestamp'];
    displayComment = json['displayComment'];
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['withdrawStatus'] = this.withdrawStatus;
    data['displayMsg'] = this.displayMsg;
    data['withdrawTime'] = this.withdrawTime;
    data['withdrawTimestamp'] = this.withdrawTimestamp;
    data['displayComment'] = this.displayComment;
    return data;
  }
}

class WithdrawDetailModel {
  // 属性
  int accFlowType;
  String flowNo;
  dynamic bankAccountName;
  dynamic bankName;
  dynamic cardNumber;
  int payBindType;
  int walletId;
  String walletAccountName;
  String walletUrl;
  String moneyStr;
  int moneyCent;
  String cTime;
  String comment;
  String mobile;
  List<WithdrawFlows> withdrawFlows;

  // 构造方法
  WithdrawDetailModel(
      {this.accFlowType,
      this.flowNo,
      this.bankAccountName,
      this.bankName,
      this.cardNumber,
      this.payBindType,
      this.walletId,
      this.walletAccountName,
      this.walletUrl,
      this.moneyStr,
      this.moneyCent,
      this.cTime,
      this.comment,
      this.mobile,
      this.withdrawFlows});

  // fromJson方法
  WithdrawDetailModel.fromJson(Map<String, dynamic> json) {
    accFlowType = json['accFlowType'];
    flowNo = json['flowNo'];
    bankAccountName = json['bankAccountName'];
    bankName = json['bankName'];
    cardNumber = json['cardNumber'];
    payBindType = json['payBindType'];
    walletId = json['walletId'];
    walletAccountName = json['walletAccountName'];
    walletUrl = json['walletUrl'];
    moneyStr = json['moneyStr'];
    moneyCent = json['moneyCent'];
    cTime = json['cTime'];
    comment = json['comment'];
    mobile = json['mobile'];

    if (json['withdrawFlows'] != null) {
      withdrawFlows = [];
      json['withdrawFlows'].forEach((v) {
        withdrawFlows.add(WithdrawFlows.fromJson(v));
      });
    }
  }

  // toJson方法
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['accFlowType'] = this.accFlowType;
    data['flowNo'] = this.flowNo;
    data['bankAccountName'] = this.bankAccountName;
    data['bankName'] = this.bankName;
    data['cardNumber'] = this.cardNumber;
    data['payBindType'] = this.payBindType;
    data['walletId'] = this.walletId;
    data['walletAccountName'] = this.walletAccountName;
    data['walletUrl'] = this.walletUrl;
    data['moneyStr'] = this.moneyStr;
    data['moneyCent'] = this.moneyCent;
    data['cTime'] = this.cTime;
    data['comment'] = this.comment;
    data['mobile'] = this.mobile;

    if (this.withdrawFlows != null) {
      data['withdrawFlows'] =
          this.withdrawFlows.map((v) => v.toJson()).toList();
    }

    return data;
  }
}
