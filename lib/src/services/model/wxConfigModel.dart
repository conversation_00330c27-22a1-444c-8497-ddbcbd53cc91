class WxConfigModel {
  WxConfigModel({
    this.title,
    this.subTitle,
    this.iconUrl,
    this.contentId,
    this.contentType,
  });

  String title;
  String subTitle;
  String iconUrl;
  num contentId;
  num contentType;
}

class WxShareConfigResponse {
  WxShareConfigResponse({
    bool debug,
    String appId,
    String nonceStr,
    String signature,
    num timestamp,
    String url,
    List<String> jsApiList,
  });

  WxShareConfigResponse.fromJson(Map<String, dynamic> json) {
    debug = json['debug'];
    appId = json['appId'];
    nonceStr = json['nonceStr'];
    signature = json['signature'];
    timestamp = json['timestamp'];
    url = json['url'];
    jsApiList = json['jsApiList'];
  }

  /// 开启调试模式时,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
  bool debug;
  String appId;
  String nonceStr;
  String signature;
  num timestamp;
  String url;
  List<String> jsApiList;
}
