// ignore_for_file: missing_return

import 'package:waimai_e_fe_flutter_finance/src/tools/third_part/date_format/date_format.dart';

class DateFormat {
  static Map<int, String> weekDayMap = {
    1: '星期一',
    2: '星期二',
    3: '星期三',
    4: '星期四',
    5: '星期五',
    6: '星期六',
    7: '星期日',
  };
  static int _formatTm(int tm) {
    if (tm == null) {
      return DateTime.now().millisecondsSinceEpoch;
    }
    String tmStr = '$tm';
    if (tmStr.length == 10) {
      tm *= 1000;
    }
    return tm;
  }

  static bool getIsToday(int tm) {
    int now = DateTime.now().millisecondsSinceEpoch;
    int days = ((now - tm) / (60 * 60 * 24 * 1000)).floor();
    return days == 0;
  }

  // 判断是今日/昨日
  static String getIsday(int tm) {
    var today = DateTime.now();
    var standardDate = DateTime(today.year, today.month, today.day, 23, 59, 59);
    var time = formatFulls(tm);
    Duration diff = standardDate.difference(DateTime.parse(time));
    if (diff >= Duration(days: 1) && diff < Duration(days: 2)) {
      return '昨日 ';
    } else if (diff < Duration(days: 1)) {
      return '今日';
    }
    return '';
  }

  /// 格式化成 年月日
  static forTimeString(int tm) {
    DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(tm);
    return '${dateTime.year}年${dateTime.month}月${dateTime.day}日';
  }

  /// 格式化成 时分秒 ==> 11:22:33
  static forTimeStringHMS(int tm) {
    DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(tm);
    return '${dateTime.hour}:${dateTime.minute}:${dateTime.second}';
  }

  /// 格式化成：2019/09/03，传 null 为格式化当前时间
  static formatFull(int tm, {String splitStr}) {
    tm = _formatTm(tm);
    String spiltstr = splitStr ?? "/";

    return formatDate(DateTime.fromMillisecondsSinceEpoch(tm),
        [yyyy, spiltstr, mm, spiltstr, dd, " ", HH, ":", nn, ":", ss]);
  }

  /// 格式化成：2019-09-03，传 null 为格式化当前时间
  static formatFulls(int tm, {String splitStr}) {
    tm = _formatTm(tm);
    String spiltstr = splitStr ?? "-";

    return formatDate(DateTime.fromMillisecondsSinceEpoch(tm),
        [yyyy, spiltstr, mm, spiltstr, dd, " ", HH, ":", nn, ":", ss]);
  }

  /// 格式化成：2019/09/03，传 null 为格式化当前时间
  static String formatYYYYMMDD(int tm, {String splitStr}) {
    tm = _formatTm(tm);
    String spiltstr = splitStr ?? "/";

    return formatDate(DateTime.fromMillisecondsSinceEpoch(tm),
        [yyyy, spiltstr, mm, spiltstr, dd]);
  }

  /// 格式化成：2019-09-03，传 null 为格式化当前时间
  static String formatXfYYYYMMDD(int tm, {String splitStr}) {
    tm = _formatTm(tm);
    String spiltstr = splitStr ?? "-";

    return formatDate(DateTime.fromMillisecondsSinceEpoch(tm),
        [yyyy, spiltstr, mm, spiltstr, dd]);
  }

  /// 格式化成：09/03，传 null 为格式化当前时间
  static formatMMDD(int tm) {
    tm = _formatTm(tm);
    return formatDate(DateTime.fromMillisecondsSinceEpoch(tm), [mm, "/", dd]);
  }

  /// 格式化成：09-03，传 null 为格式化当前时间
  static formatMMDDs(int tm) {
    tm = _formatTm(tm);
    return formatDate(DateTime.fromMillisecondsSinceEpoch(tm), [mm, "-", dd]);
  }

  /// 格式化成：09/03，传 null 为格式化当前时间
  static formatYYYYMM(DateTime dateTime) {
    return formatDate(dateTime, [yyyy, "/", mm]);
  }

  /// 格式化成：09-03，传 null 为格式化当前时间
  static formatYYYYMMs(DateTime dateTime) {
    return formatDate(dateTime, [yyyy, "-", mm]);
  }

  /// 格式化成：2023-09-03，传 null 为格式化当前时间
  static formatYYYYMMDDs(DateTime dateTime) {
    return formatDate(dateTime, [yyyy, "-", mm, "-", dd]);
  }

  static formatMMWithMonth(DateTime dateTime) {
    return formatDate(dateTime, [mm]) + '月';
  }

  /// 格式化成：09，传 null 为格式化当前时间
  static formatMM(int tm) {
    tm = _formatTm(tm);
    return formatDate(DateTime.fromMillisecondsSinceEpoch(tm), [mm]);
  }

  /// 把字符串转成时间戳，比如：09/24 或 2020-08-24 --> 1567440000
  static formatStringToTm(String tmStr) {
    if (tmStr != null) {
      List<String> list = tmStr.split('/');
      if (list.length == 1) {
        list = tmStr.split('-');
      }
      if (list.length < 2) {
        throw Error.safeToString('$tmStr - 参数不正确，如: 2019/10/1 or 10/01');
      } else {
        list = list.reversed.toList();
        int day = int.parse(list[0]);
        int month = int.parse(list[1]);
        int year = list[2] != null ? int.parse(list[2]) : DateTime.now().year;
        DateTime dateTime = DateTime(year, month, day);
        return dateTime.millisecondsSinceEpoch;
      }
    } else {
      throw Error.safeToString('$tmStr - 参数不能为空，如: 2019/10/1 or 10/01');
    }
  }

  /// 把字符串转成时间戳，比如：09/24 或 2020-08-24 --> DateTime
  static DateTime formatStringToDateTime(String tmStr) {
    int tm = formatStringToTm(tmStr);
    return DateTime.fromMillisecondsSinceEpoch(tm);
  }

  static String getDay(String tmStr) {
    DateTime dt = formatStringToDateTime(tmStr);
    return weekDayMap[dt.weekday];
  }

  /// 格式化开始和结束日期，比如：2020-10-01 2020-10-03 --> 2020/10/01 至 10/03
  static formatStartEndDate(String startDate, String endDate) {
    int startTm = formatStringToTm(startDate);
    int endTm = formatStringToTm(endDate);
    return formatYYYYMMDD(startTm) + ' 至 ' + formatMMDD(endTm);
  }

  /// 替换日期字符串的分隔符号，比如：2020-02-09 ---> 2020/02/09
  static String changeDateSplitChar(String tmStr) {
    return tmStr.replaceAll(RegExp(r'-'), '/');
  }

  /// 获取四个月前的今天
  static int get90DayAgo() {
    return getSomeDayAgo(90);
  }

  static int get120DayAgo() {
    return getSomeDayAgo(120);
  }

  static int threeMonthsAgo() {
    DateTime curNow = DateTime.now();
    DateTime threeMonthsAgo =
        DateTime(curNow.year, curNow.month - 3, curNow.day);
    return threeMonthsAgo.millisecondsSinceEpoch;
  }

  static int getSomeDayAgo(int count) {
    DateTime now = DateTime.now();
    DateTime fourMonthAgo = DateTime.utc(now.year, now.month, now.day - count);
    return fourMonthAgo.millisecondsSinceEpoch;
  }

  static List<String> getFirstLastDay(DateTime dateTime) {
    DateTime start = DateTime(dateTime.year, dateTime.month, 1);
    DateTime nextMonth = DateTime(dateTime.year, dateTime.month + 1, 1);
    DateTime end = DateTime.fromMillisecondsSinceEpoch(
        nextMonth.millisecondsSinceEpoch - 1);
    return [
      formatYYYYMMDD(start.millisecondsSinceEpoch),
      formatYYYYMMDD(end.millisecondsSinceEpoch)
    ];
  }

  static changeSplitChar(String datetime) {
    if (datetime != null) {
      return datetime.replaceAll(RegExp(r'/'), '-');
    } else {
      return datetime;
    }
  }
}
