import 'package:flutter/material.dart';

// 黄色渐变值
const LinearGradient yellowGradient = LinearGradient(
  begin: Alignment.centerLeft,
  end: Alignment.centerRight,
  colors: [
    Color(0xFFFFE862),
    Color(0xFFFFCC33),
  ],
);

// 灰色渐变值
const LinearGradient grayGradient = LinearGradient(
  begin: Alignment.bottomCenter,
  end: Alignment.topCenter,
  colors: [
    Color(0xFFF8F8FC),
    Color(0xFFFBFBFD),
  ],
);

const Color title1 = Color(0xFF222222);
const Color title2 = Color(0xFF999999);
const Color titleRed = Color(0xFFFF192D);

/// 绿色金额颜色
const Color greenMoney = Color(0xFF00BF7F);

/// 禁止点击的背景颜色
const Color disabledBgColor = Color(0xFFEEEEEE);

/// 禁止点击的文本颜色
const Color disabledTextColor = Color(0xFF999999);

/// 黑色背景
const Color blackBgColor = Color(0xFF000000);

/// 黑色字体
const Color commonTextColor = Color(0xFF222222);
