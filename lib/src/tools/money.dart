class MoneyTool {
  // 给的金额字符串(可带符号)添加千位分隔符 '12344.00' => '12,344.00'
  static _thousands(String str) {
    if (str == null) {
      return '0.0';
    }
    // + - 号
    String sign = '';
    String firstWord = str.substring(0, 1);
    if (firstWord == '+' || firstWord == '-') {
      sign = firstWord;
      str = str.substring(1);
    }
    int idx = str.indexOf('.', 0);
    // 切分整数和(带小数点)小数部分
    String intStr = '';
    String decStr = '';

    if (idx > -1) {
      intStr = str.substring(0, idx);
      decStr = str.substring(idx);
    } else {
      intStr = str;
      decStr = '';
    }
    idx = intStr.length % 3;

    List<String> list = [];
    if (idx > 0) {
      list.add(str.substring(0, idx));
    }
    for (int i = idx; i < intStr.length; i += 3) {
      list.add(str.substring(i, i + 3));
    }
    return '$sign${list.join(',')}$decStr';
  }

  /// 格式化金额
  /// [money] 要格式化的金额：10000-->100,00 -10000-->-100,00
  static formatMoney(int money) {
    String result = '0.00';
    if (money != null) {
      result = _thousands((money / 100).toStringAsFixed(2));
    }
    return result;
  }

  /// 格式化金额
  /// [money] 要格式化的金额：10000-->100,00元 -10000-->-100,00元
  static formatMoneyWithYuan(num money) {
    String result = '0.00';
    if (money != null) {
      result = _thousands((money / 100).toStringAsFixed(2));
    }
    return result;
  }

  /// 格式化字符串类型的金额，添加前缀/后缀
  static formatMoneyWithPrefixAndSuffix(String money,
      {bool needPrefix = false, String suffix = '元'}) {
    String res = formatMoneyWithYuan(toNum(money));
    if (needPrefix && toNum(money) > 0) {
      return '+$res$suffix';
    }
    return '$res$suffix';
  }

  /// 格式化金额 无前面正负号
  /// [money] 要格式化的金额：-10000 --> ¥100,00
  static formatMoneyNoPrex(int money) {
    String result = '0.00';
    if (money != null) {
      result = _thousands((money.abs() / 100).toStringAsFixed(2));
    }
    return result;
  }

  /// 格式化金额，带单位，
  /// [money] 要格式化的金额，比如：：100 --> +¥100,00
  static formatMoneyWithPrex(int money) {
    String prex = '';
    if (money != null) {
      if (money > 0) {
        prex = '+';
      } else if (money < 0) {
        prex = '-';
      }
    }
    return prex + '¥' + formatMoneyNoPrex(money);
  }

  // static bool formatMoney() {}

  /// 金额正则，保留两位小数
  static isMoneyFormat(value) {
    RegExp exp = RegExp(r"^[0-9]+(\.[0-9]{1,2})?$");
    return exp.hasMatch(value);
  }

  /// 判断一个字符串是正值还是负值
  static isPlus(String value) {
    if (value == null) {
      return true;
    }
    String prex = value.substring(0, 1);
    if (prex == '￥' || prex == '+') {
      return true;
    } else {
      return false;
    }
  }

  /// 把后端返回的字符串钱数，转成int，如："-￥10.80" --> -1080
  static formatStringToInt(String value) {
    if (value == null) {
      return 0;
    }
    String newVale = value
        .replaceAll('+', '')
        .replaceAll('-', '')
        .replaceAll('￥', '')
        .replaceAll(',', '')
        .replaceAll('.', '');
    int parsedInt = int.tryParse(newVale);
    if (parsedInt != null) {
      if (isPlus(value)) {
        return parsedInt;
      } else {
        return parsedInt * -1;
      }
    }
    return 0;
  }

  // 把非num类型转成num
  static num toNum(String value) {
    num returnVal = 0;
    if (value != null && value.trim() != '') {
      if (value.runtimeType.toString() == 'String') {
        // 转成int，用于使用map来取值
        returnVal = num.tryParse(value);
      }
    }
    return returnVal;
  }
}
