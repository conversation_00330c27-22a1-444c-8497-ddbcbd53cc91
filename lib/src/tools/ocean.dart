// 账户结算信息
mixin AccountDetailsMixin {
  String get cid => 'c_waimai_e_n2ptf4p1';
  String get pageKeyInfo => 'AccountDetailsPage';
}

// 账户详情
mixin AccountInfoMixin {
  String get cid => 'c_waimai_e_ye4wxiug';
  String get pageKeyInfo => 'AccountInfoPage';
}

// 余额流水
mixin BalanceFlowMixin {
  String get cid => 'c_waimai_e_7r8hh9e9';
  String get pageKeyInfo => 'BalanceFlowPage';
}

// 余额提现
mixin BalanceWithdrawMixin {
  String get cid => 'c_waimai_e_9kgh0ntg';
  String get pageKeyInfo => 'BalanceWithdrawPage';
}

// 首页
mixin HomeMixin {
  String get cid => 'c_waimai_e_u8bmfo56';
  String get pageKeyInfo => 'HomePage';
}

// 日账单
mixin DailyBillMixin {
  String get cid => 'c_waimai_e_2q9g177v';
  String get pageKeyInfo => 'MonthBillPage';
}

// 月账单
mixin MonthBillMixin {
  String get cid => 'c_waimai_e_vvlj4den';
  String get pageKeyInfo => 'MonthBillPage';
}

// 订单查询
mixin OrderQueryMixin {
  String get cid => 'c_waimai_e_e3aftf11';
  String get pageKeyInfo => 'OrderQueryPage';
}

// 提现详情
mixin ExportDetailMixin {
  String get cid => 'c_waimai_e_p7106rn9';
  String get pageKeyInfo => 'ExportDetailPage';
}

// 结算详情
mixin RemitDetailMixin {
  String get cid => 'c_waimai_e_33hlg33n';
  String get pageKeyInfo => 'RemitDetailPage';
}

// 交易类型
mixin TradingTypeMixin {
  String get cid => 'c_waimai_e_kj57nuct';
  String get pageKeyInfo => 'TradingTypePage';
}

// 充值页面
mixin RechargeMixin {
  String get cid => 'c_waimai_e_ax7t959h';
  String get pageKeyInfo => 'BalanceRechargePage';
}

// 订单页面

mixin OrderListMixin {
  String get cid => 'c_waimai_e_ewfauqtr';
  String get pageKeyInfo => 'OrderListPage';
}
