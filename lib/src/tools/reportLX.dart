import 'package:flutter_lx/channel/statistics_lx.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/util/index.dart';

class ReportLX {
  static Future<List<String>> _getInfo() {
    // return Util.getPoiId().then((wmPoiIdValue) {
    //   wmPoiId = wmPoiIdValue;
    //   print('----------- getInfo ----------- $wmPoiId');
    //   Util.getAcctId().then((acctIdValue) {
    //     acctId = acctIdValue;
    //     print('----------- getInfo ----------- $acctId');
    //     return Future.value(1);
    //   });
    // });
    return Future.wait([Util.getPoiId(), Util.getAcctId()]);
  }

  // 获取 poi_id\acctId等通参
  static Future<Map<String, dynamic>> getValLab(
      {isPV = false, Map<String, dynamic> customVal}) async {
    List<String> res = await _getInfo();
    String wmPoiId = res[0];
    String acctId = res[1];

    print('ReportLX ----- $wmPoiId $acctId');
    customVal = customVal ?? {};
    if (isPV) {
      customVal.addAll({'poi_id': wmPoiId});
      customVal.addAll({
        "custom": {"acctId": acctId, "wmPoiId": wmPoiId}
      });
    } else {
      customVal.addAll({'acctId': acctId});
      customVal.addAll({'poi_id': wmPoiId});
      customVal.addAll({'wmPoiId': wmPoiId});
    }
    return Future.value(customVal);
  }

  static void mc(String pageKeyInfo, String cid, String bid,
      {Map<String, dynamic> val}) {
    getValLab(isPV: false, customVal: val).then((value) {
      print('value $value ${value.runtimeType}');
      FlutterLx.moudleClick(pageKeyInfo, cid, bid,
          channel: 'waimai_e', val: value);
    });
  }

  static void mv(String pageKeyInfo, String cid, String bid,
      {Map<String, dynamic> val}) {
    getValLab(isPV: false, customVal: val).then((value) => FlutterLx.moudleView(
        pageKeyInfo, cid, bid,
        channel: 'waimai_e', val: value));
  }

  static void pv(String pageKeyInfo, String cid, {Map<String, dynamic> val}) {
    getValLab(isPV: true, customVal: val).then((value) =>
        FlutterLx.pageView(pageKeyInfo, cid, channel: 'waimai_e', val: value));
  }

  static void pageDidDisappear(String pageKeyInfo, String cid,
      {Map<String, dynamic> val}) {
    getValLab(isPV: true, customVal: val).then((value) =>
        FlutterLx.pageDidDisappear(pageKeyInfo, cid,
            channel: 'waimai_e', val: value));
  }
}
