import 'package:flap/flap.dart';
import 'package:flutter/material.dart';
import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_fe_ffw_utils/platform_tool.dart';
import 'package:waimai_e_fe_flutter_finance/main.route.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/util/index.dart';

class WMESchemeUrls {
  static final flutterPackageChannel = 'waimai_e_flutter';

  static String flutterPageUrl(
    String route, {
    Map<String, dynamic> params,
  }) {
    return SchemeUrls.flutterPageUrl(route,
        params: params, channel: flutterPackageChannel);
  }
}

class RouterTools {
  /// TODO 上线的时候 务必要改成 false！！！！！！
  static bool isDev = false;

  static Future<String> _initBaseUrl() {
    // return WaimaiENativeBusiness.getEnvUrl('https://waimaieapp.meituan.com/');
    return Future.value('https://waimaieapp.meituan.com/');
  }

  /// 对 SchemeUrls.flutterPageUrl 的二次封装，用于项目单独 Attach
  static flutterPageUrl(
    BuildContext context,
    String route, {
    String channel,
    Map<String, dynamic> params,
    Function callback,
  }) async {
    String wmPoiId = await Util.getPoiId();
    if (params == null) {
      params = Map();
    }
    params.putIfAbsent('wmPoiId', () => wmPoiId);

    print('kFlapMode $kFlapMode');
    // 完整的 itakeawaybiz 路径，直接跳转
    if (route != null && route.startsWith('itakeawaybiz')) {
      print('Direct itakeawaybiz path $route');
      RouteUtils.open(route);
    } else if (kFlapMode) {
      String newRoute = route.substring(2);
      String first = route.substring(1, 2).toUpperCase();
      String flapEntry = first + newRoute + 'Page';
      print('flap isDev $isDev - $flapEntry - $params');
      if (isDev) {
        return _openDevFlap(context, flapEntry, params);
      } else {
        _openProdFlap(flapEntry, params);
      }
    } else {
      route = '/finance$route';
      print('AOT route - $route - $params');
      // Navigator.pushNamed(context, route, arguments: params);
      return Navigator.push(
        context,
        MaterialPageRoute(
          builder: $pages[route],
          settings: RouteSettings(name: route, arguments: params),
        ),
      ).then((value) {
        print(value);
        if (callback != null) {
          callback(value);
        }
      });
    }
  }

  static _openProdFlap(String flapEntry, Map<String, dynamic> params) {
    Map<String, dynamic> _p = params ?? Map<String, dynamic>();
    _p['flap_id'] = 'finance';
    _p['flap_entry'] = flapEntry;
    String url = SchemeUrls.flutterPageUrl(
      'flap',
      params: _p,
      channel: '',
    );
    return RouteUtils.open('$url&moduleName=waimai_e_fe_flutter_finance');
  }

  static _openDevFlap(
      BuildContext context, String flapEntry, Map<String, dynamic> params) {
    // return Navigator.push(
    //   context,
    //   MaterialPageRoute(
    //     builder: (context) => FlapWidgetContainer(
    //       widgetId: 'finance',
    //       entryName: flapEntry,
    //       moduleName: 'waimai_e_fe_flutter_finance',
    //     ),
    //   ),
    // );
  }

  static openWebPageUrl(String path, {Map params}) async {
    if (path == null || path == '') {
      print('path is empty');
      return;
    }
    List<String> list = [];
    params?.forEach((key, value) {
      list.add('$key=$value');
    });
    String paramStr = list.join('&');
    path = '$path${paramStr.length > 0 ? '?' : ''}$paramStr';
    // 解决 /abc 类型的相对路径直接跳转会出错的问题，需要加上协议和域名
    if (!path.startsWith('http')) {
      path = path.substring(1);
      String webBaseUrl = await _initBaseUrl();
      path = '$webBaseUrl$path';
      _nativeGo(path);
    } else {
      // 必须这样写，如果按下面的写法flap在if后就不再执行
      _nativeGo(path);
    }
    // _nativeGo(path);
  }

  static _nativeGo(String path) {
    if (PlatformTools.isPC) {
      Util.href(path);
    } else {
      String url = SchemeUrls.webPageUrl(path);
      RouteUtils.open(url);
    }
  }
}
