import 'package:mt_flutter_route/mt_flutter_route.dart';
import 'package:waimai_e_common_utils/waimai_e_common_utils.dart';
import 'package:waimai_e_fe_ffw_utils/platform_tool.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/util/index.dart';

class RouterTools {
  static initRouterInfo() {}

  static flutterPageUrl(
    dynamic context,
    String route, {
    String channel,
    Map<String, dynamic> params,
    Function callback,
  }) async {
    route = 'finance$route';
    print('webview - route - $route');
    final url = SchemeUrls.flutterPageUrl(route,
        channel: 'waimai_e_flutter', params: params);
    print('webview - url - $url');
    RouteUtils.open(url).then((value) {
      print(value);
      if (callback != null) {
        callback(value);
      }
    });
    // Navigator.pushNamed(context, route, arguments: params);
  }

  static openWebPageUrl(String path, {Map params}) async {
    if (path == null || path == '') {
      print('path is empty');
      return;
    }
    List<String> list = [];
    params?.forEach((key, value) {
      list.add('$key=$value');
    });
    String paramStr = list.join('&');
    path = '$path${paramStr.length > 0 ? '?' : ''}$paramStr';
    print('web path=$path');
    if (PlatformTools.isPC) {
      Util.href(path);
    } else {
      RouteUtils.open(path);
    }
  }
}
