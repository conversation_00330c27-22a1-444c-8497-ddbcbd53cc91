import 'dart:async';
import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:js/js.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/aigcAnalysis.dart';
import 'package:wef_network/wef_request.dart';

@JS('fetchStream')
external Stream<String> fetchStream(
    String url, String data, Function streamcallback, Function errorHandler);

void $fetchStream(
    String url, Map data, StreamController<ResponseData> controller) async {
  void read(String value) {
    if (controller.isClosed) {
      return;
    }

    Map parsedData;
    try {
      // 数据返回格式为 data: {}，所以需要从第 6 位开始截取
      String data = value.substring(6);
      if (data.startsWith(':')) {
        data = data.substring(1).trim();
      }
      parsedData = json.decode(data);
    } catch (error) {
      debugPrint('stream 解析 json 错误:${error}');
      EasyLoading.showToast('网络异常，请稍后重试');
      controller.addError(StreamError(1, 'stream 解析 json 发生错误'));
    }

    if (parsedData != null) {
      ResponseData responseData = ResponseData.fromJson(parsedData);
      if (responseData.code == 0) {
        controller.add(responseData);
      } else {
        debugPrint(
            'readStream失败: 状态码-${responseData.code} 错误信息-${responseData.msg}');
        EasyLoading.showToast('网络异常，请稍后重试');
        controller.addError(StreamError(1, 'response code不为 0'));
      }
    }
  }

  void showError() {
    EasyLoading.showToast('网络异常，请稍后重试');
    controller.addError(StreamError(1, 'HTML中fetch发生错误'));
  }

  var streamCallback = allowInterop(read);
  var errorHandler = allowInterop(showError);
  try {
    fetchStream(url, jsonEncode(data), streamCallback, errorHandler);
  } catch (error) {
    debugPrint('fetchStream发生错误: ${error}');
    EasyLoading.showToast('网络异常，请稍后重试');
    controller.addError(StreamError(1, 'fetch过程中发生错误'));
  }
}
