// web/wx.dart
@JS('wx')
library wx;

import 'package:js/js.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/wxConfigModel.dart';

external void config(WxShareConfigResponse config);

external void ready(void Function() callback);

external void onMenuShareTimeline(WxCommonShareDataModel commonShareData);

external void updateTimelineShareData(WxCommonShareDataModel commonShareData);

external void onMenuShareAppMessage(WxCommonShareDataModel commonShareData);

external void updateAppMessageShareData(WxCommonShareDataModel commonShareData);

@anonymous
@JS()
class WxCommonShareDataModel {
  external factory WxCommonShareDataModel({
    String title,
    String desc,
    String link,
    String imgUrl,
    Function success,
  });
  String title;
  String desc;
  String link;
  String imgUrl;
  Function success;
}

// @anonymous
// @JS()
// class WxShareConfigResponse {
//   // 开启调试模式时,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
//   bool debug;
//   String appId;
//   String nonceStr;
//   String signature;
//   num timestamp;
//   String url;
//   List<String> jsApiList;

//   external factory WxShareConfigResponse({
//     bool debug,
//     String appId,
//     String nonceStr,
//     String signature,
//     num timestamp,
//     String url,
//     List<String> jsApiList,
//   });

//   WxShareConfigResponse.fromJson(Map<String, dynamic> json) {
//     debug = json['debug'];
//     appId = json['appId'];
//     nonceStr = json['nonceStr'];
//     signature = json['signature'];
//     timestamp = json['timestamp'];
//     url = json['url'];
//     jsApiList = json['jsApiList'];
//   }
// }
