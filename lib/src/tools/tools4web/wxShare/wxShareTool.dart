// ignore: avoid_web_libraries_in_flutter
import 'dart:html';

import 'package:js/js.dart';
import 'package:waimai_e_fe_flutter_finance/src/services/model/wxConfigModel.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/requests.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/tools4web/wxShare/wx.dart'
    as wx;

class WxShareTool {
  static void init(WxConfigModel config) {
    if (isWx()) {
      // await LoadScript('//res.wx.qq.com/open/js/jweixin-1.4.0.js');
      comPostApi(
        path: '/api/share/getWXSignature',
        params: {
          'url': window.location.href,
        },
      ).then((response) {
        // 配置
        WxShareConfigResponse shareConfig =
            WxShareConfigResponse.fromJson(response?.data ?? {});
        wx.config(WxShareConfigResponse(
            debug: false,
            appId: shareConfig?.appId,
            nonceStr: shareConfig?.nonceStr,
            signature: shareConfig?.signature,
            timestamp: shareConfig?.timestamp,
            url: shareConfig?.url,
            jsApiList: [
              'checkJsApi', //检查api
              'onMenuShareTimeline', //1.0分享到朋友圈
              'onMenuShareAppMessage', //1.0 分享到朋友
              'updateAppMessageShareData', //1.4 分享到朋友
              'updateTimelineShareData', //1.4分享到朋友圈
            ]));
        wx.ready(allowInterop(() {
          final String iconUrl = config.iconUrl;
          final String image =
              iconUrl.indexOf('http') == -1 ? ('http:$iconUrl') : iconUrl;
          final wx.WxCommonShareDataModel commonShareData =
              wx.WxCommonShareDataModel(
            title: config.title,
            desc: '',
            link: window.location.href,
            imgUrl: image,
            success: allowInterop(() {
              // ignore: avoid_print
              print('分享回调函数');
            }),
          );
          try {
            wx.onMenuShareTimeline(commonShareData);
            wx.updateTimelineShareData(commonShareData);
            wx.onMenuShareAppMessage(commonShareData);
            wx.updateAppMessageShareData(commonShareData);
          } catch (e) {}
        }));
      });
    }
  }

  static bool isWx() {
    final _ua = window.navigator.userAgent.toLowerCase();
    return _ua.indexOf('micromessenger') > -1;
  }
}
