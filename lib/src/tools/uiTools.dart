import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:waimai_e_fe_flutter_finance/waimai_e_fe_flutter_finance.dart';
import 'util/index.dart';

class UITools {
  // 是否为曲面屏，安卓也适用
  static bool isIponeX(BuildContext context) {
    return MediaQuery.of(context).padding.bottom > 0;
  }

  static double screenWidth = 0;
  static double screenHeight = 0;
  static double navHeight = 0;
  static double appBarHeight = 0;
  static double viewInsets = 0;
  static bool isPc = Util.getAppType() == 3;
  static bool getIsPc() {
    if (isPc == null) {
      isPc = Util.getAppType() == 3;
    }
    return isPc;
  }

  static getAppBarHeight() {
    if (appBarHeight <= 0) {
      double aH = kToolbarHeight ?? 0;
      double statusBarHeight =
          MediaQueryData.fromWindow(window).padding.top ?? 0;
      appBarHeight = statusBarHeight + aH;
    }
    return appBarHeight;
  }

  static getScreenWidth(BuildContext context) {
    if (screenWidth <= 0) {
      var w = MediaQuery.of(context).size.width;
      screenWidth = w ?? 0;
    }
    return screenWidth;
  }

  static getScreenHeight(BuildContext context) {
    if (UITools.screenHeight <= 0) {
      var h = MediaQuery.of(context).size.height;
      UITools.screenHeight = h ?? 0;
    }
    return UITools.screenHeight;
  }

  /// 边距
  static getViewInsets(BuildContext context) {
    if (UITools.viewInsets <= 0) {
      var insets = MediaQuery.of(context).viewInsets;
      UITools.viewInsets = insets ?? 0;
    }
    return UITools.viewInsets;
  }

  static num getBorderRadius(double radius) {
    return getIsPc() ? (radius / 2) : radius;
  }

  static Widget renderNavbar({
    @required context,
    title,
    List<Widget> actions,

    /// 点击左边 < 按钮响应的事件，默认是返回上一页
    Function onLeftIconTap,
    bottom,
    leading,
  }) {
    return PlatformTool.isPC
        ? null
        : AppBar(
            elevation: 0.0,
            backgroundColor: Color(0xffffffff),
            systemOverlayStyle: SystemUiOverlayStyle.light,
            title: title is String
                ? Text(
                    title,
                    style: TextStyle(
                      color: Color(0xFF222222),
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  )
                : title,
            centerTitle: true,
            leading: leading != null
                ? leading
                : GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      if (onLeftIconTap != null) {
                        onLeftIconTap();
                      } else {
                        Navigator.pop(context);
                      }
                    },
                    child: Container(
                        width: 40,
                        padding: EdgeInsets.only(left: 12),
                        child: Row(
                          children: <Widget>[
                            Icon(
                              Icons.arrow_back_ios,
                              size: 16,
                              color: Colors.black,
                            ),
                          ],
                        )),
                  ),
            actions: actions,
            bottom: bottom,
          );
  }
}
