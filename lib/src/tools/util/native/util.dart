import 'package:flutter/material.dart';
import 'package:waimai_e_fe_flutter_finance/src/tools/platformTool.dart';
import 'package:waimai_e_native_business/waimai_e_native_business.dart';

class Util {
  /// replaceState
  static void replaceState(String hash) {
    // print('native');
  }

  /// 是否支持webp
  static bool isSupportWebp() {
    return true;
  }

  /// 获取cookie
  static String getCookie(String key) {
    // print('native');
    return null;
  }

  static void setCookie(
    String cookieName,
    String cookieValue,
  ) {}

  /// 判断是否登录状态
  static bool isLogin() {
    return true;
  }

  /// 获取URL参数
  static String getUrlParam(String key) {
    // print('native');
    return null;
  }

  static double getConstrainHeight(BoxConstraints constraints) {
    return constraints.constrainHeight();
  }

  static String toDouble(int num) {
    return num > 9 ? '$num' : '0$num';
  }

  static String getTimeDesc(int _seconds) {
    if (_seconds == null || _seconds <= 0) {
      return '';
    }
    int seconds = _seconds;
    int hour = seconds ~/ 3600;
    seconds = seconds % 3600;
    int minute = seconds ~/ 60;
    seconds = seconds % 60;
    String hourStr = hour > 0 ? '$hour:' : '';
    return '$hourStr${Util.toDouble(minute)}:${Util.toDouble(seconds)}';
  }

  static String getOriginUrl() {
    return '';
  }

  static Map envMap = {
    'production': 'https://waimaie.meituan.com',
    'newtest': 'http://e.b.waimai.test.sankuai.com',
    'staging': 'https://e.waimai.st.sankuai.com',
  };

  static String getMainOrigin() {
    return envMap['production'];
  }

  static Map envMaps = {
    'newtest': 'http://paymp.pay.test.sankuai.com',
    'production': 'https://paymp.meituan.com',
    'staging': 'https://paymp.pay.st.sankuai.com',
  };
  static String getHost() {
    return envMaps['production'];
  }

  static bool isPcClient() {
    return false;
  }

  static String getUA() {
    return 'app';
  }

  static int getAppType() {
    return PlatformTool.isAndroid ? 4 : 5;
  }

  static void href(String url) {
    // print('native');
  }

  static Future<String> getPoiId() async {
    PoiInfo poiInfo = await WaimaiENativeBusiness.getPoiInfo();
    return Future.value(poiInfo?.poiId ?? "");
  }

  /// 是否是多店
  static Future<bool> isMultiple() async {
    PoiInfo poiInfo = await WaimaiENativeBusiness.getPoiInfo();
    return Future.value((poiInfo?.poiId ?? "") == '-1');
  }

  static Future<String> getAcctId() async {
    User userInfo = await WaimaiENativeBusiness.getUserInfo();
    return Future.value(userInfo?.acctId ?? "");
  }

  // goBack
  static void back(context) {
    Navigator.pop(context);
  }

  /// 路由回退2步，Navtive 不要使用这个方法
  static void back2Steps(context) {
    Navigator.pop(context);
  }

  /// 设置HTML的title
  static void setHtmlTitle(String title) {}
}
