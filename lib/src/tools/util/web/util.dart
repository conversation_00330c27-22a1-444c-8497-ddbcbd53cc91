// ignore: avoid_web_libraries_in_flutter
import 'dart:html' as html;

import 'package:flutter/material.dart';
import 'package:js/js_util.dart' as jsUtil;

class Util {
  /// replaceState
  static void replaceState(String hash) {
    html.window.history.replaceState(null, null, hash);
  }

  /// 是否支持webp
  static bool isSupportWebp() {
    var base64 = html.CanvasElement().toDataUrl('image/webp');
    return base64.contains('image/webp');
  }

  /// 获取cookie
  static String getCookie(String key) {
    String cookie = html.window.document.cookie;
    String name = '$key=';
    String value = '';
    if (cookie == null) {
      return value;
    }
    List<String> cookies = cookie.split(';');
    cookies.forEach((ck) {
      ck = ck.trim();
      if (ck.indexOf(name) == 0) {
        value = ck.substring(name.length);
      }
    });
    // print('key $key value $value');
    return value;
  }

  // 写session级别的cookie
  static void setCookie(
    String cookieName,
    String cookieValue,
  ) {
    if (cookieName.length > 0) {
      html.window.document.cookie =
          '$cookieName=${Uri.encodeComponent(cookieValue)};path=/;';
    }
  }

  /// 判断是否登录状态
  static bool isLogin() {
    String acctId = getUrlParam('acctId') ?? getCookie('acctId');
    String wmPoiId = getUrlParam('wmPoiId') ?? getCookie('wmPoiId');
    String token = getUrlParam('token') ?? getCookie('token');
    if (acctId == '' ||
        acctId == '0' ||
        wmPoiId == '' ||
        wmPoiId == '0' ||
        token == '' ||
        token == '0') {
      return false;
    }
    return true;
  }

  /// 获取URL参数
  static String getUrlParam(String key) {
    try {
      final uriParseSearch = Uri.parse(html.window.location.search);
      final uriParseHash = Uri.parse(html.window.location.hash);
      return uriParseSearch.queryParameters[key] ??
          uriParseHash.queryParameters[key];
    } catch (e) {}
    return null; // 返回参数值
  }

  /// dart map to js object
  static Object mapToJSObj(Map<dynamic, dynamic> a) {
    var object = jsUtil.newObject();
    a.forEach((k, v) {
      var key = k;
      var value = v;
      jsUtil.setProperty(object, key, value);
    });
    return object;
  }

  static double getConstrainHeight(BoxConstraints constraints) {
    return constraints.constrainHeight();
  }

  static String toDouble(int num) {
    return num > 9 ? '$num' : '0$num';
  }

  static String getTimeDesc(int _seconds) {
    if (_seconds == null || _seconds <= 0) {
      return '';
    }
    int seconds = _seconds;
    int hour = seconds ~/ 3600;
    seconds = seconds % 3600;
    int minute = seconds ~/ 60;
    seconds = seconds % 60;
    String hourStr = hour > 0 ? '$hour:' : '';
    return '$hourStr${Util.toDouble(minute)}:${Util.toDouble(seconds)}';
  }

  static String getOriginUrl() {
    String hostname = html.window.location.hostname;
    if (hostname.indexOf('localhost') > -1 ||
        hostname.indexOf('127.0.0.1') > -1 ||
        hostname.indexOf('0.0.0.0') > -1) {
      // 本地调试用测试环境地址
      return 'http://e.platform.proxy.b.waimai.test.sankuai.com/';
    } else {
      return html.window.location.origin;
    }
  }

  static Map envMap = {
    'production': 'https://waimaie.meituan.com',
    'newtest': 'http://e.b.waimai.test.sankuai.com',
    'staging': 'https://e.waimai.st.sankuai.com',
    'dev': 'http://e.b.waimai.test.sankuai.com',
  };

  static String getMainOrigin() {
    String env = jsUtil.getProperty(html.window, 'AWP_DEPLOY_ENV') ?? 'dev';
    return envMap[env];
  }

  static Map envMaps = {
    'newtest': 'http://paymp.pay.test.sankuai.com',
    'production': 'https://paymp.meituan.com',
    'staging': 'https://paymp.pay.st.sankuai.com',
    'dev': 'http://paymp.pay.test.sankuai.com',
  };
  static String getHost() {
    String env = jsUtil.getProperty(html.window, 'AWP_DEPLOY_ENV') ?? 'dev';
    return envMaps[env];
  }

  static String getUA() {
    debugPrint(
        'html.window.navigator.userAgent.toLowerCase() ${html.window.navigator.userAgent.toLowerCase()}');
    return html.window.navigator.userAgent.toLowerCase();
  }

  static bool isPcClient() {
    String _ua = html.window.navigator.userAgent.toLowerCase();
    return _ua.indexOf('waimai_e_pc_client') > -1 ||
        (_ua.indexOf('meituanwaimai') > -1 && _ua.indexOf('windows') > -1);
  }

  static int getAppType() {
    String _ua = html.window.navigator.userAgent.toLowerCase();
    if (_ua.indexOf('android') > -1 || _ua.indexOf('adr') > -1) {
      return 4;
    } else if (_ua.indexOf('iphone') > -1 || _ua.indexOf('ipad') > -1) {
      return 5;
    } else {
      return 3; // pc
    }
  }

  static void href(String url) {
    html.window.location.href = url;
  }

  static Future<String> getPoiId() {
    String poiId = Util.getUrlParam('wmPoiId') ?? Util.getCookie('wmPoiId');
    return Future.value(poiId ?? "");
  }

  /// 是否是多店
  static Future<bool> isMultiple() async {
    String poiId = Util.getUrlParam('wmPoiId') ?? Util.getCookie('wmPoiId');
    return Future.value((poiId ?? "") == '-1');
  }

  static Future<String> getAcctId() {
    String acctId = Util.getUrlParam('acctId') ?? Util.getCookie('acctId');
    return Future.value(acctId ?? "");
  }

  // goBack
  static void back(context) {
    html.window.history.back();
  }

  /// 路由回退2步
  static void back2Steps(context) {
    html.window.history.go(-2);
  }

  /// 设置HTML的title
  static void setHtmlTitle(String title) {
    html.document.title = title ?? '';
  }
}
