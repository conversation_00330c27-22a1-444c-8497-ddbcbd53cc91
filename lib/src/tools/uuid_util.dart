import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

/// UUID工具类，提供缓存UUID的方法
class UuidUtil {
  /// 单例实例
  static final UuidUtil _instance = UuidUtil._internal();

  /// 私有构造函数
  UuidUtil._internal();

  /// 工厂构造函数
  factory UuidUtil() {
    return _instance;
  }

  /// 缓存的UUID
  String _cachedUuid;

  /// 缓存时间
  DateTime _cacheTime;

  /// 本地存储的键名
  static const String _uuidKey = 'cached_uuid';
  static const String _timestampKey = 'cached_uuid_timestamp';

  /// 生成一个UUID，一小时内只生成一次，重复使用缓存值
  /// 即使应用重启也能保持一致
  Future<String> getUuidWithHourCache() async {
    // 先从内存中获取缓存
    if (_cachedUuid != null && _cacheTime != null) {
      final now = DateTime.now();
      if (now.difference(_cacheTime).inHours < 1) {
        return _cachedUuid;
      }
    }

    // 内存中没有有效缓存，尝试从本地存储获取
    final prefs = await SharedPreferences.getInstance();
    final storedUuid = prefs.getString(_uuidKey);
    final storedTimestamp = prefs.getInt(_timestampKey);

    final now = DateTime.now();

    // 检查本地存储中是否有有效的UUID
    if (storedUuid != null && storedTimestamp != null) {
      final storedTime = DateTime.fromMillisecondsSinceEpoch(storedTimestamp);
      if (now.difference(storedTime).inHours < 1) {
        // 本地存储的UUID仍然有效，更新内存缓存
        _cachedUuid = storedUuid;
        _cacheTime = storedTime;
        return _cachedUuid;
      }
    }

    // 无有效缓存，生成新的UUID并存储
    _cachedUuid = Uuid().v4();
    _cacheTime = now;

    // 保存到本地存储
    await prefs.setString(_uuidKey, _cachedUuid);
    await prefs.setInt(_timestampKey, _cacheTime.millisecondsSinceEpoch);

    return _cachedUuid;
  }

  /// 清除缓存的UUID（同时清除内存和本地存储）
  Future<void> clearCache() async {
    _cachedUuid = null;
    _cacheTime = null;

    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_uuidKey);
    await prefs.remove(_timestampKey);
  }
}
