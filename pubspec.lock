# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  archive:
    dependency: transitive
    description:
      name: archive
      url: "https://pub.sankuai.com"
    source: hosted
    version: "3.2.1"
  args:
    dependency: transitive
    description:
      name: args
      url: "https://pub.sankuai.com"
    source: hosted
    version: "2.3.0"
  asn1lib:
    dependency: transitive
    description:
      name: asn1lib
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.1.0"
  async:
    dependency: transitive
    description:
      name: async
      url: "https://pub.sankuai.com"
    source: hosted
    version: "2.8.2"
  cached_network_image:
    dependency: transitive
    description:
      name: cached_network_image
      url: "https://pub.sankuai.com"
    source: hosted
    version: "2.5.1"
  characters:
    dependency: transitive
    description:
      name: characters
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.2.0"
  charcode:
    dependency: transitive
    description:
      name: charcode
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.3.1"
  chewie:
    dependency: transitive
    description:
      name: chewie
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.10.1-mt.4"
  clock:
    dependency: transitive
    description:
      name: clock
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.1.0"
  collection:
    dependency: transitive
    description:
      name: collection
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.15.0"
  convert:
    dependency: transitive
    description:
      name: convert
      url: "https://pub.sankuai.com"
    source: hosted
    version: "3.0.1"
  crypto:
    dependency: transitive
    description:
      name: crypto
      url: "https://pub.sankuai.com"
    source: hosted
    version: "3.0.1"
  csslib:
    dependency: transitive
    description:
      name: csslib
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.16.2"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.0.4"
  dio:
    dependency: "direct main"
    description:
      name: dio
      url: "https://pub.sankuai.com"
    source: hosted
    version: "3.0.10"
  encrypt:
    dependency: transitive
    description:
      name: encrypt
      url: "https://pub.sankuai.com"
    source: hosted
    version: "5.0.1"
  ffi:
    dependency: transitive
    description:
      name: ffi
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.1.2"
  ffw_components_package:
    dependency: "direct main"
    description:
      name: ffw_components_package
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.0.6"
  file:
    dependency: transitive
    description:
      name: file
      url: "https://pub.sankuai.com"
    source: hosted
    version: "6.1.2"
  flap:
    dependency: "direct main"
    description:
      name: flap
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.16.459"
  flap_proxy:
    dependency: "direct main"
    description:
      name: flap_proxy
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.6.9"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_advanced_networkimage:
    dependency: transitive
    description:
      name: flutter_advanced_networkimage
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.7.2-mt.3"
  flutter_blurhash:
    dependency: transitive
    description:
      name: flutter_blurhash
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.5.0"
  flutter_cache_manager:
    dependency: transitive
    description:
      name: flutter_cache_manager
      url: "https://pub.sankuai.com"
    source: hosted
    version: "2.1.2"
  flutter_cat:
    dependency: transitive
    description:
      name: flutter_cat
      url: "https://pub.sankuai.com"
    source: hosted
    version: "2.2.0"
  flutter_custom_text_input_formatter:
    dependency: transitive
    description:
      name: flutter_custom_text_input_formatter
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.0.1"
  flutter_easyloading:
    dependency: "direct main"
    description:
      name: flutter_easyloading
      url: "https://pub.sankuai.com"
    source: hosted
    version: "2.2.2"
  flutter_easyrefresh:
    dependency: "direct main"
    description:
      name: flutter_easyrefresh
      url: "https://pub.sankuai.com"
    source: hosted
    version: "2.2.1"
  flutter_localizations:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_lx:
    dependency: "direct main"
    description:
      name: flutter_lx
      url: "https://pub.sankuai.com"
    source: hosted
    version: "2.6.1"
  flutter_lx_platform_interface:
    dependency: transitive
    description:
      name: flutter_lx_platform_interface
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.1.0"
  flutter_spinkit:
    dependency: transitive
    description:
      name: flutter_spinkit
      url: "https://pub.sankuai.com"
    source: hosted
    version: "4.1.2+1"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_widget_from_html_core:
    dependency: transitive
    description:
      name: flutter_widget_from_html_core
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.5.0+3"
  glob:
    dependency: transitive
    description:
      name: glob
      url: "https://pub.sankuai.com"
    source: hosted
    version: "2.0.1"
  html:
    dependency: transitive
    description:
      name: html
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.14.0+4"
  http:
    dependency: transitive
    description:
      name: http
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.12.2"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      url: "https://pub.sankuai.com"
    source: hosted
    version: "3.1.4"
  image:
    dependency: transitive
    description:
      name: image
      url: "https://pub.sankuai.com"
    source: hosted
    version: "3.1.3"
  intl:
    dependency: transitive
    description:
      name: intl
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.17.0"
  io:
    dependency: transitive
    description:
      name: io
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.0.3"
  js:
    dependency: "direct main"
    description:
      name: js
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.6.3"
  matcher:
    dependency: transitive
    description:
      name: matcher
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.12.11"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.1.3"
  meta:
    dependency: transitive
    description:
      name: meta
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.7.0"
  mt_flutter_knb:
    dependency: "direct main"
    description:
      name: mt_flutter_knb
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.2.5"
  mt_flutter_knb_platform_interface:
    dependency: transitive
    description:
      name: mt_flutter_knb_platform_interface
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.2.0"
  mt_flutter_route:
    dependency: "direct main"
    description:
      name: mt_flutter_route
      url: "https://pub.sankuai.com"
    source: hosted
    version: "4.6.19"
  mt_flutter_sniffer:
    dependency: transitive
    description:
      name: mt_flutter_sniffer
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.0.0"
  mt_flutter_web_utils:
    dependency: "direct main"
    description:
      name: mt_flutter_web_utils
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.0.23"
  mt_network:
    dependency: "direct main"
    description:
      name: mt_network
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.4.9"
  mtf_metrics:
    dependency: transitive
    description:
      name: mtf_metrics
      url: "https://pub.sankuai.com"
    source: hosted
    version: "2.3.0"
  mtf_statistics_route:
    dependency: "direct main"
    description:
      name: mtf_statistics_route
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.3.4"
  mtf_toast:
    dependency: transitive
    description:
      name: mtf_toast
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.0.3"
  nested:
    dependency: transitive
    description:
      name: nested
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.0.0"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.3.0"
  open_iconic_flutter:
    dependency: transitive
    description:
      name: open_iconic_flutter
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.3.0"
  path:
    dependency: transitive
    description:
      name: path
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.8.0"
  path_provider:
    dependency: transitive
    description:
      name: path_provider
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.6.28"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.0.1+2"
  path_provider_macos:
    dependency: transitive
    description:
      name: path_provider_macos
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.0.4+8"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.0.4"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.0.5"
  pedantic:
    dependency: transitive
    description:
      name: pedantic
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.11.1"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      url: "https://pub.sankuai.com"
    source: hosted
    version: "4.1.0"
  photo_view:
    dependency: transitive
    description:
      name: photo_view
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.10.3"
  platform:
    dependency: transitive
    description:
      name: platform
      url: "https://pub.sankuai.com"
    source: hosted
    version: "3.1.0"
  plugin_platform_interface:
    dependency: "direct overridden"
    description:
      name: plugin_platform_interface
      url: "https://pub.sankuai.com"
    source: hosted
    version: "2.1.3"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      url: "https://pub.sankuai.com"
    source: hosted
    version: "3.5.1"
  process:
    dependency: transitive
    description:
      name: process
      url: "https://pub.sankuai.com"
    source: hosted
    version: "4.2.3"
  process_run:
    dependency: "direct dev"
    description:
      name: process_run
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.11.0+2"
  provider:
    dependency: transitive
    description:
      name: provider
      url: "https://pub.sankuai.com"
    source: hosted
    version: "5.0.0"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      url: "https://pub.sankuai.com"
    source: hosted
    version: "2.1.0"
  quiver:
    dependency: transitive
    description:
      name: quiver
      url: "https://pub.sankuai.com"
    source: hosted
    version: "3.0.1+1"
  roo_flutter:
    dependency: "direct main"
    description:
      name: roo_flutter
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.5.71"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.25.0"
  shared_preferences:
    dependency: "direct main"
    description:
      name: shared_preferences
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.5.12+4"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.0.2+4"
  shared_preferences_macos:
    dependency: transitive
    description:
      name: shared_preferences_macos
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.0.1+11"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.0.4"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.1.2+7"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.0.2+3"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  source_span:
    dependency: transitive
    description:
      name: source_span
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.8.1"
  sqflite:
    dependency: transitive
    description:
      name: sqflite
      url: "https://pub.sankuai.com"
    source: hosted
    version: "2.0.2"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      url: "https://pub.sankuai.com"
    source: hosted
    version: "2.0.1+1"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.10.0"
  sticky_headers:
    dependency: "direct main"
    description:
      name: sticky_headers
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.2.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.1.0"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      url: "https://pub.sankuai.com"
    source: hosted
    version: "3.0.0"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.2.0"
  toast_widget_flutter:
    dependency: "direct main"
    description:
      name: toast_widget_flutter
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.1.12"
  tuple:
    dependency: transitive
    description:
      name: tuple
      url: "https://pub.sankuai.com"
    source: hosted
    version: "2.0.0"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.3.0"
  universal_html:
    dependency: "direct main"
    description:
      name: universal_html
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.2.4"
  universal_io:
    dependency: transitive
    description:
      name: universal_io
      url: "https://pub.sankuai.com"
    source: hosted
    version: "2.0.4"
  url_launcher:
    dependency: transitive
    description:
      name: url_launcher
      url: "https://pub.sankuai.com"
    source: hosted
    version: "5.7.10"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.0.1+4"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.0.1+9"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.0.9"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.1.5+3"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.0.1+3"
  uuid:
    dependency: "direct main"
    description:
      name: uuid
      url: "https://pub.sankuai.com"
    source: hosted
    version: "3.0.6"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      url: "https://pub.sankuai.com"
    source: hosted
    version: "2.1.1"
  video_player:
    dependency: transitive
    description:
      name: video_player
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.11.1-mt.5"
  video_player_platform_interface:
    dependency: transitive
    description:
      name: video_player_platform_interface
      url: "https://pub.sankuai.com"
    source: hosted
    version: "2.2.1-mt.1"
  video_player_web:
    dependency: transitive
    description:
      name: video_player_web
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.1.4+1"
  visibility_detector:
    dependency: transitive
    description:
      name: visibility_detector
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.2.2"
  waimai_e_base_ui:
    dependency: "direct main"
    description:
      name: waimai_e_base_ui
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.0.20"
  waimai_e_common_utils:
    dependency: "direct main"
    description:
      name: waimai_e_common_utils
      url: "https://pub.sankuai.com"
    source: hosted
    version: "6.41.0"
  waimai_e_fe_ffw_utils:
    dependency: "direct main"
    description:
      name: waimai_e_fe_ffw_utils
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.0.11"
  waimai_e_native_business:
    dependency: "direct main"
    description:
      name: waimai_e_native_business
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.0.55"
  webview_flutter:
    dependency: transitive
    description:
      name: webview_flutter
      url: "https://pub.sankuai.com"
    source: hosted
    version: "2.0.13+mt.2"
  wef_network:
    dependency: "direct main"
    description:
      name: wef_network
      url: "https://pub.sankuai.com"
    source: hosted
    version: "1.0.22"
  win32:
    dependency: transitive
    description:
      name: win32
      url: "https://pub.sankuai.com"
    source: hosted
    version: "2.0.5"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.1.2"
  xml:
    dependency: transitive
    description:
      name: xml
      url: "https://pub.sankuai.com"
    source: hosted
    version: "5.1.2"
  yaml:
    dependency: transitive
    description:
      name: yaml
      url: "https://pub.sankuai.com"
    source: hosted
    version: "3.1.0"
  zone_local:
    dependency: transitive
    description:
      name: zone_local
      url: "https://pub.sankuai.com"
    source: hosted
    version: "0.1.2"
sdks:
  dart: ">=2.14.0 <3.0.0"
  flutter: ">=2.0.0"
