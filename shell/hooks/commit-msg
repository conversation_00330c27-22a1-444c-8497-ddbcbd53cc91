#!/bin/sh

# 获取提交信息
# COMMIT_MSG="111111 fix: 111111111"
COMMIT_MSG=$(cat $1) 

# 获取当前分支名字
BRANCH_NAME=$(git symbolic-ref --short -q HEAD)
# BRANCH_NAME="MASTER"

echo "\033[32m 当前分支 $BRANCH_NAME \033[0m"
echo "\033[32m 原始提交信息 $COMMIT_MSG \033[0m"

# 合并分支，自动message(此情况忽略);
# git pull 自动merge(此情况忽略)
if [[ $COMMIT_MSG == "Merge "* ]]; then
	echo "Merge"
	exit 0
fi

# 判断提交信息是否含糊不清
COMMIT_MSG_LOWCASE=`tr '[A-Z]' '[a-z]' <<<"$COMMIT_MSG"` # 转小写
if [[ $COMMIT_MSG_LOWCASE =~ ^(fix|update|up|test|temp|更新|测试)$ ]]; then
	echo "\033[31m 提交信息过于含糊 \033[0m"
	exit 1
fi

# 长度判断
COMMIT_MSG_LEN=${#COMMIT_MSG}
if [ $COMMIT_MSG_LEN -le 10 ]; then
	echo "\033[31m 提交信息过于简短 \033[0m"
	exit 1
fi

branchCheck=false
typeCheck=false

# 分支名检测
if [[ $COMMIT_MSG =~ ^(feature\/|hotfix\/|bugfix\/release\/master|dev|develop|qa|stage|test|beta) ]]; then
	branchCheck=true
fi
# 类型检测 (第一行可能为onesId)
if [[ $COMMIT_MSG =~ ^[0-9]*[[:space:]]*(fix:|feat:|doc:|style:|merge:|bug:|test:|chore:|perf:|refactor:|ci:|revert:|breaking:) ]]; then
	typeCheck=true
fi

if [ $typeCheck == false ]; then
	echo "\033[31m 类型关键词错误，可选类型为：fix: feat: doc: style: merge: bug: test: chore: \033[0m"
	echo "\033[31m 示例：feat: 增加xxx功能 \033[0m"
	exit 1
fi
if [ $branchCheck == false ]; then
	COMMIT_MSG="${COMMIT_MSG} ${BRANCH_NAME}"
	echo "\033[32m 自动补全分支 ${COMMIT_MSG} \033[0m"
	echo "$COMMIT_MSG" > "$1"
fi
