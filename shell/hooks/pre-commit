#!/bin/sh

curCommand=$(ps -ocommand= -p $PPID)
if [[ $curCommand =~ (amend) ]]
then
    exit 0
fi
echo "\033[32m 正在进行代码提交检查... \033[0m"
analyzeRes=`flutter analyze`
echo "flutter analyze 检查输出: ${analyzeRes}"
if [[ $analyzeRes =~ (info|error|warning) ]] 
then 
    echo "\033[31m flutter analyze 检查未通过，请修复问题 \033[0m"
    exit 1
else 
    echo "\033[32m flutter analyze 检查通过 \033[0m"
fi


formatRes=`flutter format lib/`
echo "flutter format 输出: ${formatRes}"
if [[ $formatRes =~ (Formatted) ]] 
then 
    echo "\033[31m flutter format 格式化了相关文件，请重新 git add \033[0m"
    exit 1
else 
    echo "\033[32m flutter format 没有发生变化的文件 \033[0m"
fi
