<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="美团外卖商家对账">
  <!-- 灵犀 start-->
  <meta name="lx:category" content="waimai_e">
  <meta name="lx:appnm" content="waimai_e">
  <link rel="dns-prefetch" href="//analytics.meituan.net" />
  <!-- 灵犀 end-->
  <style>
    /* IOS 禁止调整浏览器字体大小*/
    body {
      -webkit-text-size-adjust: 100% !important;
      text-size-adjust: 100% !important;
      -moz-text-size-adjust: 100% !important;
    }

    flt-glass-pane {
      user-select: text;
      -webkit-user-select: text;
    }

    flt-glass-pane flt-scene-host {
      pointer-events: initial !important;
    }
  </style>
  <script src="/waimai_e_guard/H5guard.js"></script>
  <script>
    var hash = '__hash__';
    var assetBase = '__asset_base__';
  </script>
  <!-- Cat Start -->
  <script type="text/javascript">
    "use strict"; !function () { var e = 0 < arguments.length && void 0 !== arguments[0] ? arguments[0] : "_Owl_", a = window; a[e] || (a[e] = { isRunning: !1, isReady: !1, preTasks: [], dataSet: [], pageData: [], disableMutaObserver: !1, observer: null, use: function (e, t) { this.isReady && a.Owl && a.Owl[e](t), this.preTasks.push({ api: e, data: [t] }) }, add: function (e) { this.dataSet.push(e) }, run: function () { var t = this; if (!this.isRunning) { this.isRunning = !0; var e = a.onerror; a.onerror = function () { this.isReady || this.add({ type: "jsError", data: arguments }), e && e.apply(a, arguments) }.bind(this), (a.addEventListener || a.attachEvent)("error", function (e) { t.isReady || t.add({ type: "resError", data: [e] }) }, !0); var i = window.MutationObserver || window.WebKitMutationObserver || window.MozMutationObserver, r = window.performance || window.WebKitPerformance; if (i && r) { var n = -1, s = window.navigator.userAgent; if (-1 < s.indexOf("compatible") && -1 < s.indexOf("MSIE") ? (new RegExp("MSIE (\\d+\\.\\d+);").test(s), n = parseFloat(RegExp.$1)) : -1 < s.indexOf("Trident") && -1 < s.indexOf("rv:11.0") && (n = 11), -1 !== n && n <= 11) return void (this.disableMutaObserver = !0); try { this.observer = new i(function (e) { t.pageData.push({ mutations: e, startTime: r.now() }) }), this.observer.observe(document, { childList: !0, subtree: !0 }) } catch (e) { console.log("mutationObserver err") } } else this.disableMutaObserver = !0 } } }, a[e].run()) }();
  </script>
  <script>
    // auto-generate, dont edit!!!!!!
    var flutter_font_manifest = '[{"family":"MaterialIcons","fonts":[{"asset":"fonts/MaterialIcons-Regular.woff"}]},{"family":"packages/open_iconic_flutter/OpenIconic","fonts":[{"asset":"packages/open_iconic_flutter/assets/open-iconic.woff"}]}]';
    var flutter_deferered_library = {}
  </script>
  <!-- Cat End -->
  <!-- <link rel="apple-touch-icon" href="icons/Icon-192.png"> -->
  <title>财务对账</title>
  <!-- 灵犀 start-->
  <script type="text/javascript">
    !(function (win, doc, ns) {
      var cacheFunName = '_MeiTuanALogObject';
      win[cacheFunName] = ns;
      if (!win[ns]) {
        var _LX = function () {
          _LX.q.push(arguments);
          return _LX;
        };
        _LX.q = _LX.q || [];
        _LX.l = +new Date();
        win[ns] = _LX;
      }
    })(window, document, 'LXAnalytics');
  </script>
  <!-- 灵犀 end-->
</head>

<body>
  <div id="mtflutter-web-page-loading">
    <style>
      html,
      body {
        width: 100%;
        height: 100%;
      }

      #mtflutter-web-page-loading {
        width: 100%;
        height: 100%;
        position: fixed;
        top: 0;
        left: 0;
        z-index: 9999;
        background-color: white;
      }

      #mtflutter-web-page-loading img {
        width: 60px;
        height: 73px;
        position: absolute;
        top: 50%;
        left: 50%;
        margin-left: -30px;
        margin-top: -50px;
      }
    </style>
    <img src="https://s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/flutter-web/loading.gif">
  </div>
</body>
<script crossorigin="anonymous" src="//www.dpfile.com/app/owl/static/owl_latest.js"></script>
<script>
  if (typeof window.MemoryInfo == "undefined") {
    if (typeof window.performance.memory != "undefined") {
      window.MemoryInfo = function () { };
      window.MemoryInfo.prototype = window.performance.memory.__proto__;
    }
  }
</script>
<!-- 灵犀统计sdk -->
<script src="//lx.meituan.net/lx.js" type="text/javascript" charset="utf-8" async defer></script>
<script>
  (function () {
    var loadSingleScript = function (src, callback) {
      const s = document.createElement('script');
      s.async = false;
      s.src = src;
      s.addEventListener('load', function () {
        callback && callback();
      }, false);
      document.getElementsByTagName("head")[0].appendChild(s);
    }
    var ua = navigator.userAgent.toLowerCase();
    if (typeof Set === 'undefined' || typeof Map === 'undefined' || typeof Promise === 'undefined') {
      // polyfill
      loadSingleScript('//s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/cdn/common/polyfill/babel.js');
    }
    if (!document.body.attachShadow) {
      // shadow spec v1：attachShadow polyfill (release模式下 spec v0有问题)
      loadSingleScript('//s3plus.meituan.net/v1/mss_0e4451a82e1d428f9fc3b99c0b3ebf15/cdn/common/polyfill/shadow-dom-v1.js');
    }
  })()
</script>
<!-- 大文件拆分请求 -->
<script>
  !function (t) { var e = {}; function n(o) { if (e[o]) return e[o].exports; var r = e[o] = { i: o, l: !1, exports: {} }; return t[o].call(r.exports, r, r.exports, n), r.l = !0, r.exports } n.m = t, n.c = e, n.d = function (t, e, o) { n.o(t, e) || Object.defineProperty(t, e, { enumerable: !0, get: o }) }, n.r = function (t) { "undefined" != typeof Symbol && Symbol.toStringTag && Object.defineProperty(t, Symbol.toStringTag, { value: "Module" }), Object.defineProperty(t, "__esModule", { value: !0 }) }, n.t = function (t, e) { if (1 & e && (t = n(t)), 8 & e) return t; if (4 & e && "object" == typeof t && t && t.__esModule) return t; var o = Object.create(null); if (n.r(o), Object.defineProperty(o, "default", { enumerable: !0, value: t }), 2 & e && "string" != typeof t) for (var r in t) n.d(o, r, function (e) { return t[e] }.bind(null, r)); return o }, n.n = function (t) { var e = t && t.__esModule ? function () { return t.default } : function () { return t }; return n.d(e, "a", e), e }, n.o = function (t, e) { return Object.prototype.hasOwnProperty.call(t, e) }, n.p = "/", n(n.s = 2) }([function (t, e, n) { "use strict"; n.d(e, "b", (function () { return a })), n.d(e, "c", (function () { return i })), n.d(e, "a", (function () { return o })), n.d(e, "d", (function () { return c })); var o, r = new (n(1).a)({ timeout: 3e4 }), u = window; function a() { var t = location.hostname; return /(localhost)|(\d+\.){3}(\d+)$|(dev\.sankuai\.com)$/.test(t) || null == u.projectName && null == u.AWP_DEPLOY_ENV ? "local" : "online" } function i() { if ("local" !== a()) { for (var t = u.assetBase || "/", e = u.hash, n = 0, o = r.create(), i = [], c = 0; c < 6; c++)i.push("" + t + e + "/" + c + ".js"); e && function t(e) { var r = []; e.forEach((function (t) { r.push(o.get(t)) })), Promise.all(r).then((function (t) { n = 0; var e = []; t.forEach((function (t) { var n = t.response; e.push(n.data) })); var o = document.createElement("script"); o.textContent = e.join(""), document.body.appendChild(o), setTimeout((function () { var t; null === (t = document.getElementById("mtflutter-web-page-loading")) || void 0 === t || t.remove() }), 300) }), (function () { var o; if (++n > 5) { var r = document.createElement("a"); r.href = "javascript:location.reload()", r.style.textAlign = "center", r.style.margin = "50px auto", r.style.display = "block", r.style.color = "#f89800", r.innerText = "加载失败，点击重新请求页面", document.body.appendChild(r), null === (o = document.getElementById("mtflutter-web-page-loading")) || void 0 === o || o.remove() } else t(e) })) }(i) } else document.addEventListener("DOMContentLoaded", (function () { var t, e = document.createElement("script"); e.src = "/main.dart.js", document.body.appendChild(e), null === (t = document.getElementById("mtflutter-web-page-loading")) || void 0 === t || t.remove() })) } function c() { console.log("parallel_request_main_js version=1.0.2") } !function (t) { t.success = "success", t.fail = "fail" }(o || (o = {})) }, function (t, e, n) { "use strict"; n.d(e, "a", (function () { return o })); var o = function () { function t(t) { this.option = t } return t.prototype.create = function (t) { return t && (this.option = t), this }, t.prototype.get = function (t) { var e = new XMLHttpRequest; e.open("GET", t), e.timeout = this.option.timeout, e.send(); var n = Date.now(); return new Promise((function (t, o) { e.onreadystatechange = function () { if (e && 4 === e.readyState) { 200 !== e.status && o(new Error("Network " + e.status)); var r = { data: e.responseText, status: e.status, statusText: e.statusText, request: e }, u = Date.now(); t({ response: r, cost: u - n }) } }, e.onerror = function () { o(new Error("Network Error")) }, e.ontimeout = function () { o(new Error("Network Timeout")) } })) }, t }() }, function (t, e, n) { "use strict"; n.r(e); var o = n(0); try { Object(o.c)(), Object(o.d)() } catch (t) { } }]);
</script>
<!-- 异步加载拆分之后的js -->
<script>
  var getCookie = function (cname) {
    try {
      var name = cname + '=';
      var ca = document.cookie.split(';');
      for (var i = 0; i < ca.length; i++) {
        var c = ca[i].trim();
        if (c.indexOf(name) == 0) return c.substring(name.length, c.length);
      }
      return '';
    } catch (e) {
      return '';
    }
  };
  var fetchStream = function (url, data, callback, errorHandler) {
    var _data = '';
    var obj = JSON.parse(data);
    Object.keys(obj).forEach(function (key) {
      _data += `${key}=${obj[key]}&`;
    });

    const utf8Decoder = new TextDecoder("utf-8");

    fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: _data,
      credentials: 'same-origin',
    })
      .then((response) => {
        if (!response || !response.ok) {
          errorHandler();
          console.error(
            `fetchStream:${url} 请求失败: 状态码-${response.status} 错误信息-${response.statusText}`
          );
        }
        return response.body.getReader();
      })
      .then((reader) => {
        let buffer = ''; // 用于保存未完成的 JSON 数据片段
        let openBracesCount = 0; // 大括号计数器

        const processStream = () => {
          reader
            .read()
            .then(({ done, value }) => {
              if (done) {
                // 处理流结束时的剩余数据
                if (buffer.trim()) {
                  try {
                    console.log(buffer);
                    callback(buffer.trim()); // 调用回调函数处理最后的 JSON 数据
                  } catch (error) {
                    errorHandler(`尾部数据解析异常：${error}`);
                  }
                }
                return;
              }

              // 解码数据块并追加到缓冲区
              const chunk = utf8Decoder.decode(value, { stream: true });
              buffer += chunk;

              // 查找 JSON 数据
              for (let i = 0; i < buffer.length; i++) {
                if (buffer[i] === '{') {
                  openBracesCount++;
                } else if (buffer[i] === '}') {
                  openBracesCount--;

                  // 如果大括号计数器归零，说明一个完整的 JSON 字符串已到达
                  if (openBracesCount === 0) {
                    const jsonString = buffer.substring(0, i + 1);
                    buffer = buffer.substring(i + 1); // 移除已处理的部分
                    try {
                      console.log(jsonString);
                      callback(jsonString); // 调用回调函数处理 JSON 数据
                    } catch (error) {
                      errorHandler(`JSON解析失败：${error}`);
                    }

                    // 重置索引以处理剩余数据
                    i = -1;
                  }
                }
              }
              // 继续处理后续数据
              processStream();
            })
            .catch((error) => {
              errorHandler();
              console.error('readStream失败：', error);
            });
        };

        processStream();
      })
      .catch((error) => {
        errorHandler();
        console.error(`fetch错误：${error}`);
      });
  };
</script>
<script>
  window.Owl && window.Owl.start({
    project: 'com.sankuai.waimaieproxy.finance.ffw',
    pageUrl: location.origin + location.pathname + location.hash.split("?")[0],
    devMode: ['localhost', '127.0.0.1', 'http://e.platform.proxy.b.waimai.test.sankuai.com', 'https://proxy.waimai.st.sankuai.com'].some((item) =>
      location.href.indexOf(item) > -1),
    enableLogTrace: true,
    autoCatch: {
      fetch: true,
      console: true,
    },
    page: {
      sample: 1,
      fstPerfAnalysis: true,
      logSlowView: true,
    },
    error: {
      formatUnhandledRejection: true
    },
    SPA: {
      // 采集SPA应用routeFST
      autoPV: true,
      getFST: true,
    },
    resource: {
      enableStatusCheck: true,
      sampleApi: 1,
    },
    logan: {
      enable: true,
      version: '2.1.5',
      config: {
        devMode: ['localhost', '127.0.0.1', 'http://e.platform.proxy.b.waimai.test.sankuai.com', 'https://proxy.waimai.st.sankuai.com'].some((item) =>
          location.href.indexOf(item) > -1)
      },
    },
    ignoreList: {
      js: ['ResizeObserver loop limit exceeded'],
      ajax: [
        'https?://dreport.meituan.net',
        'https?://api.neixin.cn/dxlvs/open/v2/lgservers/json',
        'https?://lx2.meituan.net',
        'https?://report.meituan.com',
        'https?://ws.meituan.net',
        'https?://w.meituan.net',
        'https?://s3plus.meituan.net',
        'https?://image.meituan.net',
        'https?://p0.meituan.net',
        'https?://p1.meituan.net',
        'https?://img.meituan.net',
        'https?://lx1.meituan.net'
      ],
      resource: [
        'https?://waimai.sankuai.com',
        'https?://d.meituan.com',
        'https?://analytics.meituan.net',
      ],
      noScriptError: true,
    },
    onErrorPush: function (modal) {
      // 过滤非js错误导致的错误
      var _allErrorType = ['EvalError', 'RangeError', 'ReferenceError', 'SyntaxError', 'TypeError', 'URIError', 'AggregateError', 'Error']
      if (modal && modal.category === "jsError" && modal.sec_category.indexOf("unhandledrejection") > -1) {
        var _errorMatch = modal.content && modal.content.toString().match(/[a-zA-Z]+/)
        var _errorType = _errorMatch ? _errorMatch[0] : '';
        // 非js错误全部改成warn
        if (_allErrorType.indexOf(_errorType) < 0) {
          modal.level = 'warn'
        }
      }

      if (modal && modal.updateTags && getCookie) {
        modal.updateTags({
          cityid: getCookie('cityId'),
          acctId: getCookie('acctId'),
          wmPoiId: getCookie('wmPoiId'),
        });
      }
      return modal
    },
    onBatchPush: function (m) {
      // 过滤掉因为页面跳转导致接口被取消的报错
      if (m && m.logContent && m.logContent.indexOf('from: xhr') > -1) return false;
      return true;
    }
  })
</script>
<script src="//s0.meituan.net/bs/knb/v1.6.5/knb.js"></script>

</html>